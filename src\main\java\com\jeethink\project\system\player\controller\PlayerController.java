package com.jeethink.project.system.player.controller;

import com.alibaba.fastjson.JSON;
import com.jeethink.framework.aspectj.lang.annotation.Log;
import com.jeethink.framework.aspectj.lang.enums.BusinessType;
import com.jeethink.framework.web.controller.BaseController;
import com.jeethink.framework.web.domain.AjaxResult;
import com.jeethink.framework.web.page.PageDomain;
import com.jeethink.framework.web.page.TableDataInfo;
import com.jeethink.framework.web.page.TableSupport;
import com.jeethink.project.system.player.domain.UserAccount;
import com.jeethink.project.system.player.domain.Character;
import com.jeethink.project.system.player.service.IPlayerService;
import com.jeethink.project.system.user.domain.User;
import com.jeethink.project.system.user.service.IUserService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 玩家统计Controller
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@Controller
@RequestMapping("/system/player")
public class PlayerController extends BaseController {
    
    private String prefix = "system/player";
    
    @Autowired
    private IPlayerService playerService;
    
    @Autowired
    private IUserService userService;

    /**
     * 玩家统计页面
     */
    @RequiresPermissions("system:player:view")
    @GetMapping()
    public String player() {
        return prefix + "/player";
    }

    /**
     * 查询玩家列表
     */
    @RequiresPermissions("system:player:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(UserAccount userAccount) {
        // 获取当前登录用户，非admin只能看自己邀请码的玩家
        User currentUser = getSysUser();
        if (!currentUser.getLoginName().equals("admin")) {
            // 重新查询用户信息获取最新的invite字段
            currentUser = userService.selectUserByLoginName(currentUser.getLoginName());
            userAccount.setInvitation(currentUser.getInvite());
        }

        // 手动处理分页参数
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();

        // 如果没有分页参数，设置默认值
        if (pageNum == null || pageNum <= 0) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize <= 0) {
            pageSize = 20;
        }

        // 查询数据和总数
        List<UserAccount> list = playerService.selectPlayerListWithRegDate(userAccount, pageNum, pageSize);
        int total = playerService.countPlayerListWithRegDate(userAccount);

        // 手动构建分页结果
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(0);
        rspData.setRows(list);
        rspData.setTotal(total);
        return rspData;
    }

    /**
     * 获取玩家统计信息
     */
    @RequiresPermissions("system:player:stats")
    @PostMapping("/stats")
    @ResponseBody
    public String stats() {
        // 获取当前登录用户
        User currentUser = getSysUser();
        Map<String, Object> stats;
        
        if (currentUser.getLoginName().equals("admin")) {
            // admin用户查看全部统计
            stats = playerService.getPlayerStats();
        } else {
            // 普通用户只查看自己邀请码的统计
            currentUser = userService.selectUserByLoginName(currentUser.getLoginName());
            stats = playerService.getPlayerStatsByInvitation(currentUser.getInvite());
        }
        
        return JSON.toJSONString(stats);
    }

    /**
     * 根据邀请码查询玩家列表（admin专用）
     */
    @RequiresPermissions("system:player:listByInvitation")
    @PostMapping("/listByInvitation")
    @ResponseBody
    public TableDataInfo listByInvitation(@RequestParam String invitation) {
        // 只有admin用户可以查询指定邀请码的玩家
        User currentUser = getSysUser();
        if (!currentUser.getLoginName().equals("admin")) {
            return getDataTable(null);
        }

        startPage();
        List<UserAccount> list = playerService.selectPlayerListByInvitationWithRegDate(invitation);
        return getDataTable(list);
    }

    /**
     * 根据邀请码获取统计信息（admin专用）
     */
    @RequiresPermissions("system:player:statsByInvitation")
    @PostMapping("/statsByInvitation")
    @ResponseBody
    public String statsByInvitation(@RequestParam String invitation) {
        // 只有admin用户可以查询指定邀请码的统计
        User currentUser = getSysUser();
        if (!currentUser.getLoginName().equals("admin")) {
            return JSON.toJSONString(AjaxResult.error("权限不足"));
        }

        Map<String, Object> stats = playerService.getPlayerStatsByInvitation(invitation);
        return JSON.toJSONString(stats);
    }

    /**
     * 获取所有邀请码列表（admin专用）
     */
    @RequiresPermissions("system:player:invitations")
    @PostMapping("/invitations")
    @ResponseBody
    public AjaxResult getInvitations() {
        // 只有admin用户可以查询所有邀请码
        User currentUser = getSysUser();
        if (!currentUser.getLoginName().equals("admin")) {
            return AjaxResult.error("权限不足");
        }

        List<String> invitations = playerService.selectAllInvitations();
        return AjaxResult.success(invitations);
    }

    /**
     * 根据账户名查询玩家详情
     */
    @RequiresPermissions("system:player:detail")
    @GetMapping("/detail/{account}")
    @ResponseBody
    public AjaxResult detail(@PathVariable("account") String account) {
        UserAccount player = playerService.selectPlayerByAccount(account);
        
        // 权限检查：非admin用户只能查看自己邀请码的玩家
        User currentUser = getSysUser();
        if (!currentUser.getLoginName().equals("admin")) {
            currentUser = userService.selectUserByLoginName(currentUser.getLoginName());
            if (player == null || !currentUser.getInvite().equalsIgnoreCase(player.getInvitation())) {
                return AjaxResult.error("权限不足或玩家不存在");
            }
        }
        
        return AjaxResult.success(player);
    }

    /**
     * 实时获取在线玩家数量
     */
    @RequiresPermissions("system:player:onlineCount")
    @PostMapping("/onlineCount")
    @ResponseBody
    public AjaxResult getOnlineCount() {
        User currentUser = getSysUser();
        int onlineCount;
        
        if (currentUser.getLoginName().equals("admin")) {
            onlineCount = playerService.countOnlinePlayers();
        } else {
            currentUser = userService.selectUserByLoginName(currentUser.getLoginName());
            onlineCount = playerService.countOnlinePlayersByInvitation(currentUser.getInvite());
        }
        
        return AjaxResult.success("在线玩家数量", onlineCount);
    }

    /**
     * 实时获取今日注册数量
     */
    @RequiresPermissions("system:player:todayRegCount")
    @PostMapping("/todayRegCount")
    @ResponseBody
    public AjaxResult getTodayRegCount() {
        User currentUser = getSysUser();
        int todayRegCount;
        
        if (currentUser.getLoginName().equals("admin")) {
            todayRegCount = playerService.countTodayRegistrations();
        } else {
            currentUser = userService.selectUserByLoginName(currentUser.getLoginName());
            todayRegCount = playerService.countTodayRegistrationsByInvitation(currentUser.getInvite());
        }
        
        return AjaxResult.success("今日注册数量", todayRegCount);
    }

    /**
     * 根据账户名查询角色列表
     */
    @RequiresPermissions("system:player:detail")  // 暂时使用已有的权限
    @GetMapping("/characters/{account}")
    @ResponseBody
    public AjaxResult getCharacters(@PathVariable("account") String account) {
        System.out.println("=== 角色查询接口被调用 ===");
        System.out.println("请求账户: " + account);

        try {
            // 权限检查：非admin用户只能查看自己邀请码的玩家角色
            User currentUser = getSysUser();
            System.out.println("当前用户: " + currentUser.getLoginName());

            if (!currentUser.getLoginName().equals("admin")) {
                // 先检查该账户是否属于当前用户的邀请码
                UserAccount player = playerService.selectPlayerByAccount(account);
                if (player == null) {
                    System.out.println("玩家不存在: " + account);
                    return AjaxResult.error("玩家不存在");
                }

                currentUser = userService.selectUserByLoginName(currentUser.getLoginName());
                if (!currentUser.getInvite().equalsIgnoreCase(player.getInvitation())) {
                    System.out.println("权限不足，用户邀请码: " + currentUser.getInvite() + ", 玩家邀请码: " + player.getInvitation());
                    return AjaxResult.error("权限不足");
                }
            }

            System.out.println("开始查询角色信息...");
            List<Character> characters = playerService.selectCharactersByAccount(account);
            System.out.println("查询到角色数量: " + characters.size());

            return AjaxResult.success(characters);

        } catch (Exception e) {
            System.err.println("角色查询异常: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("查询角色信息失败: " + e.getMessage());
        }
    }

    /**
     * 查询游戏世界在线玩家列表
     */
    @RequiresPermissions("system:player:list")
    @PostMapping("/onlinePlayers")
    @ResponseBody
    public AjaxResult getOnlinePlayers(@RequestBody List<String> accountNames) {
        System.out.println("=== 查询游戏世界在线玩家列表 ===");
        System.out.println("传入的账户名列表: " + accountNames);

        try {
            // 权限检查：非admin用户只能查看自己邀请码的玩家
            User currentUser = getSysUser();
            if (!currentUser.getLoginName().equals("admin")) {
                // 验证传入的账户名是否都属于当前用户的邀请码
                currentUser = userService.selectUserByLoginName(currentUser.getLoginName());
                String userInvite = currentUser.getInvite();

                for (String accountName : accountNames) {
                    UserAccount player = playerService.selectPlayerByAccount(accountName);
                    if (player == null || !userInvite.equalsIgnoreCase(player.getInvitation())) {
                        System.out.println("权限不足，账户: " + accountName + " 不属于用户邀请码: " + userInvite);
                        return AjaxResult.error("权限不足，无法查看部分玩家信息");
                    }
                }
            }

            // 查询在线角色信息
            List<Character> onlineCharacters = playerService.selectOnlineCharactersByAccounts(accountNames);
            System.out.println("查询到在线角色数量: " + onlineCharacters.size());

            return AjaxResult.success(onlineCharacters);

        } catch (Exception e) {
            System.err.println("查询在线玩家异常: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("查询在线玩家失败: " + e.getMessage());
        }
    }

    /**
     * 测试接口 - 验证路由是否工作
     */
    @GetMapping("/test")
    @ResponseBody
    public AjaxResult test() {
        return AjaxResult.success("PlayerController路由工作正常");
    }
}
