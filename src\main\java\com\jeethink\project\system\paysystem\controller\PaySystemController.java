package com.jeethink.project.system.paysystem.controller;

import java.util.HashMap;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.jeethink.project.system.user.service.UserServiceImpl;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.jeethink.framework.aspectj.lang.annotation.Log;
import com.jeethink.framework.aspectj.lang.enums.BusinessType;
import com.jeethink.project.system.paysystem.domain.PaySystem;
import com.jeethink.project.system.paysystem.service.IPaySystemService;
import com.jeethink.framework.web.controller.BaseController;
import com.jeethink.framework.web.domain.AjaxResult;
import com.jeethink.common.utils.poi.ExcelUtil;
import com.jeethink.framework.web.page.TableDataInfo;
import com.jeethink.project.system.user.domain.User;
import com.jeethink.project.system.user.service.IUserService;
import org.springframework.ui.ModelMap;
/**
 * 代理分成Controller
 * 
 * <AUTHOR>
 * @date 2023-11-11
 */
@Controller
@RequestMapping("/system/paysystem")
public class PaySystemController extends BaseController
{
    private String prefix = "system/paysystem";
    @Autowired
    private IPaySystemService paySystemService;
    @Autowired
    private IUserService userService;

    @RequiresPermissions("system:paysystem:view")
    @GetMapping()
    public String paysystem(ModelMap mmap)
    {
        // 将当前用户信息传递到模板
        User user = getSysUser();
        mmap.put("user", user);
        return prefix + "/paysystem";
    }

    /**
     * 查询代理分成列表
     */
    @RequiresPermissions("system:paysystem:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(PaySystem paySystem)
    {
        /**获取当前登录用户，非admin只能看自己代理的利润分成*/
        User u=getSysUser();
        if(!u.getLoginName().equals("admin")){
            u=userService.selectUserByLoginName(u.getLoginName());
            paySystem.setAgency(u.getInvite());
        };

        startPage();
        List<PaySystem> list = paySystemService.selectPaySystemList(paySystem);
        return getDataTable(list);
    }
    /**
     * 查询代理分成列表成功总额
     */
    @RequiresPermissions("system:paysystem:list2")
    @PostMapping("/list2")
    @ResponseBody
    public String list2(PaySystem paySystem)
    {
       /**获取当前登录用户，非admin只能看自己代理的利润分成*/
        User u=getSysUser();
        if(!u.getLoginName().equals("admin")){
            u=userService.selectUserByLoginName(u.getLoginName());
            paySystem.setAgency(u.getInvite());
        }
        if(paySystem.getChannelCode()!=null){
            if(paySystem.getChannelCode().equals("微信")){
                paySystem.setChannelCode("WECHAT");
            }else if(paySystem.getChannelCode().equals("支付宝")){
                paySystem.setChannelCode("ALIPAY");
            }
        }

        paySystem.setStatus("支付成功");
        Integer  psum = paySystemService.selectPaySum(paySystem);
        if(psum==null){//没有当月数据
            psum=0;
        }
        paySystem.setAmount(psum.longValue());
        return JSON.toJSONString(paySystem);

    }
    /**
     * 查询代理分成图表数据
     */
    @RequiresPermissions("system:paysystem:list3")
    @PostMapping("/list3")
    @ResponseBody
    public String list3(PaySystem paySystem)
    {
        /**获取当前登录用户，非admin只能看自己代理的利润分成*/
        User u=getSysUser();
        if(!u.getLoginName().equals("admin")){
            u=userService.selectUserByLoginName(u.getLoginName());
            paySystem.setAgency(u.getInvite());
        }

        paySystem.setStatus("支付成功");
        List<PaySystem> list = paySystemService.selectPayTb(paySystem);
        //paySystem.setAmount(psum.longValue());
        System.out.println("json字符串："+JSON.toJSONString(list));
        return JSON.toJSONString(list);

    }
    /**
     * 查询本月代理分成金额
     */
    @RequiresPermissions("system:paysystem:list4")
    @PostMapping("/list4")
    @ResponseBody
    public String list4(PaySystem paySystem)
    {
        /**获取当前登录用户，非admin只能看自己代理的利润分成*/
        User u=getSysUser();
        if(!u.getLoginName().equals("admin")){
            u=userService.selectUserByLoginName(u.getLoginName());
            paySystem.setAgency(u.getInvite());
        }
        paySystem.setStatus("支付成功");
        List<PaySystem> list = paySystemService.selectPayAm(paySystem);
        System.out.println("json字符串："+JSON.toJSONString(list));
        return JSON.toJSONString(list);

    }
    /**
     * 导出代理分成列表
     */
    @RequiresPermissions("system:paysystem:export")
    @Log(title = "代理分成", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(PaySystem paySystem)
    {
        List<PaySystem> list = paySystemService.selectPaySystemList(paySystem);
        ExcelUtil<PaySystem> util = new ExcelUtil<PaySystem>(PaySystem.class);
        return util.exportExcel(list, "paysystem");
    }

    /**
     * 新增代理分成
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存代理分成
     */
    @RequiresPermissions("system:paysystem:add")
    @Log(title = "代理分成", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(PaySystem paySystem)
    {
        return toAjax(paySystemService.insertPaySystem(paySystem));
    }

    /**
     * 修改代理分成
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        PaySystem paySystem = paySystemService.selectPaySystemById(id);
        mmap.put("paySystem", paySystem);
        System.out.println(paySystem.getId()+"----0000000000");
        return prefix + "/edit";
    }

    /**
     * 修改保存代理分成
     */
    @RequiresPermissions("system:paysystem:edit")
    @Log(title = "代理分成", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(PaySystem paySystem)
    {
        return toAjax(paySystemService.updatePaySystem(paySystem));
    }

    /**
     * 删除代理分成
     */
    @RequiresPermissions("system:paysystem:remove")
    @Log(title = "代理分成", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(paySystemService.deletePaySystemByIds(ids));
    }
}
