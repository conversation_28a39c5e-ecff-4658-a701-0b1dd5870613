<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('代理分成列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>区号：</label>
                                <input type="text"  name="area" id="area"/>
                            </li>
                            <li>
                                <label>支付渠道：</label>
                                <input type="text" name="channelCode" id="channelCode"/>
                            </li>
                            <li>
                                <label>支付金额：</label>
                                <input type="text" name="amount" id="amount"/>
                            </li>
                            <li>
                                <label>角色名：</label>
                                <input type="text" name="goodsParamExt" id="goodsParamExt" />
                            </li>
                            <li>
                                <label>ip：</label>
                                <input type="text" name="ip" id="ip"/>
                            </li>
                            <li>
                                <label>支付状态：</label>
                                <select name="status" id="status" th:with="type=${@dict.getType('sys_succe_lose')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>发货状态：</label>
                                <input type="text" name="deliverGoods" id="deliverGoods"/>
                            </li>
                            <li>
                                <label>代理：</label>
                                <input type="text" name="agency" id="agency"/>
                            </li>
                            <li class="select-time">
                                <label>创建时间：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateTime]"/>
                            </li>
                            <li>
                                <label>平台订单号：</label>
                                <input type="text" name="wudiOrderNo" id="wudiOrderNo"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search();retse();"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:paysystem:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:paysystem:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:paysystem:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:paysystem:export">
                    <i class="fa fa-download"></i> 导出
                </a>
                <span>&nbsp;&nbsp;&nbsp;&nbsp;</span>
                <span style="font-size: 20px;">支付成功总额：</span><span id="zsid" style="font-size: 20px;"></span>
                <!-- 仅非admin用户显示提成总额 -->
                <span th:unless="${user.admin}">&nbsp;&nbsp;&nbsp;&nbsp;</span>
                <span th:unless="${user.admin}" style="font-size: 20px;">提成总额：</span>
                <span th:unless="${user.admin}" id="tcid" style="font-size: 20px;"></span>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:paysystem:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:paysystem:remove')}]];
        var channelCodeDatas = [[${@dict.getType('sys_wx_zfb')}]];
        var statusDatas = [[${@dict.getType('sys_succe_lose')}]];
        var deliverGoodsDatas = [[${@dict.getType('sys_fhzt')}]];
        var prefix = ctx + "system/paysystem";
        var ZS = 0;
        var isAdmin = [[${user.admin}]] || false;
        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "代理分成",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'id',
                    visible: false
                },
                {
                    field: 'area',
                    title: '区号'
                },
                {
                    field: 'channelCode',
                    title: '支付渠道',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(channelCodeDatas, value);
                    }
                },
                {
                    field: 'amount',
                    title: '支付金额'
                },
                {
                    field: 'goodsParamExt',
                    title: '角色名'
                },
                {
                    field: 'ip',
                    title: 'ip'
                },
                {
                    field: 'status',
                    title: '支付状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                {
                    field: 'deliverGoods',
                    title: '发货状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(deliverGoodsDatas, value);
                    }
                },
                {
                    field: 'agency',
                    title: '代理'
                },
                {
                    field: 'createTime',
                    title: '创建时间'
                },
                {
                    field: 'wudiOrderNo',
                    title: '平台订单号'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
            retse();

        });
        function retse(){

            var area =$("#area").val();
            var channelCode =$("#channelCode").val();
            var amount =$("#amount").val();
            var goodsParamExt =$("#goodsParamExt").val();
            var ip =$("#ip").val();
            var status =$("#status").val();
            var deliverGoods =$("#deliverGoods").val();
            var agency =$("#agency").val();
            var startTime =$("#startTime").val();
            var endTime =$("#endTime").val();
            var wudiOrderNo=$("#wudiOrderNo").val();
            var params = {};
            params["beginCreateTime"] = startTime;
            params["endCreateTime"] = endTime;
           // alert(agency+","+channelCode+","+startTime+","+endTime);
            $.ajax({
                type:"post",
                url: prefix + "/list2",
                dataType:"json",
                data:{"id":"","area":area,"channelCode":channelCode,"amount":amount,"goodsParamExt":goodsParamExt,"ip":ip,"status":status,"deliverGoods":deliverGoods,"agency":agency,
                    "wudiOrderNo":wudiOrderNo,"params":params},
                success:function (res) {
                    ZS=res.amount;
                    document.getElementById("zsid").innerHTML=ZS;
                    // 仅非admin用户计算和显示提成总额（20%）
                    if (!isAdmin) {
                        var tcAmount = (ZS * 0.2).toFixed(2);
                        document.getElementById("tcid").innerHTML=tcAmount;
                    }
                    //var a = JSON.parse(res.rows);
                    //alert(a.length);

                }

            });
        };
    </script>
</body>
</html>