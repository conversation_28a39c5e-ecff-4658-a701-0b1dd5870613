package com.jeethink;

import com.alibaba.druid.pool.DruidDataSource;
import org.junit.Test;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

/**
 * SQL Server连接测试
 * 简化版本，不依赖Spring上下文
 */
public class SqlServerConnectionTest {

    private DataSource createSqlServerDataSource() {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setDriverClassName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        dataSource.setUrl("***********************************************************************************************");
        dataSource.setUsername("sa");
        dataSource.setPassword("bgroigmroAD147258JFDJGBHjhdhf");

        // 设置连接池参数
        dataSource.setInitialSize(2);
        dataSource.setMinIdle(2);
        dataSource.setMaxActive(10);
        dataSource.setMaxWait(60000);
        dataSource.setTimeBetweenEvictionRunsMillis(60000);
        dataSource.setMinEvictableIdleTimeMillis(300000);
        dataSource.setValidationQuery("SELECT 1");
        dataSource.setTestWhileIdle(true);
        dataSource.setTestOnBorrow(false);
        dataSource.setTestOnReturn(false);

        return dataSource;
    }

    @Test
    public void testSqlServerConnection() {
        DataSource sqlServerDataSource = createSqlServerDataSource();
        try (Connection connection = sqlServerDataSource.getConnection()) {
            System.out.println("SQL Server连接成功！");
            
            // 测试查询在线玩家数量
            String sql = "SELECT COUNT(*) as count FROM user_account WHERE (last_login > last_logout OR last_logout IS NULL) AND last_login IS NOT NULL";
            try (PreparedStatement ps = connection.prepareStatement(sql);
                 ResultSet rs = ps.executeQuery()) {
                
                if (rs.next()) {
                    int onlineCount = rs.getInt("count");
                    System.out.println("当前在线玩家数量: " + onlineCount);
                }
            }
            
            // 测试查询今日注册数量
            String sql2 = "SELECT COUNT(*) as count FROM ssn WHERE CAST(reg_date AS DATE) = CAST(GETDATE() AS DATE)";
            try (PreparedStatement ps = connection.prepareStatement(sql2);
                 ResultSet rs = ps.executeQuery()) {
                
                if (rs.next()) {
                    int todayRegCount = rs.getInt("count");
                    System.out.println("今日注册数量: " + todayRegCount);
                }
            }
            
        } catch (Exception e) {
            System.err.println("SQL Server连接失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
