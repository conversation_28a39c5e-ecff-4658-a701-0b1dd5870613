package com.jeethink.framework.config;

import com.alibaba.druid.pool.DruidDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * SQL Server数据源配置
 * 使用JdbcTemplate避免MyBatis配置冲突
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(prefix = "spring.datasource.druid.sqlserver", name = "enabled", havingValue = "true")
public class SqlServerDataSourceConfig {

    /**
     * 创建lin2db数据源
     */
    @Bean("lin2dbDataSource")
    public DataSource lin2dbDataSource() {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setDriverClassName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        dataSource.setUrl("***********************************************************************************************");
        dataSource.setUsername("sa");
        dataSource.setPassword("bgroigmroAD147258JFDJGBHjhdhf");

        // 设置连接池参数
        dataSource.setInitialSize(2);
        dataSource.setMinIdle(2);
        dataSource.setMaxActive(10);
        dataSource.setMaxWait(60000);
        dataSource.setTimeBetweenEvictionRunsMillis(60000);
        dataSource.setMinEvictableIdleTimeMillis(300000);
        dataSource.setValidationQuery("SELECT 1");
        dataSource.setTestWhileIdle(true);
        dataSource.setTestOnBorrow(false);
        dataSource.setTestOnReturn(false);
        return dataSource;
    }

    /**
     * 创建lin2world数据源（预留）
     */
    @Bean("lin2worldDataSource")
    public DataSource lin2worldDataSource() {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setDriverClassName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        dataSource.setUrl("**************************************************************************************************");
        dataSource.setUsername("sa");
        dataSource.setPassword("bgroigmroAD147258JFDJGBHjhdhf");

        // 设置连接池参数
        dataSource.setInitialSize(1);
        dataSource.setMinIdle(1);
        dataSource.setMaxActive(5);
        dataSource.setMaxWait(60000);
        dataSource.setTimeBetweenEvictionRunsMillis(60000);
        dataSource.setMinEvictableIdleTimeMillis(300000);
        dataSource.setValidationQuery("SELECT 1");
        dataSource.setTestWhileIdle(true);
        dataSource.setTestOnBorrow(false);
        dataSource.setTestOnReturn(false);
        return dataSource;
    }

    /**
     * 创建lin2site数据源（预留）
     */
    @Bean("lin2siteDataSource")
    public DataSource lin2siteDataSource() {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setDriverClassName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        dataSource.setUrl("*************************************************************************************************");
        dataSource.setUsername("sa");
        dataSource.setPassword("bgroigmroAD147258JFDJGBHjhdhf");

        // 设置连接池参数
        dataSource.setInitialSize(1);
        dataSource.setMinIdle(1);
        dataSource.setMaxActive(5);
        dataSource.setMaxWait(60000);
        dataSource.setTimeBetweenEvictionRunsMillis(60000);
        dataSource.setMinEvictableIdleTimeMillis(300000);
        dataSource.setValidationQuery("SELECT 1");
        dataSource.setTestWhileIdle(true);
        dataSource.setTestOnBorrow(false);
        dataSource.setTestOnReturn(false);
        return dataSource;
    }



    /**
     * 创建SQL Server的JdbcTemplate（lin2db数据库）
     */
    @Bean("sqlServerJdbcTemplate")
    public JdbcTemplate sqlServerJdbcTemplate(@Qualifier("lin2dbDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    /**
     * 创建lin2world数据库的JdbcTemplate
     */
    @Bean("lin2worldJdbcTemplate")
    public JdbcTemplate lin2worldJdbcTemplate(@Qualifier("lin2worldDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    /**
     * 创建SQL Server事务管理器
     */
    @Bean("sqlServerTransactionManager")
    public DataSourceTransactionManager sqlServerTransactionManager(@Qualifier("lin2dbDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
