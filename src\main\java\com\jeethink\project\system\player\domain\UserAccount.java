package com.jeethink.project.system.player.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jeethink.framework.aspectj.lang.annotation.Excel;
import com.jeethink.framework.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 用户账户对象 user_account
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
public class UserAccount extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    private Integer uid;

    /** 账户名 */
    @Excel(name = "账户名")
    private String account;

    /** 支付状态 */
    private Integer payStat;

    /** 登录标志 */
    private Integer loginFlag;

    /** 警告标志 */
    private Integer warnFlag;

    /** 封禁标志 */
    private Integer blockFlag;

    /** 封禁标志2 */
    private Integer blockFlag2;

    /** 封禁结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date blockEndDate;

    /** 最后登录时间 */
    @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLogin;

    /** 最后登出时间 */
    @Excel(name = "最后登出时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLogout;

    /** 订阅标志 */
    private Integer subscriptionFlag;

    /** 最后世界 */
    private Integer lastWorld;

    /** 最后游戏 */
    private Integer lastGame;

    /** 最后IP */
    @Excel(name = "最后IP")
    private String lastIp;

    /** 电话 */
    private String telephone;

    /** 邀请码 */
    @Excel(name = "邀请码")
    private String invitation;

    /** 在线状态（计算字段，不存储在数据库） */
    @Excel(name = "在线状态")
    private String onlineStatus;

    /** 注册时间（关联查询字段，不存储在数据库） */
    @Excel(name = "注册时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date regDate;

    public void setUid(Integer uid) {
        this.uid = uid;
    }

    public Integer getUid() {
        return uid;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getAccount() {
        return account;
    }

    public void setPayStat(Integer payStat) {
        this.payStat = payStat;
    }

    public Integer getPayStat() {
        return payStat;
    }

    public void setLoginFlag(Integer loginFlag) {
        this.loginFlag = loginFlag;
    }

    public Integer getLoginFlag() {
        return loginFlag;
    }

    public void setWarnFlag(Integer warnFlag) {
        this.warnFlag = warnFlag;
    }

    public Integer getWarnFlag() {
        return warnFlag;
    }

    public void setBlockFlag(Integer blockFlag) {
        this.blockFlag = blockFlag;
    }

    public Integer getBlockFlag() {
        return blockFlag;
    }

    public void setBlockFlag2(Integer blockFlag2) {
        this.blockFlag2 = blockFlag2;
    }

    public Integer getBlockFlag2() {
        return blockFlag2;
    }

    public void setBlockEndDate(Date blockEndDate) {
        this.blockEndDate = blockEndDate;
    }

    public Date getBlockEndDate() {
        return blockEndDate;
    }

    public void setLastLogin(Date lastLogin) {
        this.lastLogin = lastLogin;
    }

    public Date getLastLogin() {
        return lastLogin;
    }

    public void setLastLogout(Date lastLogout) {
        this.lastLogout = lastLogout;
    }

    public Date getLastLogout() {
        return lastLogout;
    }

    public void setSubscriptionFlag(Integer subscriptionFlag) {
        this.subscriptionFlag = subscriptionFlag;
    }

    public Integer getSubscriptionFlag() {
        return subscriptionFlag;
    }

    public void setLastWorld(Integer lastWorld) {
        this.lastWorld = lastWorld;
    }

    public Integer getLastWorld() {
        return lastWorld;
    }

    public void setLastGame(Integer lastGame) {
        this.lastGame = lastGame;
    }

    public Integer getLastGame() {
        return lastGame;
    }

    public void setLastIp(String lastIp) {
        this.lastIp = lastIp;
    }

    public String getLastIp() {
        return lastIp;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setInvitation(String invitation) {
        this.invitation = invitation;
    }

    public String getInvitation() {
        return invitation;
    }

    public void setOnlineStatus(String onlineStatus) {
        this.onlineStatus = onlineStatus;
    }

    public String getOnlineStatus() {
        return onlineStatus;
    }

    public void setRegDate(Date regDate) {
        this.regDate = regDate;
    }

    public Date getRegDate() {
        return regDate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("uid", getUid())
            .append("account", getAccount())
            .append("payStat", getPayStat())
            .append("loginFlag", getLoginFlag())
            .append("warnFlag", getWarnFlag())
            .append("blockFlag", getBlockFlag())
            .append("blockFlag2", getBlockFlag2())
            .append("blockEndDate", getBlockEndDate())
            .append("lastLogin", getLastLogin())
            .append("lastLogout", getLastLogout())
            .append("subscriptionFlag", getSubscriptionFlag())
            .append("lastWorld", getLastWorld())
            .append("lastGame", getLastGame())
            .append("lastIp", getLastIp())
            .append("telephone", getTelephone())
            .append("invitation", getInvitation())
            .append("onlineStatus", getOnlineStatus())
            .append("regDate", getRegDate())
            .toString();
    }
}
