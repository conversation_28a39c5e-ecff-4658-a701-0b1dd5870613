package com.jeethink;

import com.jeethink.project.system.player.dao.PlayerDao;
import com.jeethink.project.system.player.domain.Character;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * 角色查询功能测试
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CharacterQueryTest {

    @Autowired
    private PlayerDao playerDao;

    @Test
    public void testSelectCharactersByAccount() {
        String accountName = "tt0011";
        
        System.out.println("=== 测试角色查询功能 ===");
        System.out.println("查询账户: " + accountName);
        
        try {
            List<Character> characters = playerDao.selectCharactersByAccount(accountName);
            
            System.out.println("查询结果: 找到 " + characters.size() + " 个角色");
            
            for (Character character : characters) {
                System.out.println("角色信息:");
                System.out.println("  - 角色名: " + character.getCharName());
                System.out.println("  - 等级: " + character.getLevel());
                System.out.println("  - 在线状态: " + character.getOnlineStatus());
                System.out.println("  - 在线时长: " + character.getUseTimeFormatted());
                System.out.println("  - 最后登录: " + character.getLogin());
                System.out.println("  - 创建时间: " + character.getCreateDate());
                System.out.println("  ---");
            }
            
        } catch (Exception e) {
            System.err.println("查询失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
