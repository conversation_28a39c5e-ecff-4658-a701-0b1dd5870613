package com.jeethink.project.monitor.job.util;

import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;

import com.jeethink.project.monitor.job.domain.Job;

/**
 * 定时任务处理（禁止并发执行）
 * 
 * <AUTHOR>  官方网址：www.jeethink.vip
 *
 */
@DisallowConcurrentExecution
public class QuartzDisallowConcurrentExecution extends AbstractQuartzJob
{
    @Override
    protected void doExecute(JobExecutionContext context, Job job) throws Exception
    {
        JobInvokeUtil.invokeMethod(job);
    }
}
