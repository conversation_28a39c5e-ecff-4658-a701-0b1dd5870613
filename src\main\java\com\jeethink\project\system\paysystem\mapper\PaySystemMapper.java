package com.jeethink.project.system.paysystem.mapper;

import java.util.List;
import com.jeethink.project.system.paysystem.domain.PaySystem;

/**
 * 代理分成Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-11
 */
public interface PaySystemMapper 
{
    /**
     * 查询代理分成
     * 
     * @param id 代理分成ID
     * @return 代理分成
     */
    public PaySystem selectPaySystemById(Long id);

    /**
     * 查询代理分成成功总金额
     *
     * @param paySystem 代理分成
     * @return 总金额
     */
    public Integer selectPaySum(PaySystem paySystem);

    /**
     * 查询代理分成列表
     * 
     * @param paySystem 代理分成
     * @return 代理分成集合
     */
    public List<PaySystem> selectPaySystemList(PaySystem paySystem);

    /**
     * 查询代理分成图标
     *
     * @param paySystem 代理分成
     * @return 代理分成集合
     */
    public List<PaySystem> selectPayTb(PaySystem paySystem);
    /**
     * 查询本月代理分成金额
     *
     * @param paySystem 代理分成
     * @return 代理分成集合
     */
    public List<PaySystem> selectPayAm(PaySystem paySystem);
    /**
     * 新增代理分成
     * 
     * @param paySystem 代理分成
     * @return 结果
     */
    public int insertPaySystem(PaySystem paySystem);

    /**
     * 修改代理分成
     * 
     * @param paySystem 代理分成
     * @return 结果
     */
    public int updatePaySystem(PaySystem paySystem);

    /**
     * 删除代理分成
     * 
     * @param id 代理分成ID
     * @return 结果
     */
    public int deletePaySystemById(Long id);

    /**
     * 批量删除代理分成
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deletePaySystemByIds(String[] ids);
}
