-- 玩家统计功能问题诊断和解决方案

-- ========================================
-- 问题1：数据源问题诊断
-- ========================================

-- 检查当前数据库连接（应该是MySQL）
SELECT DATABASE() as current_database, VERSION() as mysql_version;

-- 检查是否存在user_account表（这个表应该在SQL Server中，不在MySQL中）
-- 如果这个查询返回错误，说明确实是数据源问题
-- SELECT COUNT(*) FROM user_account;  -- 这行会报错，因为表在SQL Server中

-- ========================================
-- 问题2：菜单权限问题诊断和解决
-- ========================================

-- 2.1 检查玩家统计菜单是否存在
SELECT '=== 检查玩家统计菜单 ===' as step;
SELECT menu_id, menu_name, parent_id, order_num, url, perms, menu_type, visible
FROM sys_menu 
WHERE menu_id BETWEEN 2060 AND 2069
ORDER BY menu_id;

-- 2.2 检查当前权限分配情况
SELECT '=== 当前权限分配情况 ===' as step;
SELECT r.role_name, m.menu_name, m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.menu_id BETWEEN 2060 AND 2069
ORDER BY r.role_name, m.order_num;

-- 2.3 检查所有角色
SELECT '=== 所有可用角色 ===' as step;
SELECT role_id, role_name, role_key, status
FROM sys_role 
WHERE status = '0'
ORDER BY role_id;

-- 2.4 检查用户和角色关联
SELECT '=== 用户角色关联 ===' as step;
SELECT u.login_name, u.user_name, u.Invite, r.role_name, r.role_key
FROM sys_user u
JOIN sys_user_role ur ON u.user_id = ur.user_id
JOIN sys_role r ON ur.role_id = r.role_id
WHERE u.status = '0' AND r.status = '0'
ORDER BY u.login_name;

-- ========================================
-- 解决方案：添加缺失的权限
-- ========================================

-- 3.1 删除可能存在的重复权限（避免主键冲突）
DELETE FROM sys_role_menu 
WHERE role_id = 2 AND menu_id BETWEEN 2060 AND 2069;

DELETE FROM sys_role_menu 
WHERE role_id = 100 AND menu_id BETWEEN 2060 AND 2069;

-- 3.2 为"主播"角色（role_id=2）添加基础权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES 
(2, 2060),  -- 玩家统计主菜单
(2, 2061),  -- 玩家查询
(2, 2062),  -- 玩家统计
(2, 2063),  -- 玩家详情
(2, 2064),  -- 在线统计
(2, 2065);  -- 注册统计

-- 3.3 为"技术"角色（role_id=100）添加全部权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES 
(100, 2060),  -- 玩家统计主菜单
(100, 2061),  -- 玩家查询
(100, 2062),  -- 玩家统计
(100, 2063),  -- 玩家详情
(100, 2064),  -- 在线统计
(100, 2065),  -- 注册统计
(100, 2066),  -- 邀请码查询
(100, 2067),  -- 邀请码统计
(100, 2068),  -- 邀请码列表
(100, 2069);  -- 玩家导出

-- ========================================
-- 验证解决结果
-- ========================================

-- 4.1 验证权限分配结果
SELECT '=== 权限分配完成后的结果 ===' as step;
SELECT r.role_name, m.menu_name, m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.menu_id BETWEEN 2060 AND 2069
ORDER BY r.role_name, m.order_num;

-- 4.2 统计每个角色的玩家统计权限数量
SELECT '=== 权限统计 ===' as step;
SELECT r.role_name, COUNT(*) as permission_count
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.menu_id BETWEEN 2060 AND 2069
GROUP BY r.role_name
ORDER BY r.role_name;

-- 4.3 检查特定用户的权限（以A001用户为例）
SELECT '=== A001用户权限检查 ===' as step;
SELECT u.login_name, u.Invite, r.role_name, m.menu_name, m.perms
FROM sys_user u
JOIN sys_user_role ur ON u.user_id = ur.user_id
JOIN sys_role r ON ur.role_id = r.role_id
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE u.login_name = 'A001' AND m.menu_id BETWEEN 2060 AND 2069
ORDER BY m.order_num;

-- ========================================
-- 预期结果说明
-- ========================================

/*
执行完成后，预期结果：

1. 超级管理员（admin）：拥有全部10个权限
2. 主播角色用户：拥有6个基础权限（查看、统计、详情等）
3. 技术角色用户：拥有全部10个权限

如果用户仍然看不到菜单，可能的原因：
1. 需要重新登录以刷新权限缓存
2. 应用需要重启以加载新的权限配置
3. 数据源配置问题导致SQL Server连接失败

数据源问题的解决：
1. 确保SQL Server服务运行正常
2. 检查网络连接和防火墙设置
3. 验证数据库连接字符串和凭据
4. 运行DatabaseConnectionTest.java进行连接测试
*/
