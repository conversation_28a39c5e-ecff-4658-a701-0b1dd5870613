package com.jeethink.common.utils.pay;

import org.apache.commons.codec.binary.Base64;

import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

public class RsaKeyTools {
    
    public static final String PEM_PUBLICKEY = "PUBLIC KEY";
    
    public static final String PEM_PRIVATEKEY = "PRIVATE KEY";
    
    /**
     * generateRSAKeyPair
     * 
     * @param keySize
     * @return
     */
    public static KeyPair generateRSAKeyPair(int keySize) {
        // 为RSA算法创建一个KeyPairGenerator对象
        KeyPairGenerator kpg;
        try {

            try {
                kpg = KeyPairGenerator.getInstance("RSA");
            } catch (NoSuchAlgorithmException e) {
                throw new IllegalArgumentException("No such algorithm-->");
            }

            // 初始化KeyPairGenerator对象,密钥长度
            kpg.initialize(keySize);
            // 生成密匙对
            KeyPair keyPair = kpg.generateKeyPair();
            return keyPair;
        }
        catch (Exception e) {
            e.printStackTrace();
        }

        return null;
        

    }
    

    
    public static byte[] sign(String data,String privateKey) throws Exception {
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKey));
        
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        
        PrivateKey privateKey2 = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        Signature signature = Signature.getInstance("SHA1WithRSA");
        signature.initSign(privateKey2);
        signature.update(data.getBytes());
        return signature.sign();
        
    }
    //后台测试签名的时候 要和前台保持一致，所以需要将结果转换
    private static String bytes2String(byte[] bytes) {
        StringBuilder string = new StringBuilder();
        for (byte b : bytes) {
            String hexString = Integer.toHexString(0x00FF & b);
            string.append(hexString.length() == 1 ? "0" + hexString : hexString);
        }
        return string.toString();
    }
    
    public static boolean verify(String data,
                                 String publicKey,
                                 byte[] signatureResult) {
        try {
            X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(Base64.decodeBase64(publicKey));
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey publicKey2 = keyFactory.generatePublic(x509EncodedKeySpec);
            
            Signature signature = Signature.getInstance("SHA1WithRSA");
            signature.initVerify(publicKey2);
            signature.update(data.getBytes());
            
            return signature.verify(signatureResult);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
    
    //前台的签名结果是将byte 中的一些 负数转换成了正数，
    //但是后台验证的方法需要的又必须是转换之前的
    public static byte[] hexStringToByteArray(String data) {
        int k = 0;
        byte[] results = new byte[data.length() / 2];
        for (int i = 0; i + 1 < data.length(); i += 2, k++) {
            results[k] = (byte) (Character.digit(data.charAt(i), 16) << 4);
            results[k] += (byte) (Character.digit(data.charAt(i + 1), 16));
        }
        return results;
    }
    
    public static void main(String[] args) {
        String str = "amount";
        String publicKey  = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgCQTMMX1odcWkDvkdsSf4iOhXe3PBIiKxTQ/FQgDyHFAcP08JI7tumYkK+v7vqh8tpoi0rj6/gOSq4+MArh+80fbJgOiMJIqX+Pv9rly0RKM2uHeeQjoSLOGioJx2UbM7yStAKffbIFg41FRVEJTHjAljec7R3bIMA4TRxqo+ogiZX/AvlCQcjfrvM1uP2LNvG5AwnsYfnr2jbV94zEl13jVGMABXTXuwL6utxXSg4a5fTkkYq619h/Xro6EquhYVHmJn9O7DLOSlnmKrX/5BeSXFSNliwzpyRkYWs2nrBNymXBnIPFh2u1kFADktnqrEPGSze4Qx/Y0Maqo6TlXQwIDAQAB";
        //私钥
        String privateKey ="MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCAJBMwxfWh1xaQO+R2xJ/iI6Fd7c8EiIrFND8VCAPIcUBw/Twkju26ZiQr6/u+qHy2miLSuPr+A5Krj4wCuH7zR9smA6Iwkipf4+/2uXLREoza4d55COhIs4aKgnHZRszvJK0Ap99sgWDjUVFUQlMeMCWN5ztHdsgwDhNHGqj6iCJlf8C+UJByN+u8zW4/Ys28bkDCexh+evaNtX3jMSXXeNUYwAFdNe7Avq63FdKDhrl9OSRirrX2H9eujoSq6FhUeYmf07sMs5KWeYqtf/kF5JcVI2WLDOnJGRhazaesE3KZcGcg8WHa7WQUAOS2eqsQ8ZLN7hDH9jQxqqjpOVdDAgMBAAECggEAcspeWqHr0mhRMTJGDtNDexx6ER/ZBPGghtqv7BI6+VPi65UfTIyxj/qySppi8cATwKzBp03IosbmnEPCpClMqHlla19z6tj1luMlcztcDMj2X5pZZsnA9GfGjcwfWwoeydhsFptL/fLErb6KAdZMiU2ORtmu8+0Hlmz1fgv29j64RK9Nn2HywOORbJo4Jbd9ADo2pl3i2lZipMwDjBLxlEAIn+QmVLFWJVzgF7GqEKCpZdeMsxoMA/1ltjS1mL92q2MI/etM/NoC2CbX9mj1GS2Wp1YH+5yOCjp4ngFbA3JykscDi45sjlmjslkT2BIAmu6uijm3iie5Sf9mAwwiAQKBgQDPQjgdwfL2zSrRZpFaspSk2C3M5Mt7uYMLZU97ShtaBBbc4Xt6BjVmsZVyKRUAynjcq8IezUrogSbbv0J4juv4X2pEaSOv+Tw1/n+X3wFVZEvQXpZD9debEmWjR+aSA1kfIuASkyrhg9hwoWp0tSG7bDxYWeSNvqjlLpl+IR6tAQKBgQCeRqiJ2nJ+u4T2cMhreafXSmduem0CYYSICMuORLmXPeFwuiGfOjB9JW8y2IQs+TuoBOs/kQavDRLKceaOyW7K0gh6vAabrY2KXFODLmvkJx7muUlk2Wz+ET6NomgRXEpxyhPjpAtnwbd+DNmEucVIWV/2FMkWLn7t2BLxGWIQQwKBgFFtXsayMFaufKgQeAL+LlAlbrkVGqzdUZLklrmFgAF5odHd9LkYP1q9Yk2bvli1M47fHEElvsxiEXY8Sk07OZI3SnTeKds1PGaqOk7cmpWF9hNDVh4VNEQEklH4KaosDpOhDA/AKhS3zA1UHZMEpuAPwhWkG5tk0M5Z5IwdTwQBAoGAWXpkqZpfhBoK7y+PfoMGSgX8N07q9Z+fxzABs+pSaZhILOxtOZEx/G3/UWVqQ+qauUAepo8ckjX3249M4ogqJGtc89X8fmI9VG0hoVXLFO6w34r8XRE2NuQU0wlvf/EG/RFa8SS6devkClTFW+2SBHbSc1II5ItgoqG3A6pv7/UCgYEAh9EwP7YGHG9qSf+O7+F2PLtXyEz0q1MYmsTJfUOu0X4BEdAqGIbQdsYUtc3hgk7fojJc1sA3osUOta6POciL65uNBBu7fKzRj3wSj5JuvP2E0+Br588iozEsCzBqUymaedo74/RpkIt7jn70zfcmviCdOT7n7lWOmdGGIvTyPZw=";


        try {
            byte[] signautreResult = sign(str, privateKey);
            String signatureStr = bytes2String(signautreResult);
            System.out.print("signatureStr   " + signatureStr);
            byte[] signatureResult2 = hexStringToByteArray(signatureStr);
            
            boolean b = verify(str,
                    publicKey,
                               signatureResult2);
            System.out.print("iii   " + b);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        
    }
    
}