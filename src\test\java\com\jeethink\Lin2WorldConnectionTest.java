package com.jeethink;

import com.alibaba.druid.pool.DruidDataSource;
import org.junit.Test;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

/**
 * lin2world数据库连接测试
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
public class Lin2WorldConnectionTest {

    private DataSource createLin2WorldDataSource() {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setDriverClassName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        dataSource.setUrl("**************************************************************************************************");
        dataSource.setUsername("sa");
        dataSource.setPassword("bgroigmroAD147258JFDJGBHjhdhf");

        // 设置连接池参数
        dataSource.setInitialSize(1);
        dataSource.setMinIdle(1);
        dataSource.setMaxActive(5);
        dataSource.setMaxWait(60000);
        dataSource.setTimeBetweenEvictionRunsMillis(60000);
        dataSource.setMinEvictableIdleTimeMillis(300000);
        dataSource.setValidationQuery("SELECT 1");
        dataSource.setTestWhileIdle(true);
        dataSource.setTestOnBorrow(false);
        dataSource.setTestOnReturn(false);

        return dataSource;
    }

    @Test
    public void testLin2WorldConnection() {
        DataSource lin2worldDataSource = createLin2WorldDataSource();
        try (Connection connection = lin2worldDataSource.getConnection()) {
            System.out.println("lin2world数据库连接成功！");
            
            // 测试查询角色数据
            String sql = "SELECT TOP 3 char_id, char_name, account_name, account_id, Lev, class, gender, race, " +
                        "       login, logout, use_time, create_date, " +
                        "       CASE " +
                        "           WHEN login > logout OR logout IS NULL THEN '在线' " +
                        "           ELSE '离线' " +
                        "       END as online_status, " +
                        "       CASE " +
                        "           WHEN use_time >= 3600 THEN CAST(use_time / 3600 AS VARCHAR) + '小时' + CAST((use_time % 3600) / 60 AS VARCHAR) + '分钟' " +
                        "           WHEN use_time >= 60 THEN CAST(use_time / 60 AS VARCHAR) + '分钟' + CAST(use_time % 60 AS VARCHAR) + '秒' " +
                        "           ELSE CAST(use_time AS VARCHAR) + '秒' " +
                        "       END as use_time_formatted " +
                        "FROM user_data " +
                        "WHERE account_name = ? " +
                        "ORDER BY " +
                        "    CASE " +
                        "        WHEN login > logout OR logout IS NULL THEN 1 " +
                        "        ELSE 2 " +
                        "    END, " +
                        "    login DESC";
            
            try (PreparedStatement ps = connection.prepareStatement(sql)) {
                ps.setString(1, "tt0011");
                
                try (ResultSet rs = ps.executeQuery()) {
                    System.out.println("查询账户 tt0011 的角色信息:");
                    
                    while (rs.next()) {
                        System.out.println("角色信息:");
                        System.out.println("  - 角色ID: " + rs.getInt("char_id"));
                        System.out.println("  - 角色名: " + rs.getString("char_name"));
                        System.out.println("  - 账户名: " + rs.getString("account_name"));
                        System.out.println("  - 等级: " + rs.getInt("Lev"));
                        System.out.println("  - 在线状态: " + rs.getString("online_status"));
                        System.out.println("  - 在线时长: " + rs.getString("use_time_formatted"));
                        System.out.println("  - 最后登录: " + rs.getTimestamp("login"));
                        System.out.println("  - 创建时间: " + rs.getTimestamp("create_date"));
                        System.out.println("  ---");
                    }
                }
            }
            
        } catch (Exception e) {
            System.err.println("lin2world数据库连接失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
