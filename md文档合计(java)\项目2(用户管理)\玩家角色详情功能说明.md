# 玩家角色详情功能说明

## 功能概述

在原有的玩家统计功能基础上，新增了玩家角色详情查看功能。当点击玩家列表中的"详情"按钮时，不仅可以查看玩家的基本账户信息，还可以查看该账户下所有角色的详细信息，包括在线状态和在线时长。

## 新增功能特性

### 1. 角色信息展示
- **角色名称**: 显示角色的游戏内名称
- **等级**: 显示角色当前等级
- **在线状态**: 实时显示角色是否在线（在线/离线）
- **在线时长**: 格式化显示角色累计在线时间（小时分钟秒）
- **最后登录时间**: 角色最后一次登录的时间
- **创建时间**: 角色创建的时间

### 2. 数据来源
- **数据库**: lin2world (游戏世界数据库)
- **数据表**: user_data
- **关联字段**: account_name（账号名称）与 char_name（角色名称）为一对多关系

### 3. 权限控制
- **admin用户**: 可以查看任意玩家的角色信息
- **普通用户**: 只能查看自己邀请码下玩家的角色信息

## 技术实现

### 1. 新增文件

#### 实体类
- `src/main/java/com/jeethink/project/system/player/domain/Character.java`
  - 角色实体类，映射user_data表的字段
  - 包含角色基本信息和计算字段（在线状态、格式化时长）

#### 数据访问层
- 在 `PlayerDao.java` 中新增：
  - `characterRowMapper`: 角色数据映射器
  - `selectCharactersByAccount()`: 根据账户名查询角色列表

#### 服务层
- 在 `IPlayerService.java` 和 `PlayerServiceImpl.java` 中新增：
  - `selectCharactersByAccount()`: 角色查询服务方法

#### 控制器层
- 在 `PlayerController.java` 中新增：
  - `getCharacters()`: 角色查询REST接口

### 2. 核心SQL查询

```sql
SELECT char_id, char_name, account_name, account_id, Lev, class, gender, race,
       login, logout, use_time, create_date,
       CASE 
           WHEN login > logout OR logout IS NULL THEN '在线' 
           ELSE '离线' 
       END as online_status,
       CASE 
           WHEN use_time >= 3600 THEN CAST(use_time / 3600 AS VARCHAR) + '小时' + CAST((use_time % 3600) / 60 AS VARCHAR) + '分钟'
           WHEN use_time >= 60 THEN CAST(use_time / 60 AS VARCHAR) + '分钟' + CAST(use_time % 60 AS VARCHAR) + '秒'
           ELSE CAST(use_time AS VARCHAR) + '秒'
       END as use_time_formatted
FROM user_data
WHERE account_name = ?
ORDER BY 
    CASE 
        WHEN login > logout OR logout IS NULL THEN 1 
        ELSE 2 
    END,
    login DESC
```

### 3. 前端界面改进

#### 详情弹窗优化
- 使用 `$.when()` 同时获取玩家基本信息和角色信息
- 分区域显示：账户信息 + 角色信息
- 角色信息以表格形式展示，包含状态标识
- 弹窗宽度调整为1000px以适应更多内容

#### 界面布局
```html
<!-- 账户信息区域 -->
<h4>账户信息</h4>
<div>基本账户信息...</div>

<!-- 角色信息区域 -->
<h4>角色信息 (X个角色)</h4>
<table>
  <thead>角色名称 | 等级 | 在线状态 | 在线时长 | 最后登录 | 创建时间</thead>
  <tbody>角色数据...</tbody>
</table>
```

## API接口

### 新增接口

#### 查询角色列表
- **接口地址**: `GET /system/player/characters/{account}`
- **接口描述**: 根据账户名查询该账户下所有角色信息
- **权限要求**: `system:player:characters`
- **路径参数**: `account` - 玩家账户名
- **返回数据**:
```json
{
  "code": 0,
  "msg": "查询成功",
  "data": [
    {
      "charId": 28,
      "charName": "鸟",
      "accountName": "tt0011",
      "level": 80,
      "onlineStatus": "离线",
      "useTimeFormatted": "23小时43分钟",
      "login": "2025-07-26 22:22:27",
      "createDate": "2025-07-21 12:09:08"
    }
  ]
}
```

## 权限配置

### 新增权限
需要在MySQL数据库中添加新的权限项：

```sql
-- 执行 sql/add_character_permission.sql 脚本
-- 该脚本会自动添加 system:player:characters 权限
```

### 权限说明
- **权限标识**: `system:player:characters`
- **权限名称**: 角色查询
- **权限类型**: 功能权限（F）
- **适用角色**: 所有拥有玩家统计权限的角色

## 使用说明

### 1. 部署步骤
1. 确保数据库连接配置正确（lin2world数据库）
2. 执行权限配置SQL脚本：`sql/add_character_permission.sql`
3. 重启应用程序
4. 登录系统，进入玩家统计页面

### 2. 操作流程
1. 在玩家统计页面中找到目标玩家
2. 点击该玩家行的"详情"按钮
3. 在弹出的详情窗口中查看：
   - 上半部分：玩家账户基本信息
   - 下半部分：该账户下所有角色信息表格

### 3. 功能特点
- **实时数据**: 角色在线状态和时长都是实时计算的
- **权限隔离**: 普通用户只能查看自己邀请码的玩家角色
- **友好展示**: 在线时长自动格式化为易读格式
- **状态标识**: 在线角色显示绿色标识，离线角色显示灰色标识

## 测试验证

### 单元测试
- 文件位置: `src/test/java/com/jeethink/CharacterQueryTest.java`
- 测试内容: 验证角色查询功能是否正常工作

### 手动测试
1. 选择一个有多个角色的账户（如：tt0011）
2. 点击详情按钮
3. 验证是否正确显示所有角色信息
4. 验证在线状态和时长格式化是否正确

## 注意事项

1. **数据库连接**: 确保lin2world数据库连接正常
2. **权限配置**: 必须先执行权限配置SQL才能正常使用
3. **性能考虑**: 角色查询会增加数据库查询次数，建议监控性能
4. **数据一致性**: 角色数据来自游戏服务器，可能存在延迟

## 后续扩展

### 可能的功能扩展
1. **角色装备信息**: 显示角色装备详情
2. **角色技能信息**: 显示角色技能等级
3. **角色统计图表**: 可视化显示角色数据
4. **批量操作**: 支持批量查看多个玩家的角色信息

---

**功能版本**: v1.0  
**开发日期**: 2025-07-26  
**开发者**: jeethink  
**状态**: 已完成，可用于生产环境
