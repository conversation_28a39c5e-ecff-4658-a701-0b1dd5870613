<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改发货信息表')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: select2-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-shipments-edit" th:object="${shipments}">
            <input name="sid" th:field="*{sid}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">角色名称：</label>
                <div class="col-sm-8">
                    <input name="sname" th:field="*{sname}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">物品名称：</label>
                <div class="col-sm-8">
                    <input name="sname" th:field="*{wpname}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">强化值：</label>
                <div class="col-sm-8">
                    <input name="qhValue" th:field="*{qhValue}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">发货数量：</label>
                <div class="col-sm-8">
                    <input name="snumber" th:field="*{snumber}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">发货原因：</label>
                <div class="col-sm-8">
                    <input name="sause" th:field="*{sause}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">发货时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="stime" th:value="${#dates.format(shipments.stime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>

        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: select2-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/shipments";
        $("#form-shipments-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()){
                var data = $("#form-shipments-edit").serializeArray();
                /*var obj=document.getElementById("goods");
                var index = obj.selectedIndex;
                var wpnames = obj.options[index].text;
                var wpname = wpnames.split("-")[1];
                var goodsIds = $.form.selectSelects("goods");
                data.push({"name": "wpid", "value": goodsIds});
                data.push({"name": "wpname", "value": wpname});*/
                $.operate.save(prefix + "/edit",data);
            }


        }

        $("input[name='stime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        })
        /*$(function() {
            $('#goods').select2({
                placeholder: "请选择物品",
                allowClear: true,
                maximumSelectionLength:1
            });
        })*/
    </script>
</body>
</html>