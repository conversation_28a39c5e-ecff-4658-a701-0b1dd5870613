# 项目2(用户管理) - 玩家统计功能接口文档

## 文档说明
本文档记录了玩家统计功能的所有前后端接口，使用JdbcTemplate技术实现，避免了MyBatis配置冲突问题。
方便在后续的编程过程中快速查阅和修改代码。

## 技术架构总览

### 核心技术栈
- **数据访问**: JdbcTemplate + 自定义RowMapper
- **数据源**: SQL Server (lin2db数据库)
- **权限控制**: 基于Shiro的角色权限控制
- **前端技术**: Thymeleaf + Bootstrap + jQuery

### 系统特性
- ✅ **大小写忽略**: 邀请码比较使用UPPER()函数
- ✅ **权限隔离**: admin用户查看全部，普通用户只看自己邀请码数据
- ✅ **实时统计**: 在线玩家数量、今日注册数量
- ✅ **配置独立**: 不影响现有MyBatis配置

## REST API接口详情

### 1. 页面访问接口

#### 1.1 玩家统计主页面
- **接口地址**: `GET /system/player`
- **接口描述**: 返回玩家统计页面HTML
- **权限要求**: `system:player:view`
- **返回类型**: HTML页面
- **使用场景**: 用户点击左侧菜单"玩家统计"时访问

### 2. 数据查询接口

#### 2.1 分页查询玩家列表
- **接口地址**: `POST /system/player/list`
- **接口描述**: 分页查询玩家列表，支持多条件搜索和权限控制
- **权限要求**: `system:player:list`
- **请求参数**:
  ```json
  {
    "account": "玩家账号（模糊匹配，可选）",
    "invitation": "邀请码（精确匹配，可选）",
    "lastIp": "IP地址（模糊匹配，可选）",
    "pageNum": 1,
    "pageSize": 10
  }
  ```
- **返回数据**:
  ```json
  {
    "total": 100,
    "rows": [
      {
        "uid": 1,
        "account": "player001",
        "onlineStatus": "在线",
        "invitation": "A008",
        "lastLogin": "2025-07-25 23:56:20",
        "lastLogout": "2025-07-25 23:56:14",
        "regDate": "2025-07-20 19:52:15",
        "lastIp": "*************"
      }
    ],
    "code": 0,
    "msg": "查询成功"
  }
  ```
- **权限逻辑**:
  - **admin用户**: 查看所有玩家数据
  - **普通用户**: 只能查看invitation字段匹配自己invite的玩家数据

#### 2.2 获取统计信息
- **接口地址**: `POST /system/player/stats`
- **接口描述**: 获取玩家统计信息（在线数量、今日注册数量）
- **权限要求**: `system:player:stats`
- **请求参数**: 无
- **返回数据**:
  ```json
  {
    "onlineCount": 11,
    "todayRegisterCount": 4,
    "totalPlayerCount": 150
  }
  ```
- **权限逻辑**:
  - **admin用户**: 返回全平台统计数据
  - **普通用户**: 返回自己邀请码的统计数据

#### 2.3 查询玩家详情
- **接口地址**: `GET /system/player/detail/{account}`
- **接口描述**: 根据账户名查询玩家详细信息
- **权限要求**: `system:player:detail`
- **路径参数**: `account` - 玩家账户名
- **返回数据**:
  ```json
  {
    "code": 0,
    "msg": "查询成功",
    "data": {
      "uid": 1,
      "account": "player001",
      "onlineStatus": "在线",
      "invitation": "A008",
      "lastLogin": "2025-07-25 23:56:20",
      "lastLogout": "2025-07-25 23:56:14",
      "regDate": "2025-07-20 19:52:15",
      "lastIp": "*************",
      "payState": 0,
      "loginFlag": 1
    }
  }
  ```
- **权限逻辑**:
  - **admin用户**: 可查看任意玩家详情
  - **普通用户**: 只能查看自己邀请码的玩家详情

### 3. 实时统计接口

#### 3.1 获取在线玩家数量
- **接口地址**: `POST /system/player/onlineCount`
- **接口描述**: 实时获取在线玩家数量
- **权限要求**: `system:player:onlineCount`
- **返回数据**:
  ```json
  {
    "code": 0,
    "msg": "在线玩家数量",
    "data": 11
  }
  ```

#### 3.2 获取今日注册数量
- **接口地址**: `POST /system/player/todayRegCount`
- **接口描述**: 实时获取今日注册账号数量
- **权限要求**: `system:player:todayRegCount`
- **返回数据**:
  ```json
  {
    "code": 0,
    "msg": "今日注册数量",
    "data": 4
  }
  ```

### 4. 管理员专用接口

#### 4.1 按邀请码查询玩家列表
- **接口地址**: `POST /system/player/listByInvitation`
- **接口描述**: 根据指定邀请码查询玩家列表（管理员功能）
- **权限要求**: `system:player:listByInvitation`
- **请求参数**:
  ```json
  {
    "invitation": "A008"
  }
  ```
- **返回数据**: 同玩家列表接口格式
- **权限说明**: 仅admin用户可使用

#### 4.2 按邀请码获取统计信息
- **接口地址**: `POST /system/player/statsByInvitation`
- **接口描述**: 获取指定邀请码的统计信息（管理员功能）
- **权限要求**: `system:player:statsByInvitation`
- **请求参数**:
  ```json
  {
    "invitation": "A008"
  }
  ```
- **返回数据**: 同统计信息接口格式
- **权限说明**: 仅admin用户可使用

#### 4.3 获取所有邀请码列表
- **接口地址**: `POST /system/player/invitations`
- **接口描述**: 获取系统中所有邀请码列表（管理员功能）
- **权限要求**: `system:player:invitations`
- **返回数据**:
  ```json
  {
    "code": 0,
    "msg": "查询成功",
    "data": ["A008", "A111", "A110", "B001"]
  }
  ```
- **权限说明**: 仅admin用户可使用

## 数据库设计

### SQL Server数据源
- **服务器**: 110.42.3.94:1433
- **数据库**: lin2db
- **连接方式**: JdbcTemplate

### 核心数据表

#### user_account表 (玩家账户信息)
| 字段名 | 类型 | 说明 | 业务用途 |
|--------|------|------|----------|
| `uid` | int | 用户ID | 主键标识 |
| `account` | varchar(14) | 玩家账号 | 唯一账户名 |
| `last_login` | datetime | 最后登录时间 | 在线状态判断 |
| `last_logout` | datetime | 最后登出时间 | 在线状态判断 |
| `invitation` | varchar(10) | 邀请码 | **权限控制核心字段** |
| `last_ip` | varchar(15) | 最后IP | 登录地址记录 |
| `pay_stat` | int | 支付状态 | 付费用户标识 |
| `login_flag` | int | 登录标志 | 账户状态 |

#### ssn表 (玩家注册信息)
| 字段名 | 类型 | 说明 | 业务用途 |
|--------|------|------|----------|
| `name` | varchar(15) | 玩家账号 | 关联user_account.account |
| `reg_date` | datetime | 注册时间 | 注册统计 |
| `email` | varchar | 邮箱 | 联系方式 |

### 关键业务逻辑

#### 在线状态判断
```sql
CASE
    WHEN ua.last_login > ua.last_logout OR ua.last_logout IS NULL THEN '在线'
    ELSE '离线'
END as onlineStatus
```

#### 权限控制逻辑
```sql
-- 普通用户权限过滤
WHERE UPPER(ua.invitation) = UPPER(#{currentUser.invite})

-- admin用户无限制
-- 无WHERE条件限制
```

#### 大小写忽略处理
```sql
-- 所有邀请码比较都使用UPPER函数
WHERE UPPER(ua.invitation) = UPPER(?)
```

## 前端集成

### 页面文件
- **模板路径**: `src/main/resources/templates/system/player/player.html`
- **访问URL**: `/system/player`

### 核心JavaScript函数
```javascript
// 刷新统计信息
function refreshStats() {
    $.ajax({
        type: "post",
        url: prefix + "/stats",
        success: function (res) {
            $("#onlineCount").html(res.onlineCount || 0);
            $("#todayRegCount").html(res.todayRegisterCount || 0);
        }
    });
}

// 查看玩家详情
function viewDetail(account) {
    $.ajax({
        type: "get",
        url: prefix + "/detail/" + account,
        success: function (res) {
            // 显示详情弹窗
        }
    });
}
```

### 自动刷新机制
- **刷新间隔**: 30秒
- **刷新内容**: 统计信息（在线数量、今日注册）
- **实现方式**: `setInterval(refreshStats, 30000)`

## 权限配置

### 菜单权限
需要在MySQL数据库中添加以下菜单项：
```sql
-- 主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon)
VALUES('玩家统计', [parent_id], '3', '/system/player', 'C', '0', 'system:player:view', 'fa fa-users');

-- 功能权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, perms, menu_type, visible)
VALUES('玩家查询', [menu_id], '1', 'system:player:list', 'F', '0');
-- ... 其他权限项
```

### 角色权限分配
- **admin角色**: 拥有所有`system:player:*`权限
- **普通角色**: 根据需要分配特定权限

## 技术实现细节

### JdbcTemplate配置
```java
@Bean("sqlServerJdbcTemplate")
public JdbcTemplate sqlServerJdbcTemplate(@Qualifier("lin2dbDataSource") DataSource dataSource) {
    return new JdbcTemplate(dataSource);
}
```

### 自定义RowMapper
```java
private final RowMapper<UserAccount> userAccountRowMapper = new RowMapper<UserAccount>() {
    @Override
    public UserAccount mapRow(ResultSet rs, int rowNum) throws SQLException {
        UserAccount userAccount = new UserAccount();
        userAccount.setAccount(rs.getString("account"));
        userAccount.setOnlineStatus(rs.getString("onlineStatus"));
        // ... 其他字段映射
        return userAccount;
    }
};
```

## 核心代码文件清单

### 后端代码文件
1. **配置层**:
   - `src/main/java/com/jeethink/framework/config/SqlServerDataSourceConfig.java` - 数据源配置

2. **数据访问层**:
   - `src/main/java/com/jeethink/project/system/player/dao/PlayerDao.java` - JdbcTemplate数据访问

3. **业务逻辑层**:
   - `src/main/java/com/jeethink/project/system/player/service/IPlayerService.java` - 服务接口
   - `src/main/java/com/jeethink/project/system/player/service/impl/PlayerServiceImpl.java` - 服务实现

4. **控制器层**:
   - `src/main/java/com/jeethink/project/system/player/controller/PlayerController.java` - REST接口

5. **实体类**:
   - `src/main/java/com/jeethink/project/system/player/domain/UserAccount.java` - 用户账户实体
   - `src/main/java/com/jeethink/project/system/player/domain/Ssn.java` - 注册信息实体

### 前端代码文件
1. **页面模板**:
   - `src/main/resources/templates/system/player/player.html` - 玩家统计页面

2. **配置文件**:
   - `sql/player_menu.sql` - 菜单权限配置脚本

### 测试文件
1. **单元测试**:
   - `src/test/java/com/jeethink/SqlServerConnectionTest.java` - 数据库连接测试

## 接口调用示例

### 前端JavaScript调用示例
```javascript
// 1. 获取玩家列表
$.table.init({
    url: ctx + "system/player/list",
    columns: [
        {field: 'account', title: '玩家账号'},
        {field: 'onlineStatus', title: '在线状态'},
        {field: 'invitation', title: '邀请码'}
    ]
});

// 2. 获取统计信息
$.ajax({
    type: "post",
    url: ctx + "system/player/stats",
    success: function (res) {
        $("#onlineCount").text(res.onlineCount);
        $("#todayRegCount").text(res.todayRegisterCount);
    }
});

// 3. 查看玩家详情
function viewDetail(account) {
    $.ajax({
        type: "get",
        url: ctx + "system/player/detail/" + account,
        success: function (res) {
            if (res.code === 0) {
                // 显示详情信息
                $.modal.open("玩家详情", formatPlayerDetail(res.data));
            }
        }
    });
}
```

## 性能优化建议

### 数据库索引
```sql
-- 建议创建的索引
CREATE INDEX IX_user_account_invitation ON user_account(invitation);
CREATE INDEX IX_user_account_login_times ON user_account(last_login, last_logout);
CREATE INDEX IX_ssn_name ON ssn(name);
CREATE INDEX IX_ssn_reg_date ON ssn(reg_date);
```

### 连接池配置
- **初始连接数**: 2
- **最大连接数**: 10
- **连接超时**: 60秒

## 常见问题和解决方案

### 1. 配置冲突问题
**问题**: MyBatis配置冲突导致启动失败
**解决**: 已改用JdbcTemplate，完全避免配置冲突

### 2. 权限控制问题
**问题**: 普通用户能看到其他用户的数据
**解决**: 在Service层强制覆盖查询条件，确保数据隔离

### 3. 大小写敏感问题
**问题**: 邀请码大小写不一致导致查询失败
**解决**: 所有查询都使用UPPER()函数忽略大小写

## 后续扩展计划

### 可扩展功能
1. **导出功能**: Excel导出玩家数据
2. **高级搜索**: 更多搜索条件
3. **数据可视化**: 图表展示统计信息
4. **实时推送**: WebSocket实时数据更新

### 多数据库支持
当前配置已预留lin2world、lin2site数据库支持，可通过修改数据源配置轻松扩展。

---

**文档版本**: v1.0
**最后更新**: 2025-07-26
**技术负责人**: jeethink
**文档状态**: 已完成，可用于生产环境
