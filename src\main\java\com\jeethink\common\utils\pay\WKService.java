package com.jeethink.common.utils.pay;


import com.alibaba.fastjson.JSON;

import java.util.HashMap;
import java.util.Map;

public class WKService {

	public static final String CHARSET = "UTF-8";
	public static final String MCH_ID = "1231";
	public static final String MD5_KEY = "9YEFWELTGN09LDFW2S4C08G2CRBBXP8U";
	public static final String PRI_KEY = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC6OeWM6iuf4zcMRP4+9LT13WCT\n" +
			"tF/wLiCvnWfpI0fJ5/WoKHRPKXxlTkRjkwPV86eSwtP73BKyqpjT863S6kVS0igxwLKx4Ro7v+SJ\n" +
			"85ikx/Z1NTrGLDfUtIFEgHBDdojPZyCtaIFB6omPiEldWiNsJT0RFzNYnBDpVzh2No/LoJQYcv60\n" +
			"/VCeTw26XrvwGlOZtnWcwJ2zuz/GZsEo1IXkWu7sbgARAnIhVvGSezVm7Jj9hcfp19FEcpiTpDix\n" +
			"mqJngVWqJxAnWLuUHKa3HRxJ0OUNp4432CMWIzw6w7B9mR2V64MIcg8J/rHga351EKzJtuXD0a1f\n" +
			"E3iiwwQ3CtszAgMBAAECggEBAJ4VYdCyRGAQ0H0A4FbvAeKoj0AzYTGQ5rxNQZU+FsXFu9qIpNtC\n" +
			"W0/gFdv1Q89Oa9UAwFTP56t1eBCMjeU6a6i3r9k0PD0V2mAYMFgOymgvOv8aNY9en/JXXFibMzGB\n" +
			"oPd3Et1BlmAvSaDnT2gIiYNXRTSJ3lN9ADokiVCIVp7CeY80UAsofGqmPmlm5iRBZLazYmIjGhG8\n" +
			"lbqQklYEfv+hPp3N4ot0eJYA7RAvJlvnpBE885RaMELsrkD9E+NdykO7JsefMusFp6YKQ21nBgXI\n" +
			"T/ALjZrJLVHgQP8X2abOjCsEkP8TMbW0qiYmziDwt7qtVMCXoht/1oVu163CahECgYEA46XZkzul\n" +
			"uBuIgOIoOGHbfbX/ch/6r0972KIRfl4fqSLmeR743le2Flu1NDiTdpMy07IdpjgobSUEiM5JH8Ac\n" +
			"SOl/xYGGc3xlvtNgRZ+qch3KMZk+9g24ltDt6jSZpZZqfQAJ/IJSBTKPhsrfj4+gtDl7KxuFNdym\n" +
			"t8DS6kXlYvsCgYEA0WtjoqZBfLGSF4F7F+omhCo4TdGqY8iLoEK+WQdQk0bosx5T/n4Vwq8sd8sJ\n" +
			"8k4DRsr9f/qq1lodPRDbbVDiykLIIzjInZ1bqT13goFXlRtYCQm380nGISLQOpBIEbElEeWb98VF\n" +
			"lp92pFY3rnTxcVNao/61yOIfzj6MOAPiMykCgYBCMw6MZEISwd72K84qZzqe2fgIxgfuRxzqqGaR\n" +
			"6ukmFEIVYs5YSgVnhw+wuesYBkI3kYpRte77nlItPl/BXepbwZsoYjwpBTnJPkq2tHGqccNEnR3U\n" +
			"C7VsFiG5g8ZSguVAqGw/A966KDPYrNJKOygugLCynnQkb6ADu40aF21XVQKBgCI4Txaj3i2XAo1O\n" +
			"viOBQnv0PjERIqudeJtebKY1mMgIHTwvp3/9cGbYowaMKfrrHoPx5YyFFGdkYES7PW/WeMieMv/2\n" +
			"ZmnC7dhwz9eGdR5LUo5fhSx/EJ4XhzkanHwvmyQahert1FoxgnyGDiE2wMFI4pXLqD7EHHhiJh19\n" +
			"HPhJAoGACwOFIHlThGeOf+2mOJFmDc4BB7QNc+5NCYAko8dZPUhWhzVupY24RIU6mUwRDepOJi5Z\n" +
			"AtkQ8B4tnjpU5tfldctExGrXm4MjrB9+AUZvPrwo+ziQkKUBppd2SeBmRDt2GCnfDdOTA8bNe1KZ\n" +
			"rgTuhZYcLYF4muAdhPks6qqmR6E=\n";
	public static final String PUB_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjbOjbJxnO8N+bol2yxoS8kKf8CyKK90f\n" +
			"nX6Z0K4Xqkjhyd/1o+y86xMPAPkWXXVVm+Ej4FHSXtV3jwNWg5QwtCb1z4+26OJ1GyrpHslRYXtb\n" +
			"a4/xSbVmn1xWhXwIzL2u3+CxRTuAbNPpbJQmj+oJIwV5r4tPdmDe2d3yZSGuGqtYqrsoEH7vAIL2\n" +
			"f39XuDlWO/Vmi/qh4FPYpaJfLaSwyFoVwSw402rD2wEUL0/9v9d5aGSZja+iOozso2hC+3x69qof\n" +
			"tV7kll6sJRKCzb4Si4zg3X4dI1f9yGp7Cv9GBKIfvyBrHp3Yi769XV4bFjg7LFwK+IZp4Tx+WLLZ\n" +
			"qdoHTwIDAQAB\n";
	public String wkPay() {
		Map<String, String> params = new HashMap<>();

			params.put("mchId", MCH_ID);
			params.put("channelCode", "WECHAT");
			params.put("serviceName", "com.zhuofutong.api.pay.qrcode");
			params.put("signType", "MD5");//MD5  RSA  两种方式
			params.put("returnUrlType", "0");
			params.put("amount", "0.01");
			params.put("ip", "*************");
			params.put("returnUrl", "http://*********:8081/Y0d4a");
			params.put("notifyUrl", "http://*******:8083/api/testPayNotipy");
			params.put("goodsParamExt", "{\"goodsName\":\"122344\",\"goodsDesc\":\"***********\"}");
			params.put("mchOrderNo", "HTZD20230317112332928396");

//		String sign = RSAUtils.sign(params, PRI_KEY, RSAUtils.CHARSET);
//		String sign = SignUtils.sign(params, MD5_KEY, RSAUtils.CHARSET);
//		params.put("sign", sign);
		//params.put("sign", "6391B20911098A291F63F005DAA8EF4A");//上面参数实际签名值

		
		String js = JSON.toJSONString(params);
		String str = HttpTookit.HttpPostWithJson("支付地址", js);
		return str;
	}

	/**
	 * {
	 * "wuDiOrderNo":"WKZF2820230317112334194500", 
	 * "mchId":"28", 
	 * "mchOrderNo":"HTZD20230317112332928396", 
	 * "paySuccTime":"20230317114457", 
	 * "sign":"728BBDE4965551ACAF4AEFB002378ECE",
	 * "mchMoney":"0.01", 
	 * "extends1":null,
	 * "status":"2"
	 * }
	 */

	public String callback(String wuDiOrderNo, String mchId, String mchOrderNo,
						   String paySuccTime,
									  String sign, String mchMoney, String status) {
		if ("2".equals(status)) {
			Map<String, String> map = new HashMap<>();
			map.put("mchId", mchId);
			map.put("mchOrderNo", mchOrderNo);
			map.put("wuDiOrderNo", wuDiOrderNo);
			map.put("status", status);
			map.put("paySuccTime", paySuccTime);
			map.put("sign", sign);
			map.put("mchMoney", mchMoney);

//			boolean verify =RSAUtils.verify(map, sign, "公钥", RSAUtils.CHARSET);
//			boolean verify = SignUtils.checkParam(map, MD5_KEY, CHARSET);
//			if (verify) {
//				return "SUCCESS";
//			}
		}

		return "FAIL";
	}
}
