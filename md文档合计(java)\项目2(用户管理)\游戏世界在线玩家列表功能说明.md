# 游戏世界在线玩家列表功能说明

## 🎯 功能概述

在玩家统计页面新增"游戏世界在线玩家列表"按钮，点击后可以查看当前在线玩家在游戏世界中的角色详细信息，包括游戏名、账号名、等级、在线时长等。

## 📋 功能特性

### 🎮 显示信息
- **游戏名**: 角色在游戏中的名称 (char_name)
- **账号名**: 玩家的账户名 (account_name)
- **玩家等级**: 角色当前等级 (Lev)
- **在线时长**: 累计游戏时间，自动格式化显示
- **最后登录**: 角色最后一次登录时间

### 🔐 权限控制
- **管理员用户**: 可以查看所有在线玩家的角色信息
- **普通用户**: 只能查看自己邀请码下在线玩家的角色信息

### 🎯 数据逻辑
1. 从当前玩家统计表格中获取在线玩家的账户名列表
2. 根据账户名列表查询游戏世界数据库中的在线角色
3. 筛选条件：`login > logout OR logout IS NULL`
4. 按最后登录时间降序排列

## 🏗️ 技术实现

### 后端架构

#### 1. 数据访问层 (PlayerDao)
```java
/**
 * 根据账户名列表查询在线角色信息
 */
public List<Character> selectOnlineCharactersByAccounts(List<String> accountNames) {
    // 构建IN子句查询
    // 筛选在线状态：login > logout OR logout IS NULL
    // 格式化在线时长显示
}
```

#### 2. 服务层 (PlayerService)
```java
/**
 * 根据账户名列表查询在线角色信息
 */
public List<Character> selectOnlineCharactersByAccounts(List<String> accountNames);
```

#### 3. 控制器层 (PlayerController)
```java
@PostMapping("/onlinePlayers")
@ResponseBody
public AjaxResult getOnlinePlayers(@RequestBody List<String> accountNames) {
    // 权限验证
    // 调用服务查询在线角色
    // 返回JSON格式数据
}
```

### 核心SQL查询
```sql
SELECT char_name, account_name, Lev, use_time, login, logout,
       CASE 
           WHEN use_time >= 3600 THEN 
               CAST(use_time / 3600 AS VARCHAR) + '小时' + 
               CAST((use_time % 3600) / 60 AS VARCHAR) + '分钟'
           WHEN use_time >= 60 THEN 
               CAST(use_time / 60 AS VARCHAR) + '分钟' + 
               CAST(use_time % 60 AS VARCHAR) + '秒'
           ELSE CAST(use_time AS VARCHAR) + '秒'
       END as use_time_formatted
FROM user_data 
WHERE account_name IN (?, ?, ?, ...) 
  AND (login > logout OR logout IS NULL) 
ORDER BY login DESC
```

### 前端实现

#### 1. 界面按钮
```html
<button type="button" class="btn btn-primary btn-sm" onclick="showOnlinePlayersList()">
    <i class="fa fa-list"></i> 游戏世界在线玩家列表
</button>
```

#### 2. JavaScript逻辑
```javascript
function showOnlinePlayersList() {
    // 1. 获取当前表格中在线玩家的账户名列表
    var onlineAccountNames = [];
    $('#bootstrap-table').bootstrapTable('getData').forEach(function(row) {
        if (row.onlineStatus === '在线') {
            onlineAccountNames.push(row.account);
        }
    });
    
    // 2. 发送POST请求到后端
    $.ajax({
        type: "POST",
        url: prefix + "/onlinePlayers",
        contentType: "application/json",
        data: JSON.stringify(onlineAccountNames),
        success: function (res) {
            // 3. 构建并显示模态框
            var content = buildOnlinePlayersContent(res.data);
            showOnlinePlayersModal("游戏世界在线玩家列表", content);
        }
    });
}
```

#### 3. 模态框显示
- 使用Bootstrap模态框
- 响应式表格布局
- 自动滚动支持
- 1200px宽度，适配大屏显示

## 🚀 使用方法

### 1. 进入玩家统计页面
```
系统菜单 → 玩家统计
```

### 2. 点击在线玩家列表按钮
在页面顶部统计信息区域，点击"游戏世界在线玩家列表"按钮

### 3. 查看在线角色信息
弹窗将显示所有在线玩家的角色详细信息表格

## 📊 数据说明

### 在线状态判断
```sql
在线条件 = (login > logout OR logout IS NULL)
```

### 在线时长格式化
- **超过1小时**: "X小时X分钟"
- **超过1分钟**: "X分钟X秒"  
- **不足1分钟**: "X秒"

### 数据来源
- **数据库**: lin2world (SQL Server)
- **数据表**: user_data
- **连接方式**: lin2worldJdbcTemplate

## 🔧 权限验证逻辑

### 管理员用户
```java
if (currentUser.getLoginName().equals("admin")) {
    // 可以查看所有在线玩家
}
```

### 普通用户
```java
// 验证每个账户名是否属于当前用户的邀请码
for (String accountName : accountNames) {
    UserAccount player = playerService.selectPlayerByAccount(accountName);
    if (!userInvite.equalsIgnoreCase(player.getInvitation())) {
        return AjaxResult.error("权限不足");
    }
}
```

## 🧪 测试验证

### 1. 单元测试
```java
@Test
public void testSelectOnlineCharactersByAccounts() {
    List<String> accountNames = Arrays.asList("cx19840312", "yl008");
    List<Character> characters = playerDao.selectOnlineCharactersByAccounts(accountNames);
    assertNotNull(characters);
}
```

### 2. 功能测试
访问测试页面：
```
http://localhost:8080/system/player/online_players_test.html
```

### 3. 集成测试
1. 登录系统，进入玩家统计页面
2. 确认有在线玩家显示
3. 点击"游戏世界在线玩家列表"按钮
4. 验证弹窗显示正确的角色信息

## 🎯 API接口

### 请求接口
- **URL**: `POST /system/player/onlinePlayers`
- **权限**: `system:player:list`
- **请求体**: `["account1", "account2", ...]`
- **响应格式**:
```json
{
  "code": 0,
  "msg": "查询成功",
  "data": [
    {
      "charName": "角色名",
      "accountName": "账户名",
      "level": 80,
      "useTime": 3600,
      "useTimeFormatted": "1小时0分钟",
      "login": "2025-07-27 18:45:33",
      "onlineStatus": "在线"
    }
  ]
}
```

## 💡 特色功能

### 1. 智能筛选
- 只查询当前页面显示的在线玩家
- 避免查询所有数据库数据，提高性能

### 2. 权限隔离
- 普通用户只能看到自己管辖范围内的玩家
- 确保数据安全和隐私保护

### 3. 实时数据
- 基于实时的login/logout时间判断在线状态
- 显示最新的角色等级和在线时长

### 4. 用户体验
- 一键查看，操作简单
- 响应式设计，适配不同屏幕
- 清晰的表格布局，信息一目了然

## 🔮 扩展可能

### 短期优化
- [ ] 添加角色职业信息显示
- [ ] 支持按等级、在线时长排序
- [ ] 添加导出功能

### 长期扩展
- [ ] 实时刷新在线状态
- [ ] 添加角色位置信息
- [ ] 集成聊天和消息功能

---

**功能版本**: v1.0  
**开发日期**: 2025-07-27  
**状态**: ✅ 开发完成，待测试  
**维护者**: jeethink
