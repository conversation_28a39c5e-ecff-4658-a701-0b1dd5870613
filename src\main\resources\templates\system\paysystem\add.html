<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增代理分成')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-paysystem-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label">订单号：</label>
                <div class="col-sm-8">
                    <input name="mchOrderNo" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">区号：</label>
                <div class="col-sm-8">
                    <input name="area" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">支付渠道：</label>
                <div class="col-sm-8">
                    <input name="channelCode" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">支付金额：</label>
                <div class="col-sm-8">
                    <input name="amount" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">角色名：</label>
                <div class="col-sm-8">
                    <input name="goodsParamExt" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">ip：</label>
                <div class="col-sm-8">
                    <input name="ip" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">支付状态：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('sys_succe_lose')}">
                        <input type="radio" th:id="${'status_' + dict.dictCode}" name="status" th:value="${dict.dictValue}" th:checked="${dict.default}">
                        <label th:for="${'status_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">支付时间：</label>
                <div class="col-sm-8">
                    <input name="paySuccTime" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">发货状态：</label>
                <div class="col-sm-8">
                    <input name="deliverGoods" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">代理：</label>
                <div class="col-sm-8">
                    <input name="agency" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">平台订单号：</label>
                <div class="col-sm-8">
                    <input name="wudiOrderNo" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />

    <script th:inline="javascript">
        var prefix = ctx + "system/paysystem"
        $("#form-paysystem-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-paysystem-add').serialize());
            }
        }
    </script>
</body>
</html>