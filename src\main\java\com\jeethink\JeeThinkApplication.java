package com.jeethink;

import org.springframework.boot.SpringApplication;
        import org.springframework.boot.autoconfigure.SpringBootApplication;
        import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 启动程序
 *
 * <AUTHOR>  官方网址：www.jeethink.vip
 */
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
public class JeeThinkApplication
{
    public static void main(String[] args)
    {
        // System.setProperty("sprin
        // g.devtools.restart.enabled", "false");
        SpringApplication.run(JeeThinkApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  代理系统启动成功   ლ(´ڡ`ლ)ﾞ  \n");
    }
}