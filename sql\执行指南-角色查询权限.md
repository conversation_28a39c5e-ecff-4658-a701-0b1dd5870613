# 角色查询权限添加 - 执行指南

## 📋 执行前检查清单

### 1. 数据库连接确认
- ✅ 确认已连接到 `jeethink` 数据库
- ✅ 确认有足够的权限执行 INSERT、SELECT 操作
- ✅ 确认 `sys_menu` 和 `sys_role_menu` 表存在

### 2. 前置条件检查
- ✅ 玩家统计主菜单已存在（menu_id: 2060）
- ✅ 相关角色权限表结构正常

## 🚀 执行步骤

### 第一步：执行SQL脚本

```bash
# 方法1：直接在MySQL命令行执行
mysql -u your_username -p jeethink < sql/add_character_permission.sql

# 方法2：在MySQL客户端中执行
mysql> USE jeethink;
mysql> SOURCE sql/add_character_permission.sql;

# 方法3：使用MCP工具执行（推荐）
# 复制整个SQL文件内容，在MCP中执行
```

### 第二步：观察执行输出

脚本会输出以下6个步骤的详细信息：

1. **第一步：检查前置条件**
   - 验证玩家统计主菜单是否存在
   - 检查角色查询权限是否已存在

2. **第二步：获取父菜单ID**
   - 显示找到的父菜单信息（应该是menu_id: 2060）

3. **第三步：添加角色查询权限**
   - 创建新的权限菜单项
   - 显示添加结果

4. **第四步：为所有角色分配权限**
   - 列出所有活跃角色
   - 为每个角色分配新权限

5. **第五步：验证最终结果**
   - 显示完整的权限结构
   - 显示角色分配情况

6. **第六步：执行总结**
   - 提供最终状态摘要
   - 提交事务

## 📊 预期执行结果

### 成功执行后应该看到：

```sql
-- 第一步输出
+----------------------------------+------------+
| check_result                     | menu_count |
+----------------------------------+------------+
| ✓ 玩家统计主菜单存在             |          1 |
+----------------------------------+------------+

-- 第三步输出
+----------------------------------+-------------+
| add_result                       | new_menu_id |
+----------------------------------+-------------+
| ✓ 角色查询权限添加成功           |        2070 |
+----------------------------------+-------------+

-- 第四步输出
+---------+--------------+----------+--------+
| role_id | role_name    | role_key | status |
+---------+--------------+----------+--------+
|       1 | 超级管理员   | admin    | 0      |
|       2 | 主播         | anchor   | 0      |
|     100 | 技术         | tech     | 0      |
+---------+--------------+----------+--------+

-- 最终总结
+----------------------------------+----------------+-------------+----------------+---------------------+
| final_status                     | parent_menu_id | new_menu_id | roles_assigned | execution_time      |
+----------------------------------+----------------+-------------+----------------+---------------------+
| ✓ 角色查询权限添加成功           |           2060 |        2070 |              3 | 2025-07-26 xx:xx:xx |
+----------------------------------+----------------+-------------+----------------+---------------------+
```

## ✅ 执行后验证

### 1. 数据库验证
```sql
-- 验证权限菜单是否创建成功
SELECT menu_id, menu_name, parent_id, perms 
FROM sys_menu 
WHERE perms = 'system:player:characters';

-- 验证角色权限是否分配成功
SELECT r.role_name, COUNT(rm.menu_id) as permission_count
FROM sys_role r
LEFT JOIN sys_role_menu rm ON r.role_id = rm.role_id
INNER JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.perms = 'system:player:characters'
GROUP BY r.role_id, r.role_name;
```

### 2. 系统界面验证
1. **菜单管理验证**：
   - 登录系统管理后台
   - 进入 `系统管理` -> `菜单管理`
   - 找到"玩家统计"菜单，展开查看
   - 确认存在"角色查询"子菜单项

2. **角色权限验证**：
   - 进入 `系统管理` -> `角色管理`
   - 点击任意角色的"修改"按钮
   - 在权限设置中找到"玩家统计"模块
   - 确认"角色查询"权限已被勾选

### 3. 功能测试验证
1. 重启应用程序
2. 登录系统，进入玩家统计页面
3. 点击任意玩家的"详情"按钮
4. 确认弹窗中显示角色信息表格

## 🔧 故障排除

### 常见问题及解决方案

1. **权限已存在错误**
   ```
   解决方案：脚本会自动跳过，这是正常情况
   ```

2. **父菜单不存在**
   ```
   错误信息：✗ 玩家统计主菜单不存在
   解决方案：先执行 sql/player_menu.sql 创建主菜单
   ```

3. **事务回滚**
   ```
   原因：可能是权限不足或表结构问题
   解决方案：检查数据库连接权限，确认表结构完整
   ```

4. **角色分配失败**
   ```
   原因：sys_role_menu表约束问题
   解决方案：检查表结构，确认外键约束正常
   ```

## 📝 执行记录模板

```
执行日期：2025-07-26
执行人员：[你的名字]
数据库：jeethink
执行结果：
- 权限创建：✅ 成功 / ❌ 失败
- 角色分配：✅ 成功 / ❌ 失败  
- 分配角色数：[数量]
- 新菜单ID：[menu_id]
备注：[任何特殊情况或问题]
```

## 🎯 下一步操作

执行成功后，你需要：

1. **重启应用程序**
   ```bash
   # 在IDEA中重启Spring Boot应用
   # 或者如果是部署环境，重启服务
   ```

2. **清除缓存**（如果使用了缓存）
   ```bash
   # 清除权限相关缓存
   ```

3. **通知相关用户**
   - 告知用户新功能已上线
   - 提供使用说明

---

**重要提醒**：
- 此脚本是幂等的，可以安全地重复执行
- 所有操作都在事务中进行，如有错误会自动回滚
- 建议在测试环境先执行一次验证
