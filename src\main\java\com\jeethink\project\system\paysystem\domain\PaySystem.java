package com.jeethink.project.system.paysystem.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jeethink.framework.aspectj.lang.annotation.Excel;
import com.jeethink.framework.web.domain.BaseEntity;

/**
 * 代理分成对象 pay
 * 
 * <AUTHOR>
 * @date 2023-11-11
 */
public class PaySystem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 订单号 */
    private String mchOrderNo;

    /** 区号 */
    @Excel(name = "区号")
    private Integer area;

    /** 支付渠道 */
    @Excel(name = "支付渠道")
    private String channelCode;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private Long amount;

    /** 角色名 */
    @Excel(name = "角色名")
    private String goodsParamExt;

    /** ip */
    @Excel(name = "ip")
    private String ip;

    /** 支付状态（0正常 1停用） */
    @Excel(name = "支付状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 支付时间 */
    private String paySuccTime;

    /** 发货状态 */
    @Excel(name = "发货状态")
    private Integer deliverGoods;

    /** 代理 */
    @Excel(name = "代理")
    private String agency;

    /** 平台订单号 */
    @Excel(name = "平台订单号")
    private String wudiOrderNo;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setMchOrderNo(String mchOrderNo)
    {
        this.mchOrderNo = mchOrderNo;
    }

    public String getMchOrderNo()
    {
        return mchOrderNo;
    }
    public void setArea(Integer area)
    {
        this.area = area;
    }

    public Integer getArea()
    {
        return area;
    }
    public void setChannelCode(String channelCode)
    {
        this.channelCode = channelCode;
    }

    public String getChannelCode()
    {
        return channelCode;
    }
    public void setAmount(Long amount)
    {
        this.amount = amount;
    }

    public Long getAmount()
    {
        return amount;
    }
    public void setGoodsParamExt(String goodsParamExt)
    {
        this.goodsParamExt = goodsParamExt;
    }

    public String getGoodsParamExt()
    {
        return goodsParamExt;
    }
    public void setIp(String ip)
    {
        this.ip = ip;
    }

    public String getIp()
    {
        return ip;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setPaySuccTime(String paySuccTime)
    {
        this.paySuccTime = paySuccTime;
    }

    public String getPaySuccTime()
    {
        return paySuccTime;
    }
    public void setDeliverGoods(Integer deliverGoods)
    {
        this.deliverGoods = deliverGoods;
    }

    public Integer getDeliverGoods()
    {
        return deliverGoods;
    }
    public void setAgency(String agency)
    {
        this.agency = agency;
    }

    public String getAgency()
    {
        return agency;
    }
    public void setWudiOrderNo(String wudiOrderNo)
    {
        this.wudiOrderNo = wudiOrderNo;
    }

    public String getWudiOrderNo()
    {
        return wudiOrderNo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("mchOrderNo", getMchOrderNo())
            .append("area", getArea())
            .append("channelCode", getChannelCode())
            .append("amount", getAmount())
            .append("goodsParamExt", getGoodsParamExt())
            .append("ip", getIp())
            .append("status", getStatus())
            .append("paySuccTime", getPaySuccTime())
            .append("deliverGoods", getDeliverGoods())
            .append("createBy", getCreateBy())
            .append("agency", getAgency())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("wudiOrderNo", getWudiOrderNo())
            .toString();
    }
}
