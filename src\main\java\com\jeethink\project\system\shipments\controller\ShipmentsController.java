package com.jeethink.project.system.shipments.controller;

import java.util.List;

import com.alibaba.fastjson.JSON;
import com.jeethink.project.system.goods.domain.Goods;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.jeethink.framework.aspectj.lang.annotation.Log;
import com.jeethink.framework.aspectj.lang.enums.BusinessType;
import com.jeethink.project.system.shipments.domain.Shipments;
import com.jeethink.project.system.shipments.service.IShipmentsService;
import com.jeethink.project.system.goods.service.IGoodsService;
import com.jeethink.framework.web.controller.BaseController;
import com.jeethink.framework.web.domain.AjaxResult;
import com.jeethink.common.utils.poi.ExcelUtil;
import com.jeethink.framework.web.page.TableDataInfo;
import springfox.documentation.spring.web.json.Json;

/**
 * 发货信息表Controller
 * 
 * <AUTHOR>
 * @date 2024-01-31
 */
@Controller
@RequestMapping("/system/shipments")
public class ShipmentsController extends BaseController
{
    private String prefix = "system/shipments";

    @Autowired
    private IShipmentsService shipmentsService;

    @Autowired
    private IGoodsService goodsService;


    @RequiresPermissions("system:shipments:view")
    @GetMapping()
    public String shipments()
    {
        return prefix + "/shipments";
    }

    /**
     * 查询发货信息表列表
     */
    @RequiresPermissions("system:shipments:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Shipments shipments)
    {
        startPage();
        List<Shipments> list = shipmentsService.selectShipmentsList(shipments);
        return getDataTable(list);
    }

    /**
     * 导出发货信息表列表
     */
    @RequiresPermissions("system:shipments:export")
    @Log(title = "发货信息表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Shipments shipments)
    {
        List<Shipments> list = shipmentsService.selectShipmentsList(shipments);
        ExcelUtil<Shipments> util = new ExcelUtil<Shipments>(Shipments.class);
        return util.exportExcel(list, "shipments");
    }

    /**
     * 新增发货信息表
     */
    @GetMapping("/add")
    public String add(ModelMap mmap)
    {
        //List<Goods> list=goodsService.selectGoodsAll();
        /*if(list.size()>=200){
            list= list.subList(0,200);
        }*/
        //mmap.put("goods",list );
        return prefix + "/add";
    }
/**
 * 根据名字模糊查询下拉列表
 * */
    @RequiresPermissions("system:shipments:listGoods")
    @PostMapping("/listGoods")
    @ResponseBody
    public String listGoods(Shipments shipments)
    {
        Goods goods=new Goods();
        if(shipments!=null&&shipments.getWpname()!=null){
            goods.setGname(shipments.getWpname());
        }
        List<Goods> list=goodsService.selectGoodsAll(goods);
        return JSON.toJSONString(list);

    }
    /**
     * 新增保存发货信息表
     */
    @RequiresPermissions("system:shipments:add")
    @Log(title = "发货信息表", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(Shipments shipments)
    {
        return toAjax(shipmentsService.insertShipments(shipments));
    }

    /**
     * 修改发货信息表
     */
    @GetMapping("/edit/{sid}")
    public String edit(@PathVariable("sid") Long sid, ModelMap mmap)
    {
        /*Shipments shipments = shipmentsService.selectShipmentsById(sid);
        mmap.put("shipments", shipments);
        mmap.put("goods", goodsService.selectGoodByShId(sid));*/
        return prefix + "/edit";
    }

    /**
     * 修改保存发货信息表
     */
    @RequiresPermissions("system:shipments:edit")
    @Log(title = "发货信息表", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(Shipments shipments)
    {

        return toAjax(shipmentsService.updateShipments(shipments));
    }

    /**
     * 删除发货信息表
     */
    @RequiresPermissions("system:shipments:remove")
    @Log(title = "发货信息表", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(shipmentsService.deleteShipmentsByIds(ids));
    }
}
