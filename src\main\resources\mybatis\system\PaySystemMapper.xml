<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeethink.project.system.paysystem.mapper.PaySystemMapper">
    
    <resultMap type="PaySystem" id="PaySystemResult">
        <result property="id"    column="id"    />
        <result property="mchOrderNo"    column="mch_order_no"    />
        <result property="area"    column="area"    />
        <result property="channelCode"    column="channel_code"    />
        <result property="amount"    column="amount"    />
        <result property="goodsParamExt"    column="goods_param_ext"    />
        <result property="ip"    column="ip"    />
        <result property="status"    column="status"    />
        <result property="paySuccTime"    column="pay_succ_time"    />
        <result property="deliverGoods"    column="deliver_goods"    />
        <result property="createBy"    column="create_by"    />
        <result property="agency"    column="agency"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="wudiOrderNo"    column="wuDi_order_no"    />
    </resultMap>

    <sql id="selectPaySystemVo">
        select id, mch_order_no, area, channel_code, amount, goods_param_ext, ip, status, pay_succ_time, deliver_goods, create_by, agency, create_time, update_by, update_time, wuDi_order_no from pay
    </sql>

    <select id="selectPaySystemList" parameterType="PaySystem" resultMap="PaySystemResult">
        <include refid="selectPaySystemVo"/>
        <where>  
            <if test="area != null "> and area = #{area}</if>
            <if test="channelCode != null  and channelCode != ''"> and channel_code = #{channelCode}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="goodsParamExt != null  and goodsParamExt != ''"> and goods_param_ext = #{goodsParamExt}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="deliverGoods != null "> and deliver_goods = #{deliverGoods}</if>
            <if test="agency != null  and agency != ''"> and agency = #{agency}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="wudiOrderNo != null  and wudiOrderNo != ''"> and wuDi_order_no = #{wudiOrderNo}</if>

            <if test="params.beginCreateTime != null and params.beginCreateTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginCreateTime},'%y%m%d')
            </if>
            <if test="params.endCreateTime != null and params.endCreateTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endCreateTime},'%y%m%d')
            </if>
        </where>
    </select>

    <select id="selectPaySum" resultType="Integer">
        select sum(amount) as amount from pay
        <where>
            <if test="area != null "> and area = #{area}</if>
            <if test="channelCode != null  and channelCode != ''"> and channel_code = #{channelCode}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="goodsParamExt != null  and goodsParamExt != ''"> and goods_param_ext = #{goodsParamExt}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="deliverGoods != null "> and deliver_goods = #{deliverGoods}</if>
            <if test="agency != null  and agency != ''"> and agency = #{agency}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="wudiOrderNo != null  and wudiOrderNo != ''"> and wuDi_order_no = #{wudiOrderNo}</if>

            <if test="params.beginCreateTime != null and params.beginCreateTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginCreateTime},'%y%m%d')
            </if>
            <if test="params.endCreateTime != null and params.endCreateTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endCreateTime},'%y%m%d')
            </if>
        </where>
    </select>
    <select id="selectPayTb" parameterType="PaySystem" resultMap="PaySystemResult">
        select DATE_FORMAT(create_time,'%Y-%m-%d') as pay_succ_time,count(1) as create_by,SUM(amount) as amount from pay
        <where>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="agency != null  and agency != ''"> and agency = #{agency}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginCreateTime},'%y%m%d')
            </if>
            <if test="params.endCreateTime != null and params.endCreateTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endCreateTime},'%y%m%d')
            </if>
        </where>
        group by DATE_FORMAT(create_time,'%Y-%m-%d');
    </select>
    <select id="selectPayAm" parameterType="PaySystem" resultMap="PaySystemResult">
        select agency,SUM(amount) AS amount,COUNT(1) AS create_by,DATE_FORMAT(create_time,'%Y-%m') as pay_succ_time from pay
        <where>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="agency != null  and agency != ''"> and agency = #{agency}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginCreateTime},'%y%m%d')
            </if>
            <if test="params.endCreateTime != null and params.endCreateTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endCreateTime},'%y%m%d')
            </if>
        </where>
        GROUP BY agency,DATE_FORMAT(create_time,'%Y-%m') order by amount desc;
    </select>
    <select id="selectPaySystemById" parameterType="Long" resultMap="PaySystemResult">
        <include refid="selectPaySystemVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPaySystem" parameterType="PaySystem" useGeneratedKeys="true" keyProperty="id">
        insert into pay
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mchOrderNo != null">mch_order_no,</if>
            <if test="area != null">area,</if>
            <if test="channelCode != null">channel_code,</if>
            <if test="amount != null">amount,</if>
            <if test="goodsParamExt != null">goods_param_ext,</if>
            <if test="ip != null">ip,</if>
            <if test="status != null">status,</if>
            <if test="paySuccTime != null">pay_succ_time,</if>
            <if test="deliverGoods != null">deliver_goods,</if>
            <if test="createBy != null">create_by,</if>
            <if test="agency != null">agency,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="wudiOrderNo != null">wuDi_order_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mchOrderNo != null">#{mchOrderNo},</if>
            <if test="area != null">#{area},</if>
            <if test="channelCode != null">#{channelCode},</if>
            <if test="amount != null">#{amount},</if>
            <if test="goodsParamExt != null">#{goodsParamExt},</if>
            <if test="ip != null">#{ip},</if>
            <if test="status != null">#{status},</if>
            <if test="paySuccTime != null">#{paySuccTime},</if>
            <if test="deliverGoods != null">#{deliverGoods},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="agency != null">#{agency},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="wudiOrderNo != null">#{wudiOrderNo},</if>
         </trim>
    </insert>

    <update id="updatePaySystem" parameterType="PaySystem">
        update pay
        <trim prefix="SET" suffixOverrides=",">
            <if test="mchOrderNo != null">mch_order_no = #{mchOrderNo},</if>
            <if test="area != null">area = #{area},</if>
            <if test="channelCode != null">channel_code = #{channelCode},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="goodsParamExt != null">goods_param_ext = #{goodsParamExt},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="status != null">status = #{status},</if>
            <if test="paySuccTime != null">pay_succ_time = #{paySuccTime},</if>
            <if test="deliverGoods != null">deliver_goods = #{deliverGoods},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="agency != null">agency = #{agency},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="wudiOrderNo != null">wuDi_order_no = #{wudiOrderNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePaySystemById" parameterType="Long">
        delete from pay where id = #{id}
    </delete>

    <delete id="deletePaySystemByIds" parameterType="String">
        delete from pay where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>