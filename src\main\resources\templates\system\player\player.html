<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('玩家统计列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <!-- 统计信息显示区域 -->
            <div class="col-sm-12" style="margin-bottom: 20px;">
                <div class="alert alert-info" role="alert" style="font-size: 18px; text-align: center; background-color: #d9edf7; border-color: #bce8f1;">
                    <strong>
                        <button type="button" class="btn btn-primary btn-sm" onclick="showOnlinePlayersList()" style="margin-right: 15px;">
                            <i class="fa fa-list"></i> 游戏世界在线玩家列表
                        </button>
                        <i class="fa fa-users"></i> 当前在线玩家数量：<span id="onlineCount" style="color: #31708f; font-weight: bold;">0</span>
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        <i class="fa fa-user-plus"></i> 今日注册账号数量：<span id="todayRegCount" style="color: #31708f; font-weight: bold;">0</span>
                    </strong>
                </div>
            </div>
            
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>玩家账号：</label>
                                <input type="text" name="account" id="account" placeholder="请输入玩家账号"/>
                            </li>
                            <li>
                                <label>邀请码：</label>
                                <input type="text" name="invitation" id="invitation" placeholder="请输入邀请码"/>
                            </li>
                            <li>
                                <label>IP地址：</label>
                                <input type="text" name="lastIp" id="lastIp" placeholder="请输入IP地址"/>
                            </li>
                            <li>
                                <label>在线状态：</label>
                                <select name="onlineStatus" id="onlineStatus">
                                    <option value="">所有</option>
                                    <option value="在线">在线</option>
                                    <option value="离线">离线</option>
                                    <option value="尚未正式参与游戏">尚未正式参与游戏</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search(); refreshStats();"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset(); refreshStats();"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                <a class="btn btn-info btn-rounded btn-sm" onclick="refreshStats();"><i class="fa fa-refresh"></i>&nbsp;刷新统计</a>
                                <a class="btn btn-success btn-rounded btn-sm" onclick="refreshTable();"><i class="fa fa-table"></i>&nbsp;刷新表格</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="toggleAutoRefresh();" id="autoRefreshBtn"><i class="fa fa-pause"></i>&nbsp;暂停自动刷新</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:player:export">
                    <i class="fa fa-download"></i> 导出
                </a>
                <span>&nbsp;&nbsp;&nbsp;&nbsp;</span>
                <span style="font-size: 16px; color: #666;">
                    <i class="fa fa-info-circle"></i> 统计信息30秒自动更新，表格数据60秒自动刷新
                    <span id="refreshStatus" style="margin-left: 10px; color: #28a745;">● 自动刷新已开启</span>
                </span>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:player:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:player:remove')}]];
        var prefix = ctx + "system/player";

        // 自动刷新控制变量
        var autoRefreshEnabled = true;
        var statsRefreshTimer = null;
        var tableRefreshTimer = null;
        var isPageVisible = true;
        var userInteracting = false;

        $(function() {
            var options = {
                url: prefix + "/list",
                exportUrl: prefix + "/export",
                modalName: "玩家统计",
                showRefresh: true,
                showToggle: true,
                showColumns: true,
                rememberSelected: true,
                pagination: true,
                sidePagination: "server",
                pageSize: 20,
                pageList: [10, 20, 50, 100],
                columns: [{
                    checkbox: true
                },
                {
                    field: 'uid',
                    title: '用户ID',
                    visible: false
                },
                {
                    field: 'account',
                    title: '玩家账号',
                    sortable: true
                },
                {
                    field: 'onlineStatus',
                    title: '在线状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value === '在线') {
                            return '<span class="badge badge-success">在线</span>';
                        } else if (value === '尚未正式参与游戏') {
                            return '<span class="badge badge-warning">尚未正式参与游戏</span>';
                        } else {
                            return '<span class="badge badge-secondary">离线</span>';
                        }
                    }
                },
                {
                    field: 'invitation',
                    title: '邀请码',
                    align: 'center'
                },
                {
                    field: 'lastLogin',
                    title: '最后登录时间',
                    sortable: true
                },
                {
                    field: 'lastLogout',
                    title: '最后登出时间',
                    sortable: true
                },
                {
                    field: 'regDate',
                    title: '注册时间',
                    sortable: true
                },
                {
                    field: 'lastIp',
                    title: '最后IP',
                    align: 'center'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewDetail(\'' + row.account + '\')"><i class="fa fa-eye"></i>详情</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);

            // 初始化统计信息
            refreshStats();

            // 启动智能刷新机制
            startAutoRefresh();

            // 页面可见性检测
            setupVisibilityDetection();

            // 用户交互检测
            setupUserInteractionDetection();
        });
        
        // 刷新统计信息
        function refreshStats() {
            $.ajax({
                type: "post",
                url: prefix + "/stats",
                dataType: "json",
                success: function (res) {
                    if (res) {
                        document.getElementById("onlineCount").innerHTML = res.onlineCount || 0;
                        document.getElementById("todayRegCount").innerHTML = res.todayRegisterCount || 0;
                    }
                },
                error: function() {
                    console.log("获取统计信息失败");
                }
            });
        }
        
        // 查看玩家详情
        function viewDetail(account) {
            // 同时获取玩家基本信息和角色信息
            $.when(
                $.ajax({
                    type: "get",
                    url: prefix + "/detail/" + account,
                    dataType: "json"
                }),
                $.ajax({
                    type: "get",
                    url: prefix + "/characters/" + account,
                    dataType: "json"
                })
            ).done(function(playerRes, charactersRes) {
                var playerData = playerRes[0];
                var charactersData = charactersRes[0];

                if (playerData.code === 0 && charactersData.code === 0) {
                    var player = playerData.data;
                    var characters = charactersData.data;

                    // 构建玩家基本信息
                    var content = '<div class="row">' +
                        '<div class="col-sm-12"><h4 style="color: #337ab7; border-bottom: 2px solid #337ab7; padding-bottom: 5px;"><i class="fa fa-user"></i> 账户信息</h4></div>' +
                        '<div class="col-sm-6"><strong>玩家账号：</strong>' + (player.account || '') + '</div>' +
                        '<div class="col-sm-6"><strong>在线状态：</strong>' + (player.onlineStatus || '') + '</div>' +
                        '<div class="col-sm-6"><strong>邀请码：</strong>' + (player.invitation || '') + '</div>' +
                        '<div class="col-sm-6"><strong>最后IP：</strong>' + (player.lastIp || '') + '</div>' +
                        '<div class="col-sm-6"><strong>最后登录：</strong>' + (player.lastLogin || '') + '</div>' +
                        '<div class="col-sm-6"><strong>最后登出：</strong>' + (player.lastLogout || '') + '</div>' +
                        '<div class="col-sm-6"><strong>注册时间：</strong>' + (player.regDate || '') + '</div>' +
                        '<div class="col-sm-6"><strong>用户ID：</strong>' + (player.uid || '') + '</div>' +
                        '</div>';

                    // 构建角色信息 - 简化版本
                    content += '<hr><h4><i class="fa fa-gamepad"></i> 角色信息 (' + characters.length + '个角色)</h4>';

                    if (characters.length > 0) {
                        content += '<table class="table table-striped">';
                        content += '<thead><tr>';
                        content += '<th>角色名称</th><th>等级</th><th>在线状态</th><th>在线时长</th><th>最后登录</th>';
                        content += '</tr></thead><tbody>';

                        for (var i = 0; i < characters.length; i++) {
                            var char = characters[i];
                            var statusText = char.onlineStatus === '在线' ? '🟢 在线' : '🔴 离线';

                            content += '<tr>';
                            content += '<td>' + (char.charName || '') + '</td>';
                            content += '<td>' + (char.level || 0) + '</td>';
                            content += '<td>' + statusText + '</td>';
                            content += '<td>' + (char.useTimeFormatted || '0秒') + '</td>';
                            content += '<td>' + (char.login || '') + '</td>';
                            content += '</tr>';
                        }

                        content += '</tbody></table>';
                    } else {
                        content += '<div class="alert alert-info">该账户暂无角色信息</div>';
                    }

                    // 使用更安全的方式显示弹窗
                    showPlayerDetailModal("玩家详情 - " + account, content);
                } else {
                    var errorMsg = playerData.code !== 0 ? playerData.msg : charactersData.msg;
                    $.modal.alertError(errorMsg);
                }
            }).fail(function() {
                $.modal.alertError("获取玩家详情失败");
            });
        }

        // 启动自动刷新机制
        function startAutoRefresh() {
            if (!autoRefreshEnabled) return;

            // 统计信息刷新（30秒）
            statsRefreshTimer = setInterval(function() {
                if (autoRefreshEnabled && isPageVisible && !userInteracting) {
                    refreshStats();
                }
            }, 30000);

            // 表格数据刷新（60秒）
            tableRefreshTimer = setInterval(function() {
                if (autoRefreshEnabled && isPageVisible && !userInteracting) {
                    console.log("自动刷新表格数据");
                    $('#bootstrap-table').bootstrapTable('refresh');
                }
            }, 60000);
        }

        // 停止自动刷新
        function stopAutoRefresh() {
            if (statsRefreshTimer) {
                clearInterval(statsRefreshTimer);
                statsRefreshTimer = null;
            }
            if (tableRefreshTimer) {
                clearInterval(tableRefreshTimer);
                tableRefreshTimer = null;
            }
        }

        // 切换自动刷新状态
        function toggleAutoRefresh() {
            autoRefreshEnabled = !autoRefreshEnabled;
            var btn = document.getElementById('autoRefreshBtn');
            var status = document.getElementById('refreshStatus');

            if (autoRefreshEnabled) {
                btn.innerHTML = '<i class="fa fa-pause"></i>&nbsp;暂停自动刷新';
                btn.className = 'btn btn-warning btn-rounded btn-sm';
                status.innerHTML = '● 自动刷新已开启';
                status.style.color = '#28a745';
                startAutoRefresh();
            } else {
                btn.innerHTML = '<i class="fa fa-play"></i>&nbsp;开启自动刷新';
                btn.className = 'btn btn-success btn-rounded btn-sm';
                status.innerHTML = '● 自动刷新已暂停';
                status.style.color = '#dc3545';
                stopAutoRefresh();
            }
        }

        // 页面可见性检测
        function setupVisibilityDetection() {
            document.addEventListener('visibilitychange', function() {
                isPageVisible = !document.hidden;
                console.log('页面可见性变化:', isPageVisible ? '可见' : '隐藏');
            });
        }

        // 用户交互检测
        function setupUserInteractionDetection() {
            var interactionTimeout;

            // 监听用户交互事件
            $(document).on('click keydown scroll', function() {
                userInteracting = true;
                clearTimeout(interactionTimeout);

                // 3秒后恢复自动刷新
                interactionTimeout = setTimeout(function() {
                    userInteracting = false;
                }, 3000);
            });

            // 监听表格操作
            $('#bootstrap-table').on('all.bs.table', function() {
                userInteracting = true;
                clearTimeout(interactionTimeout);

                interactionTimeout = setTimeout(function() {
                    userInteracting = false;
                }, 5000);
            });
        }

        // 手动刷新表格（重写原有的刷新逻辑）
        function refreshTable() {
            console.log("手动刷新表格数据");
            $('#bootstrap-table').bootstrapTable('refresh');
        }

        // 页面卸载时清理定时器
        $(window).on('beforeunload', function() {
            stopAutoRefresh();
        });

        // 显示在线玩家列表
        function showOnlinePlayersList() {
            console.log("=== 显示在线玩家列表 ===");

            // 获取当前表格中在线玩家的账户名列表
            var onlineAccountNames = [];
            $('#bootstrap-table').bootstrapTable('getData').forEach(function(row) {
                if (row.onlineStatus === '在线') {
                    onlineAccountNames.push(row.account);
                }
            });

            console.log("在线账户名列表:", onlineAccountNames);

            if (onlineAccountNames.length === 0) {
                $.modal.alertWarning("当前没有在线玩家");
                return;
            }

            // 发送请求获取在线角色信息
            $.ajax({
                type: "POST",
                url: prefix + "/onlinePlayers",
                contentType: "application/json",
                data: JSON.stringify(onlineAccountNames),
                dataType: "json",
                success: function (res) {
                    console.log("在线玩家查询结果:", res);

                    if (res.code === 0) {
                        var characters = res.data;
                        var content = buildOnlinePlayersContent(characters);
                        showOnlinePlayersModal("游戏世界在线玩家列表", content);
                    } else {
                        $.modal.alertError(res.msg);
                    }
                },
                error: function(xhr, status, error) {
                    console.error("查询在线玩家失败:", xhr, status, error);
                    $.modal.alertError("查询在线玩家失败: " + error);
                }
            });
        }

        // 构建在线玩家列表内容
        function buildOnlinePlayersContent(characters) {
            if (characters.length === 0) {
                return '<div class="alert alert-info">暂无在线角色信息</div>';
            }

            var content = '<div class="table-responsive">';
            content += '<table class="table table-striped table-bordered table-hover">';
            content += '<thead style="background-color: #f5f5f5;">';
            content += '<tr>';
            content += '<th style="text-align: center;">游戏名</th>';
            content += '<th style="text-align: center;">账号名</th>';
            content += '<th style="text-align: center;">玩家等级</th>';
            content += '<th style="text-align: center;">在线时长</th>';
            content += '<th style="text-align: center;">最后登录</th>';
            content += '</tr>';
            content += '</thead>';
            content += '<tbody>';

            for (var i = 0; i < characters.length; i++) {
                var char = characters[i];
                content += '<tr>';
                content += '<td style="text-align: center;"><strong>' + (char.charName || '') + '</strong></td>';
                content += '<td style="text-align: center;">' + (char.accountName || '') + '</td>';
                content += '<td style="text-align: center;">' + (char.level || 0) + '</td>';
                content += '<td style="text-align: center;">' + (char.useTimeFormatted || '0秒') + '</td>';
                content += '<td style="text-align: center;">' + (char.login || '') + '</td>';
                content += '</tr>';
            }

            content += '</tbody>';
            content += '</table>';
            content += '</div>';

            return content;
        }

        // 显示在线玩家列表模态框
        function showOnlinePlayersModal(title, content) {
            var modalHtml =
                '<div class="modal fade" id="onlinePlayersModal" tabindex="-1" role="dialog">' +
                    '<div class="modal-dialog modal-lg" role="document" style="width: 1200px; max-width: 95%;">' +
                        '<div class="modal-content">' +
                            '<div class="modal-header">' +
                                '<h4 class="modal-title">' + title + '</h4>' +
                                '<button type="button" class="close" data-dismiss="modal">' +
                                    '<span>&times;</span>' +
                                '</button>' +
                            '</div>' +
                            '<div class="modal-body" style="max-height: 600px; overflow-y: auto;">' +
                                content +
                            '</div>' +
                            '<div class="modal-footer">' +
                                '<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>' +
                            '</div>' +
                        '</div>' +
                    '</div>' +
                '</div>';

            // 移除已存在的模态框
            $('#onlinePlayersModal').remove();

            // 添加新的模态框到页面
            $('body').append(modalHtml);

            // 显示模态框
            $('#onlinePlayersModal').modal('show');

            // 模态框关闭后移除DOM元素
            $('#onlinePlayersModal').on('hidden.bs.modal', function () {
                $(this).remove();
            });
        }

        // 自定义模态框显示函数
        function showPlayerDetailModal(title, content) {
            // 创建模态框HTML
            var modalHtml =
                '<div class="modal fade" id="playerDetailModal" tabindex="-1" role="dialog">' +
                    '<div class="modal-dialog modal-lg" role="document" style="width: 1000px; max-width: 90%;">' +
                        '<div class="modal-content">' +
                            '<div class="modal-header">' +
                                '<h4 class="modal-title">' + title + '</h4>' +
                                '<button type="button" class="close" data-dismiss="modal">' +
                                    '<span>&times;</span>' +
                                '</button>' +
                            '</div>' +
                            '<div class="modal-body" style="max-height: 600px; overflow-y: auto;">' +
                                content +
                            '</div>' +
                            '<div class="modal-footer">' +
                                '<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>' +
                            '</div>' +
                        '</div>' +
                    '</div>' +
                '</div>';

            // 移除已存在的模态框
            $('#playerDetailModal').remove();

            // 添加新的模态框到页面
            $('body').append(modalHtml);

            // 显示模态框
            $('#playerDetailModal').modal('show');

            // 模态框关闭后移除DOM元素
            $('#playerDetailModal').on('hidden.bs.modal', function () {
                $(this).remove();
            });
        }
    </script>
</body>
</html>
