# JeeThink 项目技术栈总结

## 核心技术栈

### 后端技术
- **Spring Boot**: 版本 2.1.17.RELEASE，作为整个应用的基础框架
- **MyBatis**: 版本 2.1.3，用于数据持久层操作
- **Shiro**: 版本 1.6.0，用于权限管理和安全控制
- **MySQL**: 数据库，通过 mysql-connector-java 驱动连接
- **Druid**: 版本 1.2.1，阿里巴巴开源的数据库连接池
- **PageHelper**: 版本 1.3.0，MyBatis 分页插件
- **Thymeleaf**: 用于服务端页面渲染，与 Shiro 集成

### 前端技术
- **jQuery**: JavaScript 库
- **Bootstrap**: 前端 UI 框架
- **Layui**: 前端 UI 组件库
- **Font Awesome**: 图标库
- **Bootstrap Table**: 表格插件
- **Bootstrap TreeTable**: 树形表格插件
- **Select2**: 下拉框增强插件
- **Summernote**: 富文本编辑器
- **ECharts**: 图表库

### 工具类库
- **Apache Commons**: 常用工具类库
- **Hutool**: 版本 5.7.1，国产工具类库
- **FastJSON**: 版本 1.2.74，JSON 处理库
- **POI**: 版本 3.17，Excel 文件处理
- **HttpClient**: 版本 4.5.2，HTTP 客户端

## 系统架构

### 分层结构
- **表现层**: 基于 Thymeleaf 模板引擎
- **控制层**: Spring MVC 控制器
- **业务层**: 服务接口与实现
- **持久层**: MyBatis 与数据库交互

### 核心功能模块
- **权限管理**: 基于 Shiro 的认证与授权
- **用户管理**: 用户信息、角色分配
- **角色管理**: 角色权限分配
- **菜单管理**: 系统菜单配置
- **部门管理**: 组织架构
- **字典管理**: 系统字典
- **监控功能**: 操作日志、登录日志、在线用户
- **代码生成**: 基于模板的代码生成

### 数据源配置
- 支持主从数据源配置
- 使用 Druid 连接池管理数据库连接

### 安全特性
- 基于 Shiro 的身份认证
- 基于角色的权限控制
- 会话管理与限制
- 防止 XSS 攻击
- 验证码登录保护

## 开发环境
- JDK 1.8 (项目配置，实际运行环境为 Java 17)
- Maven 构建工具
- Spring Boot 内嵌 Tomcat 容器 