//退出登陆
function outLogin() {
    opAjaxData({ _title: "退出登录确认", _content: "你确定要退出登录吗？", _ajaxUrl: "/Home/OutLogin" });
}

function GetAjaxData(dataPara, ajaxUrl, requestType, callDataType) {
     var ReturnVal = ""; 
     requestType = requestType || "POST";
     callDataType = callDataType || "json";
     var deferred = $.Deferred();
     var request = $.ajax({
         type: requestType,
         url: ajaxUrl,
         data: dataPara,
         cache: false,
         dataType: callDataType,
         beforeSend: function () {
         }
     });
     request.done(function (result) {
         ReturnVal = result;
         deferred.resolve(ReturnVal);
         return ReturnVal;
     });
     request.fail(function () {
         deferred.reject(ReturnVal);
     });
     return deferred;
 }

 function opAjaxData(settings) {
     var defaultSetting = {
         _title: '确认删除吗？',
         _content: '确定删除这条数据吗？',
         _ajaxUrl: '',
         _dataPara: {},
         _icon: 'fa fa-question',
         _closeIcon: true
     };
     $.extend(defaultSetting, settings); 
     $.confirm({
         title: defaultSetting._title,
         content: defaultSetting._content,
         icon: defaultSetting._icon,
         closeIcon: defaultSetting._closeIcon,
         theme: 'white',
         animation: 'scale',
         type: 'orange',
         backgroundDismiss: false,
         buttons: {
             "confirm": {
                 text: '确 定',
                 btnClass: 'btn-red',
                 action: function () {
                     GetAjaxData(defaultSetting._dataPara, defaultSetting._ajaxUrl).then(function (AjaxData) {
                         if (!AjaxData)
                         { opAlert(2, '请求异常'); }
                         else
                         {
                             opAlert(AjaxData.State, AjaxData.OpMsg, AjaxData.GoNext, AjaxData.OpTitle);
                         }
                     });
                 }
             },
             cancel: {
                 text: "取 消",
                 action: function () { }
             }
         }
     });
 }

// state(1成功；2失败；3消息；4警告)、opMsg（提示内容）、goNext （空不执行，0刷新1后退 其他string跳转）
 function opAlert(state, opMsg, goNext, opTitle) {
     goNext = goNext || "";
     var _type = "green";
     var _icon = "fa fa-check-circle";
     switch (parseInt(state)) {
         case 1:
             opTitle = opTitle || "恭喜，成功！";
             break;
         case 0:
         case 2://失败或错误
             opTitle = opTitle || "抱歉，失败！";
             _type = "red";
             _icon = "fa  fa-times-circle-o";
             break;
         case 3://普通消息
             opTitle = opTitle || "通知！";
             _type = "blue";
             _icon = "fa fa-exclamation-circle";
             break;
         case 4://警告提醒
             opTitle = opTitle || "警告！";
             _type = "orange";
             _icon = "fa fa-exclamation-triangle";
             break;
     }
     $.alert({
         title: opTitle,
         content: opMsg,
         icon: _icon,
         type: _type,
         animation: 'zoom',
         closeAnimation: 'zoom',
         backgroundDismiss: false,
         buttons: {
             okay: {
                 text: '确 定',
                 btnClass: 'btn-blue',
                 action: function () {
                     if (goNext.length > 0) {
                         if (goNext == "0") { window.location.reload(); }//刷新
                         else {
                             if (goNext == "1") { window.history.go(-1); }/*后退*/
                             else {
                                 if (goNext == "-1") { window.close(); }
                                 else {
                                     goNext = goNext.split('|');
                                     var firstAction = goNext[0];
                                     if (firstAction == "T" && goNext[1].length > 0) { eval("tableAlert ('" + goNext[1] + "');"); }//刷新表格
                                     else if (firstAction == "F" && goNext[1].length > 0) {
                                         eval("" + goNext[1] + "");
                                     } else {
                                         window.location.replace(goNext);//跳转
                                     }
                                 }
                             }
                         }

                     }
                 }
             }
         }
     });
}

//当Ajax或者提交表单返回值时处理table刷新以及关闭模态框
function tableAlert(tableId) {
    tableId = tableId || "";
    if (tableId.length > 0) {
        $("#" + tableId).bootstrapTable('refresh');
    }
    if (typeof (dialogTmp) != "undefined") {
        dialogTmp.close();
    }
}

//验证登录的正则
function checkData(action, str) {
    //手机号
    if (parseInt(action) == 1) {
        var regu = /^1\d{10}$/;
        var re = new RegExp(regu);
        if (re.test(str)) { return true; } else { return false; }
    }
    //姓名
    if (parseInt(action) == 2) {
        var regu = /^[\u4E00-\u9FA5\uf900-\ufa2d]{2,6}$/;
        var re = new RegExp(regu);
        if (re.test(str)) { return true; } else { return false; }
    }
    //正整数
    if (parseInt(action) == 3) {
        var regu = /^[1-9]\d*$/;
        var re = new RegExp(regu);
        if (re.test(str)) { return true; } else { return false; }
    }
    //验证码
    if (parseInt(action) == 4) {
        var regu = /^[\d]{6}/;
        var re = new RegExp(regu);
        if (re.test(str)) { return true; } else { return false; }
    }
    //密码
    if (parseInt(action) == 5) {
        var regu = /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\\W_!@#$%^&*`~()-+=]+$)(?![a-z0-9]+$)(?![a-z\\W_!@#$%^&*`~()-+=]+$)(?![0-9\\W_!@#$%^&*`~()-+=]+$)[a-zA-Z0-9\\W_!@#$%^&*`~()-+=]{6,18}$/;
        var re = new RegExp(regu);
        if (re.test(str)) { return true; } else { return false; }
    }
    //手机号+座机
    if (parseInt(action) == 6) {
        var regu = /^(0[0-9]{2,3}\-)?([2-9][0-9]{6,7})+(\-[0-9]{1,4})?$|(^1\d{10}$)/;
        var re = new RegExp(regu);
        if (re.test(str)) { return true; } else { return false; }
    }
    if (parseInt(action) == 7) {
        var regu = /^[1-9]\d*|0$/;
        var re = new RegExp(regu);
        if (re.test(str)) { return true; } else { return false; }
    }
    //身份证号码
    if (parseInt(action) == 8) {
        var regu = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
        var re = new RegExp(regu);
        if (re.test(str)) { return true; } else { return false; }
    }
    //任意英文+数字
    if (parseInt(action) == 9) {
        var regu = /^[A-Za-z0-9]+$/;
        var re = new RegExp(regu);
        if (re.test(str)) { return true; } else { return false; }
    }
    //邮箱
    if (parseInt(action) == 10) {
        var regu = /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;
        var re = new RegExp(regu);
        if (re.test(str)) { return true; } else { return false; }
    }
    //手机号+邮箱
    if (parseInt(action) == 11) {
        var reg1 = /^1\d{10}$/;
        var reg2 = /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;
        var re1 = new RegExp(reg1);
        var re2 = new RegExp(reg2);
        var r1 = false; var r2 = false;
        if (re1.test(str)) { r1 = true; }
        if (re2.test(str)) { r2 = true; }
        if (r1 || r2) { return true; } else { return false; }
    }
    //银行卡号
    if (parseInt(action) == 12) {
        var regu = /^\d{16}|\d{19}$/;
        var re = new RegExp(regu);
        if (re.test(str)) { return true; } else { return false; }
    }
    //金额
    if (parseInt(action) == 13) {
        var regu = /(^[1-9](\d+)?(\.\d{1,2})?$)|(^[1-9]$)|(^\d\.[1-9]{1,2}$)|(^\d\.[0]{1}[1-9]{1}$|(^\d\.[1-9]{1}[0]{1}$)$)/;
        var re = new RegExp(regu);
        if (re.test(str)) { return true; } else { return false; }
    }
}

function hrefMobile() {
    GetAjaxData({ _t: new Date() }, "/Safe/PostBindMobileVisit").then(function (AjaxData) {
        if (!AjaxData) { opAlert(2, '请求异常'); }
        else {
            if (AjaxData.Status == 1) {
                if (AjaxData.data.IsFirst == 1) {
                    $.confirm({
                        title: "提示",
                        content: "老手机号是否在使用？",
                        icon: "fa fa-question",
                        closeIcon: true,
                        theme: 'white',
                        animation: 'scale',
                        type: 'orange',
                        backgroundDismiss: false,
                        buttons: {
                            "confirm": {
                                text: '是',
                                btnClass: 'btn-red',
                                action: function () { location.href = "/Safe/UnBindMobile?Mobile=" + AjaxData.data.Mobile; }
                            },
                            cancel: {
                                text: "否",
                                action: function () {
                                    // 创建form元素
                                    var temp_form = document.createElement("form");
                                    temp_form.action = "/Safe/UnBindMobile";
                                    temp_form.target = "_self";
                                    temp_form.method = "post";
                                    temp_form.style.display = "none";
                                    var opt = document.createElement("textarea");
                                    opt.name = "ChangeMsg";
                                    opt.value = AjaxData.data.ChangeMsg;
                                    temp_form.appendChild(opt);
                                    document.body.appendChild(temp_form);   
                                    temp_form.submit(); 

                                }
                            }
                        }
                    });
                } else {
                    location.href = "/Safe/EditMobile"; 
                }
            }
            else {
                opAlert(2, AjaxData.TipMsg, AjaxData.GoNext);
            }
        }
    });
}