package com.jeethink.project.system.player.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * 数据库连接测试工具
 * 用于测试SQL Server连接是否正常
 * 
 * <AUTHOR>
 */
public class DatabaseConnectionTest {
    
    // SQL Server连接信息
    private static final String SQL_SERVER_URL = "***********************************************************************************************";
    private static final String SQL_SERVER_USERNAME = "sa";
    private static final String SQL_SERVER_PASSWORD = "bgroigmroAD147258JFDJGBHjhdhf";
    
    public static void main(String[] args) {
        testSqlServerConnection();
    }
    
    /**
     * 测试SQL Server连接
     */
    public static void testSqlServerConnection() {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        
        try {
            // 加载SQL Server驱动
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            
            System.out.println("正在连接SQL Server数据库...");
            System.out.println("URL: " + SQL_SERVER_URL);
            System.out.println("Username: " + SQL_SERVER_USERNAME);
            
            // 建立连接
            conn = DriverManager.getConnection(SQL_SERVER_URL, SQL_SERVER_USERNAME, SQL_SERVER_PASSWORD);
            System.out.println("✅ SQL Server连接成功！");
            
            // 测试查询user_account表
            stmt = conn.createStatement();
            
            // 1. 测试user_account表是否存在
            System.out.println("\n--- 测试user_account表 ---");
            String sql1 = "SELECT COUNT(*) as total FROM user_account";
            rs = stmt.executeQuery(sql1);
            if (rs.next()) {
                int total = rs.getInt("total");
                System.out.println("✅ user_account表存在，总记录数: " + total);
            }
            rs.close();
            
            // 2. 测试ssn表是否存在
            System.out.println("\n--- 测试ssn表 ---");
            String sql2 = "SELECT COUNT(*) as total FROM ssn";
            rs = stmt.executeQuery(sql2);
            if (rs.next()) {
                int total = rs.getInt("total");
                System.out.println("✅ ssn表存在，总记录数: " + total);
            }
            rs.close();
            
            // 3. 测试在线玩家统计
            System.out.println("\n--- 测试在线玩家统计 ---");
            String sql3 = "SELECT COUNT(*) as online_count FROM user_account WHERE (last_login > last_logout OR last_logout IS NULL) AND last_login IS NOT NULL";
            rs = stmt.executeQuery(sql3);
            if (rs.next()) {
                int onlineCount = rs.getInt("online_count");
                System.out.println("✅ 当前在线玩家数量: " + onlineCount);
            }
            rs.close();
            
            // 4. 测试今日注册统计
            System.out.println("\n--- 测试今日注册统计 ---");
            String sql4 = "SELECT COUNT(*) as today_reg FROM ssn WHERE CAST(reg_date AS DATE) = CAST(GETDATE() AS DATE)";
            rs = stmt.executeQuery(sql4);
            if (rs.next()) {
                int todayReg = rs.getInt("today_reg");
                System.out.println("✅ 今日注册账号数量: " + todayReg);
            }
            rs.close();
            
            // 5. 测试邀请码数据
            System.out.println("\n--- 测试邀请码数据 ---");
            String sql5 = "SELECT TOP 5 invitation, COUNT(*) as player_count FROM user_account WHERE invitation IS NOT NULL AND invitation != '' GROUP BY invitation ORDER BY player_count DESC";
            rs = stmt.executeQuery(sql5);
            System.out.println("邀请码统计（前5名）:");
            while (rs.next()) {
                String invitation = rs.getString("invitation");
                int playerCount = rs.getInt("player_count");
                System.out.println("  " + invitation + ": " + playerCount + " 个玩家");
            }
            rs.close();
            
            // 6. 测试关联查询
            System.out.println("\n--- 测试关联查询 ---");
            String sql6 = "SELECT TOP 3 ua.account, ua.invitation, ua.last_login, s.reg_date FROM user_account ua LEFT JOIN ssn s ON ua.account = s.name WHERE ua.invitation IS NOT NULL ORDER BY ua.last_login DESC";
            rs = stmt.executeQuery(sql6);
            System.out.println("最近登录的玩家（前3名）:");
            while (rs.next()) {
                String account = rs.getString("account");
                String invitation = rs.getString("invitation");
                String lastLogin = rs.getString("last_login");
                String regDate = rs.getString("reg_date");
                System.out.println("  账号: " + account + ", 邀请码: " + invitation + ", 最后登录: " + lastLogin + ", 注册时间: " + regDate);
            }
            rs.close();
            
            System.out.println("\n🎉 所有测试完成，数据库连接和查询正常！");
            
        } catch (Exception e) {
            System.err.println("❌ 数据库连接测试失败:");
            e.printStackTrace();
            
            // 提供常见问题的解决建议
            System.err.println("\n💡 常见问题解决建议:");
            System.err.println("1. 检查SQL Server服务是否运行");
            System.err.println("2. 检查IP地址和端口是否正确");
            System.err.println("3. 检查用户名和密码是否正确");
            System.err.println("4. 检查防火墙是否允许1433端口");
            System.err.println("5. 检查SQL Server是否启用TCP/IP协议");
            System.err.println("6. 确保项目中包含mssql-jdbc依赖");
            
        } finally {
            // 关闭资源
            try {
                if (rs != null) rs.close();
                if (stmt != null) stmt.close();
                if (conn != null) conn.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
