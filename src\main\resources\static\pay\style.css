@font-face {
    font-family: 'Avenir';
    src: url("../fonts/AvenirNext-Regular/AvenirNext-Regular.eot");
    src: local('☺'), url("../fonts/AvenirNext-Regular/AvenirNext-Regular.woff") format("woff"), url("../fonts/AvenirNext-Regular/AvenirNext-Regular.ttf") format("truetype"), url("../fonts/AvenirNext-Regular/AvenirNext-Regulard41d.eot?#iefix")format("embedded-opentype");
    font-weight: 400;
}

@font-face {
    font-family: 'Avenir';
    src: url("../fonts/AvenirNext-DemiBold/AvenirNext-DemiBold.eot");
    src: local('☺'), url("../fonts/AvenirNext-DemiBold/AvenirNext-DemiBold.woff") format("woff"), url("../fonts/AvenirNext-DemiBold/AvenirNext-DemiBold.ttf") format("truetype"), url("../fonts/AvenirNext-DemiBold/AvenirNext-DemiBoldd41d.eot?#iefix")format("embedded-opentype");
    font-weight: 700;
}

@font-face {
    font-family: 'Avenir';
    src: url("../fonts/AvenirNext-Bold/AvenirNext-Bold.eot");
    src: local('☺'), url("../fonts/AvenirNext-Bold/AvenirNext-Bold.woff") format("woff"), url("../fonts/AvenirNext-Bold/AvenirNext-Bold.ttf") format("truetype"), url("../fonts/AvenirNext-Bold/AvenirNext-Boldd41d.eot?#iefix")format("embedded-opentype");
    font-weight: 800;
}

@font-face {
    font-family: 'RussoOne';
    src: url("../fonts/Russo_One/Russo_One.eot");
    src: local('☺'), url("../fonts/Russo_One/Russo_One.woff") format("woff"), url("../fonts/Russo_One/Russo_One.ttf") format("truetype"), url("../fonts/Russo_One/Russo_Oned41d.eot?#iefix")format("embedded-opentype");
    font-weight: normal;
}

/* CSS Reset */

html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */

article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
    display: block;
}

body {
    line-height: 1;
}

ol, ul {
    list-style: none;
}

blockquote, q {
    quotes: none;
}

blockquote:before, blockquote:after, q:before, q:after {
    content: '';
    content: none;
}

table {
    border-collapse: separate;
    border-spacing: 3px;
}

/* End of CSS Reset */

* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

html {
    height: 100%;
    width: 100%;
}

article, aside, details, figcaption, figure, footer, header, hgroup, main, nav, section, summary {
    display: block;
}

body {
    font-size: 18px;
    line-height: 1.4;
    font-family: 'Avenir', sans-serif;
    font-weight: 700;
    color: #120c3b;
    /*
    background: #0d0b0e;
    background-image: url("../images/bg.jpg");
    background-position: top center;
    background-repeat: no-repeat;
    */
    width: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /*animation: bluring 3s infinite linear;*/
}

body.inner {
    background-image: url("../images/bg-inner.jpg");
}

body.soon {
    width: 100%;
    height: 100%;
    background-color: #000;
    background: url("../images/soon.jpg") center center no-repeat;
    background-size: cover;
}

p {
    margin: 0 0 15px;
}