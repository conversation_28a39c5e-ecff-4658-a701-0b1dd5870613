<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeethink.project.system.shipments.mapper.ShipmentsMapper">
    
    <resultMap type="Shipments" id="ShipmentsResult">
        <result property="sid"    column="sid"    />
        <result property="sname"    column="sname"    />
        <result property="wpid"    column="wpid"    />
        <result property="wpname"    column="wpname"    />
        <result property="qhValue"    column="qh_value"    />
        <result property="snumber"    column="snumber"    />
        <result property="sause"    column="sause"    />
        <result property="stime"    column="stime"    />
        <result property="status"    column="status"    />
    </resultMap>


    <sql id="selectShipmentsVo">
        select sid, sname, wpname,wpid, qh_value, snumber, sause, stime, status from shipments
    </sql>
    <select id="selectShipmentsList" parameterType="Shipments" resultMap="ShipmentsResult">
        <include refid="selectShipmentsVo"/>
        <where>  
            <if test="sname != null  and sname != ''"> and sname like concat('%', #{sname}, '%')</if>
            <if test="wpname != null  and wpname != ''"> and wpname like concat('%', #{wpname}, '%')</if>
            <if test="wpid != null "> and wpid = #{wpid}</if>
            <if test="qhValue != null  and qhValue != ''"> and qh_value = #{qhValue}</if>
            <if test="snumber != null "> and snumber = #{snumber}</if>
            <if test="sause != null  and sause != ''"> and sause = #{sause}</if>
            <if test="stime != null "> and stime = #{stime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectShipmentsById" parameterType="Long" resultMap="ShipmentsResult">
        <include refid="selectShipmentsVo"/>
        where sid = #{sid}
    </select>
        
    <insert id="insertShipments" parameterType="Shipments">
        insert into shipments
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sid != null">sid,</if>
            <if test="sname != null">sname,</if>
            <if test="wpid != null">wpid,</if>
            <if test="wpname != null">wpname,</if>
            <if test="qhValue != null">qh_value,</if>
            <if test="snumber != null">snumber,</if>
            <if test="sause != null">sause,</if>
            <if test="stime != null">stime,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sid != null">#{sid},</if>
            <if test="sname != null">#{sname},</if>
            <if test="wpid != null">#{wpid},</if>
            <if test="wpname != null">#{wpname},</if>
            <if test="qhValue != null">#{qhValue},</if>
            <if test="snumber != null">#{snumber},</if>
            <if test="sause != null">#{sause},</if>
            <if test="stime != null">#{stime},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateShipments" parameterType="Shipments">
        update shipments
        <trim prefix="SET" suffixOverrides=",">
            <if test="sname != null">sname = #{sname},</if>
            <if test="wpid != null">wpid = #{wpid},</if>
            <if test="wpname != null">wpname = #{wpname},</if>
            <if test="qhValue != null">qh_value = #{qhValue},</if>
            <if test="snumber != null">snumber = #{snumber},</if>
            <if test="sause != null">sause = #{sause},</if>
            <if test="stime != null">stime = #{stime},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where sid = #{sid}
    </update>

    <delete id="deleteShipmentsById" parameterType="Long">
        delete from shipments where sid = #{sid}
    </delete>

    <delete id="deleteShipmentsByIds" parameterType="String">
        delete from shipments where sid in 
        <foreach item="sid" collection="array" open="(" separator="," close=")">
            #{sid}
        </foreach>
    </delete>

</mapper>