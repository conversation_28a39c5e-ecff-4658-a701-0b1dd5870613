# 数据库表和关键字段统计

## 概述
本文档统计了账单显示代码逻辑和账单新增代码逻辑中涉及到的所有数据库表和关键字段。

## 涉及的数据库表

### 1. Pay表 (主要核心表)

**表名**: `pay`

**表用途**: 存储支付订单记录，是系统的核心支付数据表

**关键字段详情**:

| 字段名 | 字段类型 | 说明 | 在业务中的作用 |
|--------|----------|------|----------------|
| `id` | int | 主键ID | 唯一标识每条支付记录 |
| `mch_order_no` | varchar | 商户订单号 | 系统生成的唯一订单号，用于订单查询和回调匹配 |
| `area` | varchar | 区服号 | 标识用户所在的游戏区服 |
| `channel_code` | varchar | 支付渠道代码 | 支付方式标识(WECHAT/ALIPAY/wxpay/alipay) |
| `amount` | bigint/decimal | 支付金额 | 订单金额，用于收入统计计算 |
| `goods_param_ext` | varchar | 商品描述/角色名 | 支付商品的描述信息 |
| `ip` | varchar | 用户IP地址 | 记录支付发起时的用户IP |
| `status` | varchar | 支付状态 | 关键状态字段("未支付"/"支付成功")，用于统计过滤 |
| `pay_succ_time` | datetime | 支付时间 | 支付发起时间 |
| `deliver_goods` | varchar | 发货状态 | 商品发货状态标识 |
| `create_by` | varchar | 创建人 | 记录创建者 |
| `agency` | varchar | 代理信息 | **核心权限控制字段**，关联用户的invite字段 |
| `create_time` | datetime | 创建时间 | **重要时间字段**，用于月/年收入统计的时间范围过滤 |
| `update_by` | varchar | 更新人 | 记录更新者 |
| `update_time` | datetime | 更新时间 | 记录最后更新时间 |
| `wuDi_order_no` | varchar | 第三方平台订单号 | 支付成功后由第三方平台返回的订单号 |

**重要业务特性**:
- `agency`字段使用utf8_general_ci排序规则，**大小写不敏感**
- `status`字段只有"支付成功"状态的记录才会被统计到收入中
- `create_time`字段是统计查询的核心时间维度

### 2. User表 (用户权限控制表)

**表名**: `user` (推测表名，可能是sys_user)

**表用途**: 存储用户信息，主要用于权限控制和代理关系管理

**关键字段详情**:

| 字段名 | 字段类型 | 说明 | 在业务中的作用 |
|--------|----------|------|----------------|
| `login_name` | varchar | 用户登录名 | **权限判断核心字段**，"admin"用户可查看所有数据 |
| `invite` | varchar | 用户邀请码/代理标识 | **关联Pay表agency字段**，用于数据权限过滤 |

**权限控制逻辑**:
- `login_name = "admin"`: 管理员用户，可查看所有支付数据
- `login_name ≠ "admin"`: 普通用户，只能查看`agency = user.invite`的支付数据

## 数据表关联关系

### 1. Pay表与User表的关联
```sql
-- 权限控制关联逻辑
Pay.agency = User.invite
```

**关联说明**:
- 非admin用户只能查看自己作为代理人(agency)的支付记录
- 通过User表的invite字段与Pay表的agency字段进行关联
- 实现了数据权限的隔离和控制

## 核心SQL查询模式

### 1. 订单列表查询 (selectPaySystemList)
```sql
SELECT id, mch_order_no, area, channel_code, amount, goods_param_ext,
       ip, status, pay_succ_time, deliver_goods, create_by, agency,
       create_time, update_by, update_time, wuDi_order_no
FROM pay
WHERE 1=1
  AND (agency = #{agency} OR agency IS NULL)  -- 代理权限过滤
  AND status = #{status}                      -- 状态过滤
  AND date_format(create_time,'%y%m%d') >= date_format(#{params.beginCreateTime},'%y%m%d')
  AND date_format(create_time,'%y%m%d') <= date_format(#{params.endCreateTime},'%y%m%d')
```

### 2. 收入总额查询 (selectPaySum)
```sql
SELECT SUM(amount) as amount
FROM pay
WHERE 1=1
  AND (agency = #{agency} OR agency IS NULL)  -- 代理权限过滤
  AND status = '支付成功'                     -- 只统计成功支付
  AND date_format(create_time,'%y%m%d') >= date_format(#{params.beginCreateTime},'%y%m%d')
  AND date_format(create_time,'%y%m%d') <= date_format(#{params.endCreateTime},'%y%m%d')
```

## 数据操作类型统计

### 1. 查询操作 (账单显示功能)
**涉及表**: Pay表、User表
**主要字段**:
- **过滤字段**: `agency`, `status`, `create_time`
- **统计字段**: `amount` (SUM聚合)
- **权限字段**: `login_name`, `invite`

### 2. 插入操作 (账单新增功能)
**涉及表**: 仅Pay表
**插入字段**:
- `mch_order_no` (必填)
- `channel_code` (必填)
- `amount` (必填)
- `goods_param_ext`
- `ip`
- `area`
- `status` (默认"未支付")
- `pay_succ_time`
- `create_time` (自动设置)

### 3. 更新操作 (支付回调功能)
**涉及表**: 仅Pay表
**更新字段**:
- `status`: "未支付" → "支付成功"
- `wuDi_order_no`: 设置第三方平台订单号
- `update_time`: 自动更新为当前时间

## 业务数据流转

### 1. 支付发起阶段
```
用户提交支付 → Pay表插入新记录 → status="未支付"
```

### 2. 支付完成阶段
```
第三方回调 → 更新Pay表 → status="支付成功" + wuDi_order_no
```

### 3. 数据统计阶段
```
首页查询 → 根据用户权限过滤 → 按时间范围统计 → 返回统计结果
```

## 重要发现和注意事项

### 1. 权限控制机制
- **双重安全**: 前端传参 + 后端强制覆盖
- **数据隔离**: 通过agency字段实现用户数据隔离
- **管理员特权**: admin用户可查看全部数据

### 2. 数据一致性
- **大小写不敏感**: agency字段比较不区分大小写
- **状态依赖**: 收入统计完全依赖status="支付成功"
- **时间维度**: 统计基于create_time而非update_time

### 3. 系统设计特点
- **单表核心**: Pay表承载了所有核心业务数据
- **简化设计**: 没有复杂的多表关联和事务处理
- **权限优先**: 数据安全通过权限控制而非物理隔离

## 总结

本系统涉及的核心数据库表只有2个：
1. **Pay表** - 核心业务数据表，包含17个关键字段
2. **User表** - 用户权限控制表，包含2个关键字段

系统设计简洁高效，通过Pay表的agency字段与User表的invite字段关联，实现了完整的数据权限控制和业务功能。所有的支付、统计、权限控制都围绕这两个表展开。
