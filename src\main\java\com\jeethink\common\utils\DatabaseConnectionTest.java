package com.jeethink.common.utils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.SQLException;

/**
 * 数据库连接测试工具类
 * 
 * <AUTHOR>
 */
public class DatabaseConnectionTest {
    
    /**
     * 测试SQL Server数据库连接
     * 
     * @param host 数据库主机地址
     * @param port 数据库端口
     * @param database 数据库名称
     * @param username 用户名
     * @param password 密码
     * @return 连接测试结果
     */
    public static ConnectionResult testSqlServerConnection(String host, int port, String database, String username, String password) {
        String url = String.format("********************************************************************************", 
                                   host, port, database);
        return testConnection(url, username, password, "SQL Server");
    }
    
    /**
     * 测试MySQL数据库连接
     * 
     * @param host 数据库主机地址
     * @param port 数据库端口
     * @param database 数据库名称
     * @param username 用户名
     * @param password 密码
     * @return 连接测试结果
     */
    public static ConnectionResult testMySqlConnection(String host, int port, String database, String username, String password) {
        String url = String.format("************************************************************************************************************************************", 
                                   host, port, database);
        return testConnection(url, username, password, "MySQL");
    }
    
    /**
     * 通用数据库连接测试方法
     *
     * @param url 数据库连接URL
     * @param username 用户名
     * @param password 密码
     * @param dbType 数据库类型
     * @return 连接测试结果
     */
    private static ConnectionResult testConnection(String url, String username, String password, String dbType) {
        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;

        try {
            // 显式加载驱动程序
            if ("SQL Server".equals(dbType)) {
                Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            } else if ("MySQL".equals(dbType)) {
                Class.forName("com.mysql.cj.jdbc.Driver");
            }

            // 尝试建立连接
            connection = DriverManager.getConnection(url, username, password);
            
            if (connection != null && !connection.isClosed()) {
                // 执行简单查询测试
                statement = connection.createStatement();
                
                String testQuery;
                if ("SQL Server".equals(dbType)) {
                    testQuery = "SELECT 1 AS test_result";
                } else {
                    testQuery = "SELECT 1 AS test_result";
                }
                
                resultSet = statement.executeQuery(testQuery);
                
                if (resultSet.next()) {
                    return new ConnectionResult(true, 
                        String.format("%s数据库连接成功！\n连接URL: %s\n用户名: %s", dbType, url, username), 
                        null);
                } else {
                    return new ConnectionResult(false, 
                        String.format("%s数据库连接成功，但查询测试失败", dbType), 
                        null);
                }
            } else {
                return new ConnectionResult(false, 
                    String.format("%s数据库连接失败：连接为空或已关闭", dbType), 
                    null);
            }
            
        } catch (SQLException e) {
            String errorMessage = String.format("%s数据库连接失败：%s\n连接URL: %s\n用户名: %s",
                                               dbType, e.getMessage(), url, username);
            return new ConnectionResult(false, errorMessage, e);
        } catch (ClassNotFoundException e) {
            String errorMessage = String.format("%s数据库驱动程序未找到：%s", dbType, e.getMessage());
            return new ConnectionResult(false, errorMessage, e);
            
        } finally {
            // 关闭资源
            try {
                if (resultSet != null) resultSet.close();
                if (statement != null) statement.close();
                if (connection != null) connection.close();
            } catch (SQLException e) {
                System.err.println("关闭数据库连接时发生错误: " + e.getMessage());
            }
        }
    }
    
    /**
     * 连接测试结果类
     */
    public static class ConnectionResult {
        private boolean success;
        private String message;
        private Exception exception;
        
        public ConnectionResult(boolean success, String message, Exception exception) {
            this.success = success;
            this.message = message;
            this.exception = exception;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public String getMessage() {
            return message;
        }
        
        public Exception getException() {
            return exception;
        }
        
        @Override
        public String toString() {
            return String.format("ConnectionResult{success=%s, message='%s'}", success, message);
        }
    }
    
    /**
     * 主方法，用于测试
     */
    public static void main(String[] args) {
        // 测试您提供的SQL Server数据库配置
        System.out.println("=== 测试SQL Server数据库连接 ===");
        ConnectionResult sqlServerResult = testSqlServerConnection(
            "110.42.3.94", 
            1433, 
            "lin2db", 
            "sa", 
            "bgroigmroAD147258JFDJGBHjhdhf"
        );
        
        System.out.println("SQL Server连接结果:");
        System.out.println(sqlServerResult.getMessage());
        if (!sqlServerResult.isSuccess() && sqlServerResult.getException() != null) {
            System.out.println("异常详情:");
            sqlServerResult.getException().printStackTrace();
        }
        
        System.out.println("\n=== 测试项目默认MySQL数据库连接 ===");
        ConnectionResult mysqlResult = testMySqlConnection(
            "localhost", 
            3306, 
            "jeethink", 
            "root", 
            "jad198611"
        );
        
        System.out.println("MySQL连接结果:");
        System.out.println(mysqlResult.getMessage());
        if (!mysqlResult.isSuccess() && mysqlResult.getException() != null) {
            System.out.println("异常详情:");
            mysqlResult.getException().printStackTrace();
        }
    }
}
