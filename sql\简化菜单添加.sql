-- 玩家统计菜单权限修复脚本
-- 菜单已存在，只需要为其他角色添加权限

-- 检查现有菜单（玩家统计菜单ID: 2060-2069）
-- SELECT menu_id, menu_name, parent_id FROM sys_menu WHERE menu_id BETWEEN 2060 AND 2069;

-- 为"主播"角色（role_id=2）添加玩家统计权限

-- 为"主播"角色（role_id=2）添加玩家统计权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(2, 2060),  -- 玩家统计主菜单
(2, 2061),  -- 玩家查询
(2, 2062),  -- 玩家统计
(2, 2063),  -- 玩家详情
(2, 2064),  -- 在线统计
(2, 2065);  -- 注册统计

-- 为"技术"角色（role_id=100）添加玩家统计权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(100, 2060),  -- 玩家统计主菜单
(100, 2061),  -- 玩家查询
(100, 2062),  -- 玩家统计
(100, 2063),  -- 玩家详情
(100, 2064),  -- 在线统计
(100, 2065),  -- 注册统计
(100, 2066),  -- 邀请码查询（技术角色可以有更多权限）
(100, 2067),  -- 邀请码统计
(100, 2068),  -- 邀请码列表
(100, 2069);  -- 玩家导出

-- 验证权限分配结果
SELECT r.role_name, m.menu_name, m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.menu_id BETWEEN 2060 AND 2069
ORDER BY r.role_name, m.order_num;

-- 检查是否有重复权限（避免重复插入）
-- 如果执行前想检查，可以运行：
-- SELECT rm.role_id, rm.menu_id FROM sys_role_menu rm WHERE rm.menu_id BETWEEN 2060 AND 2069 AND rm.role_id IN (2, 100);
