package com.jeethink.project.system.pay.mapper;

import java.util.List;
import com.jeethink.project.system.pay.domain.Pay;

/**
 * 支付Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-02
 */
public interface PayMapper 
{
    /**
     * 查询支付
     * 
     * @param id 支付ID
     * @return 支付
     */
    public Pay selectPayById(Long id);

    /**
     * 查询支付列表
     * 
     * @param pay 支付
     * @return 支付集合
     */
    public List<Pay> selectPayList(Pay pay);

    public Pay selectByNoId(String id);

    /**
     * 新增支付
     * 
     * @param pay 支付
     * @return 结果
     */
    public int insertPay(Pay pay);

    /**
     * 修改支付
     * 
     * @param pay 支付
     * @return 结果
     */
    public int updatePay(Pay pay);

    /**
     * 删除支付
     * 
     * @param id 支付ID
     * @return 结果
     */
    public int deletePayById(Long id);

    /**
     * 批量删除支付
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deletePayByIds(String[] ids);
}
