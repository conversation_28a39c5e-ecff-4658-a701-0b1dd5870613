# 当前文件功能介绍
当前这个文件就是用于统计新添加的后端的接口
方便再以后的编程过程中能够利用这里的记录，快速的编写前端的代码

# 接口文档

## 玩家统计模块接口

### 1. 玩家统计页面
- **接口地址**: `GET /system/player`
- **接口描述**: 返回玩家统计页面
- **权限要求**: `system:player:view`
- **返回**: 玩家统计页面HTML

### 2. 查询玩家列表
- **接口地址**: `POST /system/player/list`
- **接口描述**: 分页查询玩家列表，支持权限控制
- **权限要求**: `system:player:list`
- **请求参数**:
  ```json
  {
    "account": "玩家账号（可选）",
    "invitation": "邀请码（可选）",
    "lastIp": "IP地址（可选）",
    "pageNum": 1,
    "pageSize": 10
  }
  ```
- **返回数据**:
  ```json
  {
    "total": 100,
    "rows": [
      {
        "uid": 1,
        "account": "player001",
        "onlineStatus": "在线",
        "invitation": "A008",
        "lastLogin": "2025-07-25 23:56:20",
        "lastLogout": "2025-07-25 23:56:14",
        "regDate": "2025-07-20 19:52:15",
        "lastIp": "***********"
      }
    ],
    "code": 0,
    "msg": "查询成功"
  }
  ```
- **权限说明**:
  - admin用户：可查看所有玩家数据
  - 普通用户：只能查看自己邀请码的玩家数据

### 3. 获取玩家统计信息
- **接口地址**: `POST /system/player/stats`
- **接口描述**: 获取玩家统计信息（在线数量、今日注册数量）
- **权限要求**: `system:player:stats`
- **返回数据**:
  ```json
  {
    "onlineCount": 11,
    "todayRegisterCount": 4,
    "totalPlayerCount": 150
  }
  ```
- **权限说明**:
  - admin用户：返回全平台统计数据
  - 普通用户：返回自己邀请码的统计数据

### 4. 根据邀请码查询玩家列表（admin专用）
- **接口地址**: `POST /system/player/listByInvitation`
- **接口描述**: 根据指定邀请码查询玩家列表
- **权限要求**: `system:player:listByInvitation`
- **请求参数**:
  ```json
  {
    "invitation": "A008"
  }
  ```
- **返回数据**: 同玩家列表接口
- **权限说明**: 仅admin用户可使用

### 5. 根据邀请码获取统计信息（admin专用）
- **接口地址**: `POST /system/player/statsByInvitation`
- **接口描述**: 获取指定邀请码的统计信息
- **权限要求**: `system:player:statsByInvitation`
- **请求参数**:
  ```json
  {
    "invitation": "A008"
  }
  ```
- **返回数据**: 同统计信息接口
- **权限说明**: 仅admin用户可使用

### 6. 获取所有邀请码列表（admin专用）
- **接口地址**: `POST /system/player/invitations`
- **接口描述**: 获取系统中所有邀请码列表
- **权限要求**: `system:player:invitations`
- **返回数据**:
  ```json
  {
    "code": 0,
    "msg": "查询成功",
    "data": ["A008", "A111", "A110"]
  }
  ```
- **权限说明**: 仅admin用户可使用

### 7. 查询玩家详情
- **接口地址**: `GET /system/player/detail/{account}`
- **接口描述**: 根据账户名查询玩家详细信息
- **权限要求**: `system:player:detail`
- **路径参数**: `account` - 玩家账户名
- **返回数据**:
  ```json
  {
    "code": 0,
    "msg": "查询成功",
    "data": {
      "uid": 1,
      "account": "player001",
      "onlineStatus": "在线",
      "invitation": "A008",
      "lastLogin": "2025-07-25 23:56:20",
      "lastLogout": "2025-07-25 23:56:14",
      "regDate": "2025-07-20 19:52:15",
      "lastIp": "***********"
    }
  }
  ```
- **权限说明**:
  - admin用户：可查看任意玩家详情
  - 普通用户：只能查看自己邀请码的玩家详情

### 8. 实时获取在线玩家数量
- **接口地址**: `POST /system/player/onlineCount`
- **接口描述**: 实时获取在线玩家数量
- **权限要求**: `system:player:onlineCount`
- **返回数据**:
  ```json
  {
    "code": 0,
    "msg": "在线玩家数量",
    "data": 11
  }
  ```

### 9. 实时获取今日注册数量
- **接口地址**: `POST /system/player/todayRegCount`
- **接口描述**: 实时获取今日注册账号数量
- **权限要求**: `system:player:todayRegCount`
- **返回数据**:
  ```json
  {
    "code": 0,
    "msg": "今日注册数量",
    "data": 4
  }
  ```

## 数据库设计说明

### SQL Server数据源配置
- **数据库**: lin2db (游戏账户数据库)
- **主要表**:
  - `user_account`: 玩家账户信息表
  - `ssn`: 玩家注册信息表

### 关键字段说明
- **user_account表**:
  - `account`: 玩家账户名
  - `last_login`: 最后登录时间
  - `last_logout`: 最后登出时间
  - `invitation`: 邀请码（关联MySQL user表的invite字段）
- **ssn表**:
  - `name`: 玩家账户名（关联user_account.account）
  - `reg_date`: 注册时间

### 权限控制逻辑
- **大小写忽略**: 所有邀请码比较都使用UPPER()函数忽略大小写
- **数据隔离**: 普通用户只能查看invitation字段匹配自己invite的数据
- **在线判断**: `last_login > last_logout OR last_logout IS NULL` 表示在线

## 前端集成说明

### 页面路径
- 玩家统计页面: `/system/player`
- 模板文件: `src/main/resources/templates/system/player/player.html`

### JavaScript函数
- `refreshStats()`: 刷新统计信息
- `viewDetail(account)`: 查看玩家详情
- 自动刷新: 每30秒自动刷新统计数据

### 菜单配置
需要在数据库中添加相应的菜单项和权限配置，参考 `sql/player_menu.sql` 文件。