$(function () {
	function commonChange(tabObj,tabEvent,tabCont,tabName){
		$(tabObj).bind(tabEvent,function(){
			var index=$(this).index();
			$(this).addClass(tabName).siblings().removeClass(tabName);
			$(tabCont).hide();
			$(tabCont).eq(index).show();
		});
	}
    commonChange('#pcSidebar ul li','click','','cur');//登录切换
    commonChange('.con-list span', 'click', '', 'cur');//充值方式
 
	 
	 //获取短信验证码	            
    function getCode(obj,event){
    	var validCode=true;
    	obj.on(event,function(){
    		 var time=60;
             var $code=$(this);
             if (validCode) {
               validCode=false;
                var t=setInterval(function  () {
                     time--;
                     $code.val(time+"s后获取");
                     $code.attr("disabled",true); 
                     $code.css('opacity','0.6');
                   if (time==0) {
                        clearInterval(t);
                     	$code.val("获取验证码");
                     	$code.removeAttr("disabled"); 
                     	$code.css('opacity','1');
                        validCode=true;
                    }
                },1000)
              }
    	});   	
    }    
   // getCode($('.btn-vcode'),'click');//获取验证码

    //function textInput(obj) {
    //    obj.each(function () {
    //        $(this).css('color', '#999');
    //        $(this).bind('focus', function () {
    //            $(this).css('color', '#fff');
    //        });
    //    });
    //}
    //textInput($(".con-form .form-control"));//文本框获取焦点为白色

//图片尺寸计算
function clacImgZoomParam(maxWidth, maxHeight, width, height) {
    var param = { top: 0, left: 0, width: width, height: height };
    if (width > maxWidth || height > maxHeight) {
        rateWidth = width / maxWidth;
        rateHeight = height / maxHeight;

        if (rateWidth > rateHeight) {
            param.width = maxWidth;
            param.height = Math.round(height / rateWidth);
        } else {
            param.width = Math.round(width / rateHeight);
            param.height = maxHeight;
        }
    }

    param.left = Math.round((maxWidth - param.width) / 2);
    param.top = Math.round((maxHeight - param.height) / 2);
    return param;
}
//图片上传预览IE是用了滤镜。
function previewImage(file, putObjId) {
    var MAXWIDTH = 120;
    var MAXHEIGHT = 120;
    var div = document.getElementById(putObjId);
    if (file.files && file.files[0]) {
        div.innerHTML = '<img id=imghead' + putObjId + '>';
        var imgId = 'imghead' + putObjId;
        var img = document.getElementById(imgId);
        img.onload = function () {
            var rect = clacImgZoomParam(MAXWIDTH, MAXHEIGHT, img.offsetWidth, img.offsetHeight);
            img.width = rect.width;
            img.height = rect.height;
            img.style.marginTop = rect.top + 'px';
        }
        var reader = new FileReader();
        reader.onload = function (evt) {
            img.src = evt.target.result;
            $("#hidImg").val(evt.target.result);
            formValidateForSelect("myform","btnSubmit");
        }
        reader.readAsDataURL(file.files[0]);
    }
    else //兼容IE
    {
        var sFilter = 'filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale,src="';
        file.select();
        var src = document.selection.createRange().text;
        div.innerHTML = '<img id=imghead>';
        var img = document.getElementById('imghead');
        img.filters.item('DXImageTransform.Microsoft.AlphaImageLoader').src = src;
        var rect = clacImgZoomParam(MAXWIDTH, MAXHEIGHT, img.offsetWidth, img.offsetHeight);
        status = ('rect:' + rect.top + ',' + rect.left + ',' + rect.width + ',' + rect.height);
        div.innerHTML = "<div id=divhead style='width:" + rect.width + "px;height:" + rect.height + "px;margin-top:" + rect.top + "px;" + sFilter + src + "\"'></div>";
        alert(src);
    }
}
//上传图片本地显示
function imgFileUp(fileId, putObjId) {
    $("#" + fileId).bind('change', function () {
        previewImage(this, putObjId);
        if ($('#fileUpStore').val() != "") {
            $('.data-exchange').show();
        }
    });
}
//图片上传
    function btnUploadClick(obj, id, showId) {
    obj.bind('click', function () {
        $("#" + id).click();
        if (showId) {
            imgFileUp(id, showId);
        }
    });
}
//我的工单头像
btnUploadClick($('.box-store').find('.btn-upload'), $('.box-store').find('.file-up').attr("id"));
imgFileUp($('.box-store').find('.file-up').attr("id"), $('.box-store .img-edit').attr("id"));
    
});
