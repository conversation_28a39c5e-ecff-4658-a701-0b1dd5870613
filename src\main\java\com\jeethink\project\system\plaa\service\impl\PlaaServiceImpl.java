package com.jeethink.project.system.plaa.service.impl;

import java.util.List;
import com.jeethink.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jeethink.project.system.plaa.mapper.PlaaMapper;
import com.jeethink.project.system.plaa.domain.Plaa;
import com.jeethink.project.system.plaa.service.IPlaaService;
import com.jeethink.common.utils.text.Convert;

/**
 * 利润分成Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-11-01
 */
@Service
public class PlaaServiceImpl implements IPlaaService 
{
    @Autowired
    private PlaaMapper plaaMapper;

    /**
     * 查询利润分成
     * 
     * @param postId 利润分成ID
     * @return 利润分成
     */
    @Override
    public Plaa selectPlaaById(Long postId)
    {
        return plaaMapper.selectPlaaById(postId);
    }

    /**
     * 查询利润分成列表
     * 
     * @param plaa 利润分成
     * @return 利润分成
     */
    @Override
    public List<Plaa> selectPlaaList(Plaa plaa)
    {
        return plaaMapper.selectPlaaList(plaa);
    }

    /**
     * 新增利润分成
     * 
     * @param plaa 利润分成
     * @return 结果
     */
    @Override
    public int insertPlaa(Plaa plaa)
    {
        plaa.setCreateTime(DateUtils.getNowDate());
        return plaaMapper.insertPlaa(plaa);
    }

    /**
     * 修改利润分成
     * 
     * @param plaa 利润分成
     * @return 结果
     */
    @Override
    public int updatePlaa(Plaa plaa)
    {
        plaa.setUpdateTime(DateUtils.getNowDate());
        return plaaMapper.updatePlaa(plaa);
    }

    /**
     * 删除利润分成对象
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deletePlaaByIds(String ids)
    {
        return plaaMapper.deletePlaaByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除利润分成信息
     * 
     * @param postId 利润分成ID
     * @return 结果
     */
    @Override
    public int deletePlaaById(Long postId)
    {
        return plaaMapper.deletePlaaById(postId);
    }
}
