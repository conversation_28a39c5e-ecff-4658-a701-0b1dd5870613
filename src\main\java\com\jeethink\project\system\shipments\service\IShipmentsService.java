package com.jeethink.project.system.shipments.service;

import java.util.List;
import com.jeethink.project.system.shipments.domain.Shipments;


/**
 * 发货信息表Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-31
 */
public interface IShipmentsService 
{
    /**
     * 查询发货信息表
     * 
     * @param sid 发货信息表ID
     * @return 发货信息表
     */
    public Shipments selectShipmentsById(Long sid);

    /**
     * 查询发货信息表列表
     * 
     * @param shipments 发货信息表
     * @return 发货信息表集合
     */
    public List<Shipments> selectShipmentsList(Shipments shipments);


    /**
     * 新增发货信息表
     * 
     * @param shipments 发货信息表
     * @return 结果
     */
    public int insertShipments(Shipments shipments);

    /**
     * 修改发货信息表
     * 
     * @param shipments 发货信息表
     * @return 结果
     */
    public int updateShipments(Shipments shipments);

    /**
     * 批量删除发货信息表
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteShipmentsByIds(String ids);

    /**
     * 删除发货信息表信息
     * 
     * @param sid 发货信息表ID
     * @return 结果
     */
    public int deleteShipmentsById(Long sid);
}
