-- 玩家统计菜单 SQL
-- 使用步骤：
-- 1. 先运行 sql/查询菜单结构.sql 查找合适的父菜单ID
-- 2. 将下面的 @PARENT_MENU_ID 替换为实际的父菜单ID
-- 3. 执行本脚本

-- 设置父菜单ID变量（请根据查询结果修改这个值）
SET @PARENT_MENU_ID = 1; -- 请替换为实际的父菜单ID

-- 添加玩家统计主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('玩家统计', @PARENT_MENU_ID, '10', '/system/player', 'C', '0', 'system:player:view', 'fa fa-users', 'admin', NOW(), '', NULL, '玩家统计菜单');

-- 获取刚插入的菜单ID
SET @parentId = LAST_INSERT_ID();

-- 添加玩家统计相关的按钮权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('玩家查询', @parentId, '1', '#', 'F', '0', 'system:player:list', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('玩家统计', @parentId, '2', '#', 'F', '0', 'system:player:stats', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('玩家详情', @parentId, '3', '#', 'F', '0', 'system:player:detail', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('在线统计', @parentId, '4', '#', 'F', '0', 'system:player:onlineCount', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('注册统计', @parentId, '5', '#', 'F', '0', 'system:player:todayRegCount', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('邀请码查询', @parentId, '6', '#', 'F', '0', 'system:player:listByInvitation', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('邀请码统计', @parentId, '7', '#', 'F', '0', 'system:player:statsByInvitation', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('邀请码列表', @parentId, '8', '#', 'F', '0', 'system:player:invitations', '#', 'admin', NOW(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('玩家导出', @parentId, '9', '#', 'F', '0', 'system:player:export', '#', 'admin', NOW(), '', NULL, '');

-- 为所有现有角色添加菜单权限
-- 查询所有角色并为每个角色添加权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT r.role_id, @parentId
FROM sys_role r
WHERE r.status = '0';

-- 为所有角色添加子菜单权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT r.role_id, @parentId + 1 FROM sys_role r WHERE r.status = '0';

INSERT INTO sys_role_menu (role_id, menu_id)
SELECT r.role_id, @parentId + 2 FROM sys_role r WHERE r.status = '0';

INSERT INTO sys_role_menu (role_id, menu_id)
SELECT r.role_id, @parentId + 3 FROM sys_role r WHERE r.status = '0';

INSERT INTO sys_role_menu (role_id, menu_id)
SELECT r.role_id, @parentId + 4 FROM sys_role r WHERE r.status = '0';

INSERT INTO sys_role_menu (role_id, menu_id)
SELECT r.role_id, @parentId + 5 FROM sys_role r WHERE r.status = '0';

INSERT INTO sys_role_menu (role_id, menu_id)
SELECT r.role_id, @parentId + 6 FROM sys_role r WHERE r.status = '0';

INSERT INTO sys_role_menu (role_id, menu_id)
SELECT r.role_id, @parentId + 7 FROM sys_role r WHERE r.status = '0';

INSERT INTO sys_role_menu (role_id, menu_id)
SELECT r.role_id, @parentId + 8 FROM sys_role r WHERE r.status = '0';

INSERT INTO sys_role_menu (role_id, menu_id)
SELECT r.role_id, @parentId + 9 FROM sys_role r WHERE r.status = '0';

-- 查询结果验证
SELECT m.menu_id, m.menu_name, m.parent_id, m.order_num, m.url, m.perms, m.menu_type, m.visible
FROM sys_menu m 
WHERE m.menu_name = '玩家统计' OR m.parent_id = @parentId
ORDER BY m.parent_id, m.order_num;
