package com.jeethink.project.system.pay.service.impl;

import java.util.List;
import com.jeethink.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jeethink.project.system.pay.mapper.PayMapper;
import com.jeethink.project.system.pay.domain.Pay;
import com.jeethink.project.system.pay.service.IPayService;
import com.jeethink.common.utils.text.Convert;

/**
 * 支付Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-11-02
 */
@Service
public class PayServiceImpl implements IPayService 
{
    @Autowired
    private PayMapper payMapper;

    /**
     * 查询支付
     * 
     * @param id 支付ID
     * @return 支付
     */
    @Override
    public Pay selectPayById(Long id)
    {
        return payMapper.selectPayById(id);
    }

    @Override
    public Pay selectByNoId(String id) {
        return payMapper.selectByNoId(id);
    }

    /**
     * 查询支付列表
     * 
     * @param pay 支付
     * @return 支付
     */
    @Override
    public List<Pay> selectPayList(Pay pay)
    {
        return payMapper.selectPayList(pay);
    }

    /**
     * 新增支付
     * 
     * @param pay 支付
     * @return 结果
     */
    @Override
    public int insertPay(Pay pay)
    {
        pay.setCreateTime(DateUtils.getNowDate());
        return payMapper.insertPay(pay);
    }

    /**
     * 修改支付
     * 
     * @param pay 支付
     * @return 结果
     */
    @Override
    public int updatePay(Pay pay)
    {
        pay.setUpdateTime(DateUtils.getNowDate());
        return payMapper.updatePay(pay);
    }

    /**
     * 删除支付对象
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deletePayByIds(String ids)
    {
        return payMapper.deletePayByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除支付信息
     * 
     * @param id 支付ID
     * @return 结果
     */
    @Override
    public int deletePayById(Long id)
    {
        return payMapper.deletePayById(id);
    }
}
