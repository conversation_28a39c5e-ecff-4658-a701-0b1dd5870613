package com.jeethink;

import com.jeethink.project.system.player.controller.PlayerController;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * PlayerController测试
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class PlayerControllerTest {

    @Autowired
    private PlayerController playerController;

    @Test
    public void testControllerExists() {
        System.out.println("PlayerController注入成功: " + (playerController != null));
        
        if (playerController != null) {
            System.out.println("PlayerController类: " + playerController.getClass().getName());
        }
    }
}
