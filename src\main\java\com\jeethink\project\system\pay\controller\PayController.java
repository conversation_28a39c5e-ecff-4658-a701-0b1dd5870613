package com.jeethink.project.system.pay.controller;

// 第三方工具类导入
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

// 项目内部类导入
import com.jeethink.project.system.pay.domain.Pay;
import com.jeethink.common.constant.UserConstants;
import com.jeethink.common.utils.pay.HttpTookit;
import com.jeethink.common.utils.pay.RSAUtils;
import com.jeethink.common.utils.pay.SignUtils;
import com.jeethink.framework.web.controller.BaseController;
import com.jeethink.project.system.pay.service.IPayService;

// Apache HTTP组件导入
import org.apache.http.client.utils.URIBuilder;

// Spring框架导入
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

// Java标准库导入
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URISyntaxException;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 支付控制器
 *
 * 负责处理支付相关的Web请求，包括：
 * 1. 支付页面展示
 * 2. 支付订单创建和提交
 * 3. 支付回调处理（同步和异步）
 * 4. 支付状态查询和更新
 *
 * <AUTHOR>
 * @date 2023-11-02
 */
@Controller
public class PayController extends BaseController
{
    /** 视图前缀路径 */
    private String prefix = "system/pay";

    /** 字符编码常量 */
    public static final String CHARSET = "UTF-8";

    /** 商户ID - 从配置文件中读取，默认值为"1231" */
    @Value(value = "${pay.MCH_ID}")
    public String MCH_ID = "1231";

    /** MD5签名密钥 - 从配置文件中读取 */
    @Value(value = "${pay.MD5_KEY}")
    public String MD5_KEY;

    /** 回调基础URL - 从配置文件中读取 */
    @Value(value = "${pay.url}")
    public String url;

    /** 支付API接口地址 - 从配置文件中读取 */
    @Value(value = "${pay.apiurl}")
    public String apiurl;

    /** 支付服务接口 */
    @Autowired
    private IPayService payService;

    /**
     * 获取支付页面
     *
     * 生成一个唯一的商户订单号，并跳转到支付页面
     *
     * @param mmap 模型映射，用于向视图传递数据
     * @param area 区服ID，标识不同的游戏服务器
     * @return 返回支付页面的视图路径
     */
    @GetMapping("/pay")
    public String config(ModelMap mmap, Integer area)
    {
        // 生成UUID作为订单号的基础
        UUID id = UUID.randomUUID();
        // 将UUID按"-"分割，去除连字符
        String[] idd = id.toString().split("-");
        // 拼接前四段作为商户订单号
        String uid = idd[0] + idd[1] + idd[2] + idd[3];

        // 将订单号和区服信息传递给前端页面
        mmap.put("mchOrderNo", uid);
        mmap.put("area", area);

        return prefix + "/index";
    }

    /**
     * 支付同步回调处理
     *
     * 当用户完成支付后，支付平台会重定向到此接口
     * 主要用于页面展示，告知用户支付结果
     *
     * @param pid 商户ID
     * @param trade_no 平台交易号
     * @param out_trade_no 商户订单号
     * @param type 支付类型
     * @param name 商品名称
     * @param money 支付金额
     * @param sign 签名
     * @param trade_status 交易状态
     * @param sign_type 签名类型
     * @param map 模型映射
     * @param request HTTP请求对象
     * @return 返回支付结果页面
     */
    @GetMapping("/pay/returnUrl")
    public String returnUrl(String pid, String trade_no, String out_trade_no, String type,
                            String name, String money, String sign, String trade_status,
                            String sign_type, ModelMap map, HttpServletRequest request)
    {
        // 将支付结果参数传递给前端页面显示
        map.put("trade_no", trade_no);
        map.put("out_trade_no", out_trade_no);
        map.put("type", type);
        map.put("name", name);
        map.put("money", money);
        map.put("sign", sign);
        map.put("trade_status", trade_status);

        return prefix + "/info";
    }

    /**
     * 支付异步回调处理
     *
     * 支付平台在支付完成后会异步调用此接口通知支付结果
     * 这是支付状态更新的关键接口，需要验证签名确保数据安全
     *
     * @param pid 商户ID
     * @param trade_no 平台交易号
     * @param out_trade_no 商户订单号
     * @param type 支付类型
     * @param name 商品名称
     * @param money 支付金额
     * @param sign 签名
     * @param trade_status 交易状态
     * @param sign_type 签名类型
     * @return 返回"SUCCESS"表示处理成功，"FAIL"表示处理失败
     */
    @GetMapping("/pay/notifyUrl")
    @ResponseBody
    public String notifyUrl(String pid, String trade_no, String out_trade_no, String type,
                            String name, String money, String sign, String trade_status, String sign_type)
    {
        // 只处理支付成功的回调
        if ("TRADE_SUCCESS".equals(trade_status)) {
            // 构建参数Map用于签名验证
            Map<String, Object> map = new HashMap<>();
            map.put("pid", pid);
            map.put("trade_no", trade_no);
            map.put("out_trade_no", out_trade_no);
            map.put("name", name);
            map.put("money", money);
            map.put("sign", sign);
            map.put("trade_status", trade_status);

            // 验证签名，确保回调数据的真实性和完整性
            boolean verify = SignUtils.checkParam(map, type + MD5_KEY, CHARSET);
            if (verify) {
                // 签名验证通过，更新订单状态
                Pay pay = payService.selectByNoId(out_trade_no);
                pay.setStatus("支付成功");
                pay.setWudiOrderNo(trade_no);
                payService.updatePay(pay);
                return "SUCCESS";
            }
        }
        // 支付失败或签名验证失败
        return "FAIL";
    }

    /**
     * 网页端支付提交
     *
     * 处理来自网页的支付请求，创建支付订单并重定向到支付平台
     * 包含重复订单检查、IP地址获取、签名生成等逻辑
     *
     * @param pay 支付订单对象
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @param mmap 模型映射
     * @return 成功时返回null（重定向），失败时返回错误页面
     */
    @PostMapping("/pay/save")
    @ResponseStatus(HttpStatus.FOUND)
    public String save(Pay pay, HttpServletRequest request, HttpServletResponse response, ModelMap mmap)
    {
        // 检查订单是否已存在，防止重复提交
        Pay oldPay = payService.selectByNoId(pay.getMchOrderNo());
        if(oldPay == null){
            // 获取客户端真实IP地址，考虑代理服务器的情况
            String ipAddress = request.getHeader("X-Forwarded-For");
            if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader("Proxy-Client-IP");
            }
            if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getRemoteAddr();
            }

            // 设置订单基本信息
            pay.setStatus("未支付");
            pay.setIp(ipAddress);

            // 构建支付请求参数
            HashMap<String, Object> params = new HashMap<>();
            params.put("pid", MCH_ID);                                    // 商户ID
            params.put("out_trade_no", pay.getMchOrderNo());             // 商户订单号
            params.put("notify_url", url + "/pay/notifyUrl");            // 异步回调地址
            params.put("return_url", url + "/pay/returnUrl");            // 同步回调地址
            params.put("name", pay.getGoodsParamExt());                  // 商品名称
            params.put("money", pay.getAmount().toString());             // 支付金额
            params.put("sitename", "test");                              // 网站名称

            // 生成签名，确保数据安全
            String sign = SignUtils.sign(params, pay.getChannelCode() + MD5_KEY, RSAUtils.CHARSET);
            params.put("type", pay.getChannelCode());                    // 支付渠道类型
            params.put("sign", sign);                                    // 签名
            params.put("sign_type", "MD5");                              // 签名类型

            // 设置支付时间
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String dateStr = sf.format(new Date());
            pay.setPaySuccTime(dateStr);

            // 构建支付URL并重定向
            URIBuilder builder = null;
            try {
                builder = new URIBuilder(apiurl);
                // 将所有参数添加到URL中
                for (String key : params.keySet()) {
                    builder.addParameter(key, params.get(key).toString());
                }

                // 保存订单到数据库
                int i = payService.insertPay(pay);
                if(i == 1){
                    // 设置重定向地址
                    response.setHeader("location", builder.toString());
                    System.out.println("回调地址：" + builder.toString());
                    return null; // 返回null表示重定向
                }
            } catch (URISyntaxException e) {
                e.printStackTrace();
            }
        }

        // 订单已存在或创建失败，返回错误页面
        addModel(pay, mmap);
        mmap.put("trade_status", "ERROR_MCH_ORDER_NO");
        return prefix + "/info";
    }

    /**
     * 游戏内支付提交
     *
     * 处理来自游戏客户端的支付请求，自动生成订单号并创建支付订单
     * 与网页端支付的区别是会自动生成订单号，适用于游戏内购买场景
     *
     * @param pay 支付订单对象
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @param mmap 模型映射
     * @return 成功时返回null（重定向），失败时返回错误页面
     */
    @GetMapping("/pay/game")
    @ResponseStatus(HttpStatus.FOUND)
    public String game(Pay pay, HttpServletRequest request, HttpServletResponse response, ModelMap mmap)
    {
        // 获取客户端真实IP地址，考虑代理服务器的情况
        String ipAddress = request.getHeader("X-Forwarded-For");
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }

        // 自动生成唯一的商户订单号
        UUID id = UUID.randomUUID();
        String[] idd = id.toString().split("-");
        String uid = idd[0] + idd[1] + idd[2] + idd[3];

        // 设置订单基本信息
        pay.setMchOrderNo(uid);
        pay.setStatus("未支付");
        pay.setIp(ipAddress);

        // 构建支付请求参数
        HashMap<String, Object> params = new HashMap<>();
        params.put("pid", MCH_ID);                                    // 商户ID
        params.put("out_trade_no", pay.getMchOrderNo());             // 商户订单号
        params.put("notify_url", url + "/pay/notifyUrl");            // 异步回调地址
        params.put("return_url", url + "/pay/returnUrl");            // 同步回调地址
        params.put("name", pay.getGoodsParamExt());                  // 商品名称
        params.put("money", pay.getAmount().toString());             // 支付金额
        params.put("sitename", "test");                              // 网站名称

        // 生成签名，确保数据安全
        String sign = SignUtils.sign(params, pay.getChannelCode() + MD5_KEY, RSAUtils.CHARSET);
        params.put("type", pay.getChannelCode());                    // 支付渠道类型
        params.put("sign", sign);                                    // 签名
        params.put("sign_type", "MD5");                              // 签名类型

        // 设置支付时间
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateStr = sf.format(new Date());
        pay.setPaySuccTime(dateStr);

        // 构建支付URL并重定向
        URIBuilder builder = null;
        try {
            builder = new URIBuilder(apiurl);
            // 将所有参数添加到URL中
            for (String key : params.keySet()) {
                builder.addParameter(key, params.get(key).toString());
            }

            // 保存订单到数据库
            int i = payService.insertPay(pay);
            if(i == 1){
                // 设置重定向地址
                response.setHeader("location", builder.toString());
                System.out.println("回调地址：" + builder.toString());
                return null; // 返回null表示重定向
            }
        } catch (URISyntaxException e) {
            e.printStackTrace();
        }

        // 创建失败，返回错误页面
        addModel(pay, mmap);
        return prefix + "/info";
    }
    /**
     * 添加模型数据到视图
     *
     * 将支付订单信息添加到ModelMap中，用于在错误页面显示订单详情
     * 主要用于支付失败或异常情况下的信息展示
     *
     * @param pay 支付订单对象
     * @param map 模型映射对象
     */
    public void addModel(Pay pay, ModelMap map){
        map.put("trade_no", pay.getMchOrderNo());        // 商户订单号
        map.put("out_trade_no", "");                     // 平台订单号（空）
        map.put("type", pay.getChannelCode());           // 支付渠道类型
        map.put("name", pay.getGoodsParamExt());         // 商品名称
        map.put("money", pay.getAmount());               // 支付金额
        map.put("trade_status", "ERROR");                // 交易状态（错误）
    }
}
