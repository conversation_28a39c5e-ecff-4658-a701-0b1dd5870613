现在有一个前后端结合的需求
# 已知：
## 当前的环境：
1. 当前的项目已经可以运行，是一个前后端结合的项目
2. 项目是基于jeethink框架开发的
3. 项目是java开发的，前端是thymeleaf
4. 项目是springboot框架的
5. 项目是maven管理的
6. 项目是mysql数据库的
7. 项目是shiro权限管理的
8. 项目是mybatis框架的
9. 项目是druid数据源的

## 当前的数据库：
数据库的介绍：
项目中已经有了mysql的连接，在接下来的编程过程中，请不要影响已经有了的Mysql的连接代码配置
sql server数据库测试程序：（src\main\java\com\jeethink\common\utils\DatabaseConnectionTest.java）
我想让你连接到这个SQL server数据库：
$dbnm['DB'] = 'lin2db';
$host['DB'] = '***********';
$port['DB'] = 1433;
$user['DB'] = 'sa';
$pass['DB'] = 'bgroigmroAD147258JFDJGBHjhdhf';
这个里面最重要的表就是：user_account
我已经为你连上了数据库MCP，你可以随时查询这个数据库的相关的信息
这是玩家与主播的关联邀请码的数据表（玩家表的邀请和在线数据）：user_account
里面有重要的四个字段：account last_login last_logout invitation
注意：通过比较last_login last_logout可以判断用户是否在线，例如：当last_login小于last_logout的时候，说明用户不在线，反之，说明用户在线
注意：invitation字段，对应的就是Mysql数据库里面的user表（主播表）的invite字段
通过对比就知道了，哪些玩家属于哪个主播
（玩家表注册数据表：ssn）
这个表里面只有两个字段有用：name和reg_date，name就是玩家的账号，reg_date就是玩家的注册时间，在前端列表用于关联查询，显示注册日期

## 数据库需求：
现在的数据库需求是要加上SQL server数据库参与项目（当前的需求是连接上一个sqlserver的数据库lin2db，其实在以后的需求中，还有相同数据库主机的另外两个sqlserver数据库，所以你注意代码的可拓展性）

## 前端代码需求：
在左侧导航栏的“利润分成”的下拉显示里，加上一个选项“玩家统计”；
在右侧的大盒子里面写入一个列表，在列表的上面一些的位置写上醒目的提示：当前在线的玩家数量 今日的账号注册量
（当前的代码已经有了“代理分成”的选项与对应的界面）
例如：
“玩家统计”按钮点击之后，右下角的大盒子就要包含的内容有：
一个列表（仅仅显示属于当前登录的主播账号的邀请码的玩家，admin可以全部显示）：（注意，通过比较last_login last_logout可以判断用户是否在线）
每一行：显示属于自己的邀请码的玩家的账号名account 在线的状态 注册时间（用ssn关联查询出来的reg_date），注意分页设计

## 后端代码需求：
你把新添加的Java后端的代码的接口数据记载在文件 md文档合计(java)\项目1\接口文档.md 里面，方便以后溯源，做微调，以及前端的编写
