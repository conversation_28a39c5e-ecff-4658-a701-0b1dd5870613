<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改代理分成')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-paysystem-edit" th:object="${paySystem}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">订单号：</label>
                <div class="col-sm-8">
                    <input name="mchOrderNo" th:field="*{mchOrderNo}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">区号：</label>
                <div class="col-sm-8">
                    <input name="area" th:field="*{area}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">支付渠道：</label>
                <div class="col-sm-8">
                    <input name="channelCode" th:field="*{channelCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">支付金额：</label>
                <div class="col-sm-8">
                    <input name="amount" th:field="*{amount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">角色名：</label>
                <div class="col-sm-8">
                    <input name="goodsParamExt" th:field="*{goodsParamExt}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">ip：</label>
                <div class="col-sm-8">
                    <input name="ip" th:field="*{ip}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">支付状态：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('sys_succe_lose')}">
                        <input type="radio" th:id="${'status_' + dict.dictCode}" name="status" th:value="${dict.dictValue}" th:field="*{status}">
                        <label th:for="${'status_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">支付时间：</label>
                <div class="col-sm-8">
                    <input name="paySuccTime" th:field="*{paySuccTime}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">发货状态：</label>
                <div class="col-sm-8">
                    <input name="deliverGoods" th:field="*{deliverGoods}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">代理：</label>
                <div class="col-sm-8">
                    <input name="agency" th:field="*{agency}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">平台订单号：</label>
                <div class="col-sm-8">
                    <input name="wudiOrderNo" th:field="*{wudiOrderNo}" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "system/paysystem";
        $("#form-paysystem-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-paysystem-edit').serialize());
            }
        }
    </script>
</body>
</html>