# 玩家统计功能部署说明（JdbcTemplate版本）

## 概述
本文档说明如何部署新开发的玩家统计功能，该功能使用JdbcTemplate实现对SQL Server数据库中玩家数据的统计和管理，避免了MyBatis配置冲突问题。

## 重构说明
由于原始的MyBatis多数据源配置与现有系统产生冲突，我们已将实现方式改为JdbcTemplate：
- ✅ **优点**: 避免MyBatis配置冲突，代码更简洁，性能良好
- ✅ **稳定性**: 不影响现有系统的MyBatis配置
- ✅ **维护性**: 代码更直观，调试更容易

## 部署步骤

### 1. 确认代码文件
确保以下文件已正确部署：

**配置文件**:
- `src/main/java/com/jeethink/framework/config/SqlServerDataSourceConfig.java` - 数据源配置
- `src/main/resources/application-druid.yml` - 数据库连接配置

**业务代码**:
- `src/main/java/com/jeethink/project/system/player/dao/PlayerDao.java` - 数据访问层（JdbcTemplate）
- `src/main/java/com/jeethink/project/system/player/service/impl/PlayerServiceImpl.java` - 业务逻辑层
- `src/main/java/com/jeethink/project/system/player/controller/PlayerController.java` - 控制器层
- `src/main/resources/templates/system/player/player.html` - 前端页面

### 2. 数据库配置
SQL Server数据源配置已内置在代码中，无需额外配置文件修改：

```java
// 配置已硬编码在SqlServerDataSourceConfig.java中
dataSource.setUrl("***********************************************************************************************");
dataSource.setUsername("sa");
dataSource.setPassword("bgroigmroAD147258JFDJGBHjhdhf");
```

### 3. 确认SQL Server驱动依赖
项目的 `pom.xml` 中已包含SQL Server JDBC驱动：

```xml
<dependency>
    <groupId>com.microsoft.sqlserver</groupId>
    <artifactId>mssql-jdbc</artifactId>
    <version>9.4.1.jre8</version>
</dependency>
```

### 3. 执行菜单SQL脚本
运行 `sql/player_menu.sql` 脚本添加菜单项和权限：

```sql
-- 需要先确定"利润分成"的父菜单ID，然后修改脚本中的parent_id值
-- 执行脚本添加玩家统计菜单和相关权限
```

**重要**: 需要根据实际的菜单结构调整脚本中的 `parent_id` 值。

### 4. 验证数据库连接
可以运行测试类验证SQL Server连接：

```java
// 运行 SqlServerConnectionTest.testSqlServerConnection() 方法
// 检查控制台输出是否显示连接成功和统计数据
```

### 5. 重启应用
重启Spring Boot应用以加载新的配置和代码。

**重要**: 由于使用了JdbcTemplate而非MyBatis，不会与现有系统产生配置冲突。

## 功能验证

### 1. 菜单显示验证
- 登录系统后，在左侧导航栏的"利润分成"下拉菜单中应该能看到"玩家统计"选项
- 点击"玩家统计"应该能正常跳转到玩家列表页面

### 2. 权限验证
- **Admin用户**: 应该能看到所有玩家数据和完整的统计信息
- **普通用户**: 只能看到自己邀请码对应的玩家数据

### 3. 数据显示验证
- 页面顶部应该显示"当前在线玩家数量"和"今日注册账号数量"
- 玩家列表应该包含：账号、在线状态、邀请码、登录时间、注册时间等信息
- 在线状态应该根据 `last_login` 和 `last_logout` 时间正确计算

### 4. 功能验证
- 搜索功能：按账号、邀请码、IP地址搜索
- 分页功能：支持分页显示
- 详情查看：点击详情按钮能查看玩家详细信息
- 自动刷新：统计数据每30秒自动刷新

## 故障排除

### 1. 应用启动失败（MyBatis配置冲突）
**症状**: 启动时报 `Invalid bound statement (not found): com.jeethink.project.system.config.mapper.ConfigMapper.selectConfigList`
**解决方案**:
- ✅ **已解决**: 我们已改用JdbcTemplate，不会再出现此问题
- 如果仍有问题，检查是否有残留的MyBatis配置文件

### 2. SQL Server连接失败
**症状**: 应用启动时报数据库连接错误
**解决方案**:
- 检查SQL Server服务是否运行
- 验证IP地址、端口、用户名、密码是否正确
- 确保防火墙允许1433端口访问
- 检查SQL Server是否启用TCP/IP协议
- 运行 `SqlServerConnectionTest` 进行连接测试

### 3. JdbcTemplate相关错误
**症状**: 查询时报SQL语法错误或字段映射错误
**解决方案**:
- 检查SQL Server中 `user_account` 和 `ssn` 表是否存在
- 验证表结构是否与RowMapper中的字段匹配
- 查看应用日志中的具体SQL错误信息
- 确认数据库中有测试数据

### 4. 菜单不显示
**症状**: 左侧导航栏没有"玩家统计"选项
**解决方案**:
- 检查菜单SQL脚本是否正确执行
- 确认用户角色是否有相应的菜单权限
- 检查 `sys_menu` 和 `sys_role_menu` 表中的数据

### 5. 权限错误
**症状**: 访问页面时提示权限不足
**解决方案**:
- 检查用户角色是否包含 `system:player:*` 相关权限
- 确认菜单权限配置是否正确
- 重新登录以刷新权限缓存

### 6. 数据显示问题
**症状**: 页面能正常访问但数据为空或显示错误
**解决方案**:
- 检查SQL Server中 `user_account` 和 `ssn` 表是否有数据
- 验证邀请码字段的大小写问题（已使用UPPER函数处理）
- 检查在线状态计算逻辑
- 查看浏览器控制台是否有JavaScript错误

## 性能优化建议

### 1. 数据库索引
建议在SQL Server中创建以下索引以提高查询性能：

```sql
-- user_account表索引
CREATE INDEX IX_user_account_invitation ON user_account(invitation);
CREATE INDEX IX_user_account_login_times ON user_account(last_login, last_logout);

-- ssn表索引
CREATE INDEX IX_ssn_name ON ssn(name);
CREATE INDEX IX_ssn_reg_date ON ssn(reg_date);
```

### 2. 连接池配置
根据实际负载调整SQL Server连接池参数：

```yaml
# 连接池优化配置
initialSize: 2      # 初始连接数
minIdle: 2          # 最小空闲连接数
maxActive: 10       # 最大活跃连接数
maxWait: 60000      # 获取连接超时时间
```

### 3. 缓存策略
考虑对统计数据实施缓存策略：
- 在线玩家数量：缓存1分钟
- 今日注册数量：缓存5分钟
- 玩家列表：根据查询条件缓存

## 扩展说明

### 1. 支持更多数据库
当前配置已预留了 `lin2world` 和 `lin2site` 数据库配置，可以通过以下方式扩展：

```java
// 在Service中切换数据源
SqlServerDataSourceContextHolder.useLin2world();
// 执行查询
SqlServerDataSourceContextHolder.clear();
```

### 2. 添加新的统计维度
可以通过以下步骤添加新的统计功能：
1. 在Mapper中添加新的查询方法
2. 在Service中实现业务逻辑
3. 在Controller中添加新的接口
4. 在前端页面中添加显示元素

### 3. 实时数据推送
可以考虑集成WebSocket实现实时数据推送，避免定时轮询。

## 联系信息
如有问题，请联系开发团队或查看相关技术文档。
