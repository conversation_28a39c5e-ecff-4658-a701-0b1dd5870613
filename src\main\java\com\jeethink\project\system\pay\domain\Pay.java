package com.jeethink.project.system.pay.domain;

import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jeethink.framework.aspectj.lang.annotation.Excel;
import com.jeethink.framework.web.domain.BaseEntity;

/**
 * 支付对象 pay
 * 
 * <AUTHOR>
 * @date 2023-11-02
 */
public class Pay extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 商户订单号 */
    @Excel(name = "商户订单号")
    private String mchOrderNo;

    /** 支付渠道 */
    @Excel(name = "支付渠道")
    private String channelCode;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private Long amount;

    /** 角色名 */
    @Excel(name = "角色名")
    private String goodsParamExt;

    /** ip */
    @Excel(name = "ip")
    private String ip;

    /** 支付状态 */
    @Excel(name = "支付状态")
    private String status;

    /** 支付时间 */
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd")
    private String paySuccTime;

    /** 平台订单号 */
    @Excel(name = "平台订单号")
    private String wudiOrderNo;

    /** 平台订单号 */
    @Excel(name = "区服")
    private Integer area;

    public Integer getArea() {
        return area;
    }

    public void setArea(Integer area) {
        this.area = area;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setMchOrderNo(String mchOrderNo)
    {
        this.mchOrderNo = mchOrderNo;
    }

    public String getMchOrderNo()
    {
        return mchOrderNo;
    }
    public void setChannelCode(String channelCode)
    {
        this.channelCode = channelCode;
    }

    public String getChannelCode()
    {
        return channelCode;
    }
    public void setAmount(Long amount)
    {
        this.amount = amount;
    }

    public Long getAmount()
    {
        return amount;
    }
    public void setGoodsParamExt(String goodsParamExt)
    {
        this.goodsParamExt = goodsParamExt;
    }

    public String getGoodsParamExt()
    {
        return goodsParamExt;
    }
    public void setIp(String ip)
    {
        this.ip = ip;
    }

    public String getIp()
    {
        return ip;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public String getPaySuccTime() {
        return paySuccTime;
    }

    public void setPaySuccTime(String paySuccTime) {
        this.paySuccTime = paySuccTime;
    }

    public void setWudiOrderNo(String wudiOrderNo) {
        this.wudiOrderNo = wudiOrderNo;
    }

    public String getWudiOrderNo()
    {
        return wudiOrderNo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("mchOrderNo", getMchOrderNo())
            .append("channelCode", getChannelCode())
            .append("amount", getAmount())
            .append("goodsParamExt", getGoodsParamExt())
            .append("ip", getIp())
            .append("status", getStatus())
            .append("paySuccTime", getPaySuccTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("wudiOrderNo", getWudiOrderNo())
            .toString();
    }
}
