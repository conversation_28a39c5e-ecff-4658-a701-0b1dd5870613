/**
 * <AUTHOR> wen <<EMAIL>>
 * version: 1.17.1
 * https://github.com/wenzhixin/bootstrap-table/
 */
function getRememberRowIds(a, b) {
    return props = $.isArray(a) ? $.map(a, function (a) {
        return a[b]
    }) : [a[b]]
}

function addRememberRow(a, b) {
    var c = null == table.options.uniqueId ? table.options.columns[1].field : table.options.uniqueId,
        d = getRememberRowIds(a, c);
    -1 == $.inArray(b[c], d) && (a[a.length] = b)
}

function removeRememberRow(a, b) {
    var c = null == table.options.uniqueId ? table.options.columns[1].field : table.options.uniqueId,
        d = getRememberRowIds(a, c), e = $.inArray(b[c], d);
    -1 != e && a.splice(e, 1)
}

var TABLE_EVENTS, firstLoadTable, union, difference, _;
!function (a, b) {
    "object" == typeof exports && "undefined" != typeof module ? module.exports = b(require("jquery")) : "function" == typeof define && define.amd ? define(["jquery"], b) : (a = a || self, a.BootstrapTable = b(a.jQuery))
}(this, function (a) {
    "use strict";

    function b(a, b) {
        return b = {exports: {}}, a(b, b.exports), b.exports
    }

    function c(a, b) {
        return RegExp(a, b)
    }

    function d(a) {
        return d = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (a) {
            return typeof a
        } : function (a) {
            return a && "function" == typeof Symbol && a.constructor === Symbol && a !== Symbol.prototype ? "symbol" : typeof a
        }, d(a)
    }

    function e(a, b) {
        if (!(a instanceof b)) throw new TypeError("Cannot call a class as a function")
    }

    function f(a, b) {
        var c, d;
        for (c = 0; c < b.length; c++) d = b[c], d.enumerable = d.enumerable || !1, d.configurable = !0, "value" in d && (d.writable = !0), Object.defineProperty(a, d.key, d)
    }

    function g(a, b, c) {
        return b && f(a.prototype, b), c && f(a, c), a
    }

    function h(a, b) {
        return k(a) || m(a, b) || o()
    }

    function i(a) {
        return j(a) || l(a) || n()
    }

    function j(a) {
        if (Array.isArray(a)) {
            for (var b = 0, c = new Array(a.length); b < a.length; b++) c[b] = a[b];
            return c
        }
    }

    function k(a) {
        return Array.isArray(a) ? a : void 0
    }

    function l(a) {
        return Symbol.iterator in Object(a) || "[object Arguments]" === Object.prototype.toString.call(a) ? Array.from(a) : void 0
    }

    function m(a, b) {
        var c, d, e, f, g, h;
        if (Symbol.iterator in Object(a) || "[object Arguments]" === Object.prototype.toString.call(a)) {
            c = [], d = !0, e = !1, f = void 0;
            try {
                for (h = a[Symbol.iterator](); !(d = (g = h.next()).done) && (c.push(g.value), !b || c.length !== b); d = !0) ;
            } catch (i) {
                e = !0, f = i
            } finally {
                try {
                    d || null == h["return"] || h["return"]()
                } finally {
                    if (e) throw f
                }
            }
            return c
        }
    }

    function n() {
        throw new TypeError("Invalid attempt to spread non-iterable instance")
    }

    function o() {
        throw new TypeError("Invalid attempt to destructure non-iterable instance")
    }

    var p, q, r, s, t, u, v, w, x, y, z, A, B, C, D, E, F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T, U, V, W, X, Y, Z,
        $, _, ab, bb, cb, db, eb, fb, gb, hb, ib, jb, kb, lb, mb, nb, ob, pb, qb, rb, sb, tb, ub, vb, wb, xb, yb, zb,
        Ab, Bb, Cb, Db, Eb, Fb, Gb, Hb, Ib, Jb, Kb, Lb, Mb, Nb, Ob, Pb, Qb, Rb, Sb, Tb, Ub, Vb, Wb, Xb, Yb, Zb, $b, _b,
        ac, bc, cc, dc, ec, fc, gc, hc, ic, jc, kc, lc, mc, nc, oc, pc, qc, rc, sc, tc, uc, vc, wc, xc, yc, zc, Ac, Bc,
        Cc, Dc, Ec, Fc, Gc, Hc, Ic, Jc, Kc, Lc, Mc, Nc, Oc, Pc, Qc, Rc, Sc, Tc, Uc, Vc, Wc, Xc, Yc, Zc, $c, _c, ad, bd,
        cd, dd, ed, fd, gd, hd, id, jd, kd, ld, md, nd, od, pd, qd, rd, sd, td, ud, vd, wd, xd, yd, zd, Ad, Bd, Cd, Dd,
        Ed, Fd, Gd, Hd, Id, Jd, Kd, Ld, Md, Nd, Od, Pd, Qd, Rd, Sd, Td, Ud, Vd, Wd, Xd, Yd, Zd, $d, _d, ae, be, ce, de,
        ee, fe, ge, he, ie, je, ke, le, me, ne, oe, pe, qe, re, se, te, ue, ve, we, xe, ye, ze, Ae, Be, Ce, De, Ee, Fe,
        Ge, He, Ie, Je, Ke, Le, Me, Ne, Oe, Pe, Qe, Re, Se, Te, Ue, Ve, We, Xe, Ye, Ze, $e, _e, af, bf, cf, df, ef, ff,
        gf, hf, jf, kf, lf, mf, nf, of, pf, qf, rf, sf, tf, uf, vf, wf, xf, yf, zf, Af, Bf, Cf, Df, Ef, Ff, Gf, Hf, If,
        Jf, Kf, Lf, Mf, Nf, Of, Pf, Qf, Rf, Sf, Tf, Uf, Vf, Wf, Xf, Yf, Zf, $f, _f, ag, bg, cg, dg, eg, fg, gg, hg, ig,
        jg, kg, lg, mg, ng, og, pg, qg, rg, sg, tg, ug, vg, wg, xg, yg, zg, Ag, Bg, Cg, Dg, Eg, Fg, Gg, Hg, Ig, Jg, Kg,
        Lg, Mg, Ng, Og, Pg, Qg, Rg, Sg, Tg, Ug, Vg, Wg, Xg, Yg, Zg, $g, _g, ah, bh, ch, dh, eh, fh, gh, hh, ih, jh, kh,
        lh, mh, nh, oh, ph, qh, rh, sh, th, uh, vh, wh, xh, yh, zh, Ah, Bh, Ch, Dh, Eh, Fh, Gh;
    if (a = a && Object.prototype.hasOwnProperty.call(a, "default") ? a["default"] : a, p = "undefined" != typeof globalThis ? globalThis : "undefined" != typeof window ? window : "undefined" != typeof global ? global : "undefined" != typeof self ? self : {}, q = function (a) {
        return a && a.Math == Math && a
    }, r = q("object" == typeof globalThis && globalThis) || q("object" == typeof window && window) || q("object" == typeof self && self) || q("object" == typeof p && p) || Function("return this")(), s = function (a) {
        try {
            return !!a()
        } catch (b) {
            return !0
        }
    }, t = !s(function () {
        return 7 != Object.defineProperty({}, "a", {
            get: function () {
                return 7
            }
        }).a
    }), u = {}.propertyIsEnumerable, v = Object.getOwnPropertyDescriptor, w = v && !u.call({1: 2}, 1), x = w ? function (a) {
        var b = v(this, a);
        return !!b && b.enumerable
    } : u, y = {f: x}, z = function (a, b) {
        return {enumerable: !(1 & a), configurable: !(2 & a), writable: !(4 & a), value: b}
    }, A = {}.toString, B = function (a) {
        return A.call(a).slice(8, -1)
    }, C = "".split, D = s(function () {
        return !Object("z").propertyIsEnumerable(0)
    }) ? function (a) {
        return "String" == B(a) ? C.call(a, "") : Object(a)
    } : Object, E = function (a) {
        if (void 0 == a) throw TypeError("Can't call method on " + a);
        return a
    }, F = function (a) {
        return D(E(a))
    }, G = function (a) {
        return "object" == typeof a ? null !== a : "function" == typeof a
    }, H = function (a, b) {
        if (!G(a)) return a;
        var c, d;
        if (b && "function" == typeof (c = a.toString) && !G(d = c.call(a))) return d;
        if ("function" == typeof (c = a.valueOf) && !G(d = c.call(a))) return d;
        if (!b && "function" == typeof (c = a.toString) && !G(d = c.call(a))) return d;
        throw TypeError("Can't convert object to primitive value")
    }, I = {}.hasOwnProperty, J = function (a, b) {
        return I.call(a, b)
    }, K = r.document, L = G(K) && G(K.createElement), M = function (a) {
        return L ? K.createElement(a) : {}
    }, N = !t && !s(function () {
        return 7 != Object.defineProperty(M("div"), "a", {
            get: function () {
                return 7
            }
        }).a
    }), O = Object.getOwnPropertyDescriptor, P = t ? O : function (a, b) {
        if (a = F(a), b = H(b, !0), N) try {
            return O(a, b)
        } catch (c) {
        }
        return J(a, b) ? z(!y.f.call(a, b), a[b]) : void 0
    }, Q = {f: P}, R = function (a) {
        if (!G(a)) throw TypeError(String(a) + " is not an object");
        return a
    }, S = Object.defineProperty, T = t ? S : function (a, b, c) {
        if (R(a), b = H(b, !0), R(c), N) try {
            return S(a, b, c)
        } catch (d) {
        }
        if ("get" in c || "set" in c) throw TypeError("Accessors not supported");
        return "value" in c && (a[b] = c.value), a
    }, U = {f: T}, V = t ? function (a, b, c) {
        return U.f(a, b, z(1, c))
    } : function (a, b, c) {
        return a[b] = c, a
    }, W = function (a, b) {
        try {
            V(r, a, b)
        } catch (c) {
            r[a] = b
        }
        return b
    }, X = "__core-js_shared__", Y = r[X] || W(X, {}), Z = Y, $ = Function.toString, "function" != typeof Z.inspectSource && (Z.inspectSource = function (a) {
        return $.call(a)
    }), _ = Z.inspectSource, ab = r.WeakMap, bb = "function" == typeof ab && /native code/.test(_(ab)), cb = b(function (a) {
        (a.exports = function (a, b) {
            return Z[a] || (Z[a] = void 0 !== b ? b : {})
        })("versions", []).push({version: "3.6.0", mode: "global", copyright: "© 2019 Denis Pushkarev (zloirock.ru)"})
    }), db = 0, eb = Math.random(), fb = function (a) {
        return "Symbol(" + String(void 0 === a ? "" : a) + ")_" + (++db + eb).toString(36)
    }, gb = cb("keys"), hb = function (a) {
        return gb[a] || (gb[a] = fb(a))
    }, ib = {}, jb = r.WeakMap, nb = function (a) {
        return mb(a) ? lb(a) : kb(a, {})
    }, ob = function (a) {
        return function (b) {
            var c;
            if (!G(b) || (c = lb(b)).type !== a) throw TypeError("Incompatible receiver, " + a + " required");
            return c
        }
    }, bb ? (pb = new jb, qb = pb.get, rb = pb.has, sb = pb.set, kb = function (a, b) {
        return sb.call(pb, a, b), b
    }, lb = function (a) {
        return qb.call(pb, a) || {}
    }, mb = function (a) {
        return rb.call(pb, a)
    }) : (tb = hb("state"), ib[tb] = !0, kb = function (a, b) {
        return V(a, tb, b), b
    }, lb = function (a) {
        return J(a, tb) ? a[tb] : {}
    }, mb = function (a) {
        return J(a, tb)
    }), ub = {set: kb, get: lb, has: mb, enforce: nb, getterFor: ob}, vb = b(function (a) {
        var b = ub.get, c = ub.enforce, d = String(String).split("String");
        (a.exports = function (a, b, e, f) {
            var g = f ? !!f.unsafe : !1, h = f ? !!f.enumerable : !1, i = f ? !!f.noTargetGet : !1;
            return "function" == typeof e && ("string" != typeof b || J(e, "name") || V(e, "name", b), c(e).source = d.join("string" == typeof b ? b : "")), a === r ? (h ? a[b] = e : W(b, e), void 0) : (g ? !i && a[b] && (h = !0) : delete a[b], h ? a[b] = e : V(a, b, e), void 0)
        })(Function.prototype, "toString", function () {
            return "function" == typeof this && b(this).source || _(this)
        })
    }), wb = r, xb = function (a) {
        return "function" == typeof a ? a : void 0
    }, yb = function (a, b) {
        return arguments.length < 2 ? xb(wb[a]) || xb(r[a]) : wb[a] && wb[a][b] || r[a] && r[a][b]
    }, zb = Math.ceil, Ab = Math.floor, Bb = function (a) {
        return isNaN(a = +a) ? 0 : (a > 0 ? Ab : zb)(a)
    }, Cb = Math.min, Db = function (a) {
        return a > 0 ? Cb(Bb(a), 9007199254740991) : 0
    }, Eb = Math.max, Fb = Math.min, Gb = function (a, b) {
        var c = Bb(a);
        return 0 > c ? Eb(c + b, 0) : Fb(c, b)
    }, Hb = function (a) {
        return function (b, c, d) {
            var e, f = F(b), g = Db(f.length), h = Gb(d, g);
            if (a && c != c) {
                for (; g > h;) if (e = f[h++], e != e) return !0
            } else for (; g > h; h++) if ((a || h in f) && f[h] === c) return a || h || 0;
            return !a && -1
        }
    }, Ib = {includes: Hb(!0), indexOf: Hb(!1)}, Jb = Ib.indexOf, Kb = function (a, b) {
        var c, d = F(a), e = 0, f = [];
        for (c in d) !J(ib, c) && J(d, c) && f.push(c);
        for (; b.length > e;) J(d, c = b[e++]) && (~Jb(f, c) || f.push(c));
        return f
    }, Lb = ["constructor", "hasOwnProperty", "isPrototypeOf", "propertyIsEnumerable", "toLocaleString", "toString", "valueOf"], Mb = Lb.concat("length", "prototype"), Nb = Object.getOwnPropertyNames || function (a) {
        return Kb(a, Mb)
    }, Ob = {f: Nb}, Pb = Object.getOwnPropertySymbols, Qb = {f: Pb}, Rb = yb("Reflect", "ownKeys") || function (a) {
        var b = Ob.f(R(a)), c = Qb.f;
        return c ? b.concat(c(a)) : b
    }, Sb = function (a, b) {
        var c, d, e = Rb(b), f = U.f, g = Q.f;
        for (c = 0; c < e.length; c++) d = e[c], J(a, d) || f(a, d, g(b, d))
    }, Tb = /#|\.prototype\./, Ub = function (a, b) {
        var c = Wb[Vb(a)];
        return c == Yb ? !0 : c == Xb ? !1 : "function" == typeof b ? s(b) : !!b
    }, Vb = Ub.normalize = function (a) {
        return String(a).replace(Tb, ".").toLowerCase()
    }, Wb = Ub.data = {}, Xb = Ub.NATIVE = "N", Yb = Ub.POLYFILL = "P", Zb = Ub, $b = Q.f, _b = function (a, b) {
        var c, d, e, f, g, h, i = a.target, j = a.global, k = a.stat;
        if (d = j ? r : k ? r[i] || W(i, {}) : (r[i] || {}).prototype) for (e in b) {
            if (g = b[e], a.noTargetGet ? (h = $b(d, e), f = h && h.value) : f = d[e], c = Zb(j ? e : i + (k ? "." : "#") + e, a.forced), !c && void 0 !== f) {
                if (typeof g == typeof f) continue;
                Sb(g, f)
            }
            (a.sham || f && f.sham) && V(g, "sham", !0), vb(d, e, g, a)
        }
    }, ac = !!Object.getOwnPropertySymbols && !s(function () {
        return !String(Symbol())
    }), bc = ac && !Symbol.sham && "symbol" == typeof Symbol(), cc = Array.isArray || function (a) {
        return "Array" == B(a)
    }, dc = function (a) {
        return Object(E(a))
    }, ec = Object.keys || function (a) {
        return Kb(a, Lb)
    }, fc = t ? Object.defineProperties : function (a, b) {
        var c, d, e, f;
        for (R(a), c = ec(b), d = c.length, e = 0; d > e;) U.f(a, f = c[e++], b[f]);
        return a
    }, gc = yb("document", "documentElement"), hc = ">", ic = "<", jc = "prototype", kc = "script", lc = hb("IE_PROTO"), mc = function () {
    },nc = function (a) {
        return ic + kc + hc + a + ic + "/" + kc + hc
    },oc = function (a) {
        a.write(nc("")), a.close();
        var b = a.parentWindow.Object;
        return a = null, b
    },pc = function () {
        var a, b = M("iframe"), c = "java" + kc + ":";
        return b.style.display = "none", gc.appendChild(b), b.src = String(c), a = b.contentWindow.document, a.open(), a.write(nc("document.F=Object")), a.close(), a.F
    },rc = function () {
        try {
            qc = document.domain && new ActiveXObject("htmlfile")
        } catch (a) {
        }
        rc = qc ? oc(qc) : pc();
        for (var b = Lb.length; b--;) delete rc[jc][Lb[b]];
        return rc()
    },ib[lc] = !0,sc = Object.create || function (a, b) {
        var c;
        return null !== a ? (mc[jc] = R(a), c = new mc, mc[jc] = null, c[lc] = a) : c = rc(), void 0 === b ? c : fc(c, b)
    },tc = Ob.f,uc = {}.toString,vc = "object" == typeof window && window && Object.getOwnPropertyNames ? Object.getOwnPropertyNames(window) : [],wc = function (a) {
        try {
            return tc(a)
        } catch (b) {
            return vc.slice()
        }
    },xc = function (a) {
        return vc && "[object Window]" == uc.call(a) ? wc(a) : tc(F(a))
    },yc = {f: xc},zc = cb("wks"),Ac = r.Symbol,Bc = bc ? Ac : fb,Cc = function (a) {
        return J(zc, a) || (zc[a] = ac && J(Ac, a) ? Ac[a] : Bc("Symbol." + a)), zc[a]
    },Dc = Cc,Ec = {f: Dc},Fc = U.f,Gc = function (a) {
        var b = wb.Symbol || (wb.Symbol = {});
        J(b, a) || Fc(b, a, {value: Ec.f(a)})
    },Hc = U.f,Ic = Cc("toStringTag"),Jc = function (a, b, c) {
        a && !J(a = c ? a : a.prototype, Ic) && Hc(a, Ic, {configurable: !0, value: b})
    },Kc = function (a) {
        if ("function" != typeof a) throw TypeError(String(a) + " is not a function");
        return a
    },Lc = function (a, b, c) {
        if (Kc(a), void 0 === b) return a;
        switch (c) {
            case 0:
                return function () {
                    return a.call(b)
                };
            case 1:
                return function (c) {
                    return a.call(b, c)
                };
            case 2:
                return function (c, d) {
                    return a.call(b, c, d)
                };
            case 3:
                return function (c, d, e) {
                    return a.call(b, c, d, e)
                }
        }
        return function () {
            return a.apply(b, arguments)
        }
    },Mc = Cc("species"),Nc = function (a, b) {
        var c;
        return cc(a) && (c = a.constructor, "function" != typeof c || c !== Array && !cc(c.prototype) ? G(c) && (c = c[Mc], null === c && (c = void 0)) : c = void 0), new (void 0 === c ? Array : c)(0 === b ? 0 : b)
    },Oc = [].push,Pc = function (a) {
        var b = 1 == a, c = 2 == a, d = 3 == a, e = 4 == a, f = 6 == a, g = 5 == a || f;
        return function (h, i, j, k) {
            for (var l, m, n = dc(h), o = D(n), p = Lc(i, j, 3), q = Db(o.length), r = 0, s = k || Nc, t = b ? s(h, q) : c ? s(h, 0) : void 0; q > r; r++) if ((g || r in o) && (l = o[r], m = p(l, r, n), a)) if (b) t[r] = m; else if (m) switch (a) {
                case 3:
                    return !0;
                case 5:
                    return l;
                case 6:
                    return r;
                case 2:
                    Oc.call(t, l)
            } else if (e) return !1;
            return f ? -1 : d || e ? e : t
        }
    },Qc = {
        forEach: Pc(0),
        map: Pc(1),
        filter: Pc(2),
        some: Pc(3),
        every: Pc(4),
        find: Pc(5),
        findIndex: Pc(6)
    },Rc = Qc.forEach,Sc = hb("hidden"),Tc = "Symbol",Uc = "prototype",Vc = Cc("toPrimitive"),Wc = ub.set,Xc = ub.getterFor(Tc),Yc = Object[Uc],Zc = r.Symbol,$c = yb("JSON", "stringify"),_c = Q.f,ad = U.f,bd = yc.f,cd = y.f,dd = cb("symbols"),ed = cb("op-symbols"),fd = cb("string-to-symbol-registry"),gd = cb("symbol-to-string-registry"),hd = cb("wks"),id = r.QObject,jd = !id || !id[Uc] || !id[Uc].findChild,kd = t && s(function () {
        return 7 != sc(ad({}, "a", {
            get: function () {
                return ad(this, "a", {value: 7}).a
            }
        })).a
    }) ? function (a, b, c) {
        var d = _c(Yc, b);
        d && delete Yc[b], ad(a, b, c), d && a !== Yc && ad(Yc, b, d)
    } : ad,ld = function (a, b) {
        var c = dd[a] = sc(Zc[Uc]);
        return Wc(c, {type: Tc, tag: a, description: b}), t || (c.description = b), c
    },md = ac && "symbol" == typeof Zc.iterator ? function (a) {
        return "symbol" == typeof a
    } : function (a) {
        return Object(a) instanceof Zc
    },nd = function (a, b, c) {
        a === Yc && nd(ed, b, c), R(a);
        var d = H(b, !0);
        return R(c), J(dd, d) ? (c.enumerable ? (J(a, Sc) && a[Sc][d] && (a[Sc][d] = !1), c = sc(c, {enumerable: z(0, !1)})) : (J(a, Sc) || ad(a, Sc, z(1, {})), a[Sc][d] = !0), kd(a, d, c)) : ad(a, d, c)
    },od = function (a, b) {
        var c, d;
        return R(a), c = F(b), d = ec(c).concat(td(c)), Rc(d, function (b) {
            (!t || qd.call(c, b)) && nd(a, b, c[b])
        }), a
    },pd = function (a, b) {
        return void 0 === b ? sc(a) : od(sc(a), b)
    },qd = function (a) {
        var b = H(a, !0), c = cd.call(this, b);
        return this === Yc && J(dd, b) && !J(ed, b) ? !1 : c || !J(this, b) || !J(dd, b) || J(this, Sc) && this[Sc][b] ? c : !0
    },rd = function (a, b) {
        var c, d = F(a), e = H(b, !0);
        return d !== Yc || !J(dd, e) || J(ed, e) ? (c = _c(d, e), !c || !J(dd, e) || J(d, Sc) && d[Sc][e] || (c.enumerable = !0), c) : void 0
    },sd = function (a) {
        var b = bd(F(a)), c = [];
        return Rc(b, function (a) {
            J(dd, a) || J(ib, a) || c.push(a)
        }), c
    },td = function (a) {
        var b = a === Yc, c = bd(b ? ed : F(a)), d = [];
        return Rc(c, function (a) {
            !J(dd, a) || b && !J(Yc, a) || d.push(dd[a])
        }), d
    },ac || (Zc = function () {
        var a, b, c;
        if (this instanceof Zc) throw TypeError("Symbol is not a constructor");
        return a = arguments.length && void 0 !== arguments[0] ? String(arguments[0]) : void 0, b = fb(a), c = function (a) {
            this === Yc && c.call(ed, a), J(this, Sc) && J(this[Sc], b) && (this[Sc][b] = !1), kd(this, b, z(1, a))
        }, t && jd && kd(Yc, b, {configurable: !0, set: c}), ld(b, a)
    }, vb(Zc[Uc], "toString", function () {
        return Xc(this).tag
    }), y.f = qd, U.f = nd, Q.f = rd, Ob.f = yc.f = sd, Qb.f = td, t && (ad(Zc[Uc], "description", {
        configurable: !0,
        get: function () {
            return Xc(this).description
        }
    }), vb(Yc, "propertyIsEnumerable", qd, {unsafe: !0}))),bc || (Ec.f = function (a) {
        return ld(Cc(a), a)
    }),_b({global: !0, wrap: !0, forced: !ac, sham: !ac}, {Symbol: Zc}),Rc(ec(hd), function (a) {
        Gc(a)
    }),_b({target: Tc, stat: !0, forced: !ac}, {
        "for": function (a) {
            var b, c = String(a);
            return J(fd, c) ? fd[c] : (b = Zc(c), fd[c] = b, gd[b] = c, b)
        }, keyFor: function (a) {
            if (!md(a)) throw TypeError(a + " is not a symbol");
            return J(gd, a) ? gd[a] : void 0
        }, useSetter: function () {
            jd = !0
        }, useSimple: function () {
            jd = !1
        }
    }),_b({target: "Object", stat: !0, forced: !ac, sham: !t}, {
        create: pd,
        defineProperty: nd,
        defineProperties: od,
        getOwnPropertyDescriptor: rd
    }),_b({target: "Object", stat: !0, forced: !ac}, {
        getOwnPropertyNames: sd,
        getOwnPropertySymbols: td
    }),_b({
        target: "Object", stat: !0, forced: s(function () {
            Qb.f(1)
        })
    }, {
        getOwnPropertySymbols: function (a) {
            return Qb.f(dc(a))
        }
    }),$c && (ud = !ac || s(function () {
        var a = Zc();
        return "[null]" != $c([a]) || "{}" != $c({a: a}) || "{}" != $c(Object(a))
    }), _b({target: "JSON", stat: !0, forced: ud}, {
        stringify: function (a, b) {
            for (var c, d = [a], e = 1; arguments.length > e;) d.push(arguments[e++]);
            return c = b, !G(b) && void 0 === a || md(a) ? void 0 : (cc(b) || (b = function (a, b) {
                return "function" == typeof c && (b = c.call(this, a, b)), md(b) ? void 0 : b
            }), d[1] = b, $c.apply(null, d))
        }
    })),Zc[Uc][Vc] || V(Zc[Uc], Vc, Zc[Uc].valueOf),Jc(Zc, Tc),ib[Sc] = !0,vd = U.f,wd = r.Symbol,!t || "function" != typeof wd || "description" in wd.prototype && void 0 === wd().description || (xd = {}, yd = function () {
        var a = arguments.length < 1 || void 0 === arguments[0] ? void 0 : String(arguments[0]),
            b = this instanceof yd ? new wd(a) : void 0 === a ? wd() : wd(a);
        return "" === a && (xd[b] = !0), b
    }, Sb(yd, wd), zd = yd.prototype = wd.prototype, zd.constructor = yd, Ad = zd.toString, Bd = "Symbol(test)" == String(wd("test")), Cd = /^Symbol\((.*)\)[^)]+$/, vd(zd, "description", {
        configurable: !0,
        get: function () {
            var a, b = G(this) ? this.valueOf() : this, c = Ad.call(b);
            return J(xd, b) ? "" : (a = Bd ? c.slice(7, -1) : c.replace(Cd, "$1"), "" === a ? void 0 : a)
        }
    }), _b({global: !0, forced: !0}, {Symbol: yd})),Gc("iterator"),Dd = function (a, b, c) {
        var d = H(b);
        d in a ? U.f(a, d, z(0, c)) : a[d] = c
    },Ed = yb("navigator", "userAgent") || "",Fd = r.process,Gd = Fd && Fd.versions,Hd = Gd && Gd.v8,Hd ? (Id = Hd.split("."), Jd = Id[0] + Id[1]) : Ed && (Id = Ed.match(/Edge\/(\d+)/), (!Id || Id[1] >= 74) && (Id = Ed.match(/Chrome\/(\d+)/), Id && (Jd = Id[1]))),Kd = Jd && +Jd,Ld = Cc("species"),Md = function (a) {
        return Kd >= 51 || !s(function () {
            var b = [], c = b.constructor = {};
            return c[Ld] = function () {
                return {foo: 1}
            }, 1 !== b[a](Boolean).foo
        })
    },Nd = Cc("isConcatSpreadable"),Od = 9007199254740991,Pd = "Maximum allowed index exceeded",Qd = Kd >= 51 || !s(function () {
        var a = [];
        return a[Nd] = !1, a.concat()[0] !== a
    }),Rd = Md("concat"),Sd = function (a) {
        if (!G(a)) return !1;
        var b = a[Nd];
        return void 0 !== b ? !!b : cc(a)
    },Td = !Qd || !Rd,_b({target: "Array", proto: !0, forced: Td}, {
        concat: function () {
            var a, b, c, d, e, f = dc(this), g = Nc(f, 0), h = 0;
            for (a = -1, c = arguments.length; c > a; a++) if (e = -1 === a ? f : arguments[a], Sd(e)) {
                if (d = Db(e.length), h + d > Od) throw TypeError(Pd);
                for (b = 0; d > b; b++, h++) b in e && Dd(g, h, e[b])
            } else {
                if (h >= Od) throw TypeError(Pd);
                Dd(g, h++, e)
            }
            return g.length = h, g
        }
    }),Ud = Qc.filter,Vd = Md("filter"),Wd = Vd && !s(function () {
        [].filter.call({length: -1, 0: 1}, function (a) {
            throw a
        })
    }),_b({target: "Array", proto: !0, forced: !Vd || !Wd}, {
        filter: function (a) {
            return Ud(this, a, arguments.length > 1 ? arguments[1] : void 0)
        }
    }),Xd = Cc("unscopables"),Yd = Array.prototype,void 0 == Yd[Xd] && U.f(Yd, Xd, {
        configurable: !0,
        value: sc(null)
    }),Zd = function (a) {
        Yd[Xd][a] = !0
    },$d = Qc.find,_d = "find",ae = !0,_d in [] && Array(1)[_d](function () {
        ae = !1
    }),_b({target: "Array", proto: !0, forced: ae}, {
        find: function (a) {
            return $d(this, a, arguments.length > 1 ? arguments[1] : void 0)
        }
    }),Zd(_d),be = Qc.findIndex,ce = "findIndex",de = !0,ce in [] && Array(1)[ce](function () {
        de = !1
    }),_b({target: "Array", proto: !0, forced: de}, {
        findIndex: function (a) {
            return be(this, a, arguments.length > 1 ? arguments[1] : void 0)
        }
    }),Zd(ce),ee = Ib.includes,_b({target: "Array", proto: !0}, {
        includes: function (a) {
            return ee(this, a, arguments.length > 1 ? arguments[1] : void 0)
        }
    }),Zd("includes"),fe = function (a, b) {
        var c = [][a];
        return !c || !s(function () {
            c.call(null, b || function () {
                throw 1
            }, 1)
        })
    },ge = Ib.indexOf,he = [].indexOf,ie = !!he && 1 / [1].indexOf(1, -0) < 0,je = fe("indexOf"),_b({
        target: "Array",
        proto: !0,
        forced: ie || je
    }, {
        indexOf: function (a) {
            return ie ? he.apply(this, arguments) || 0 : ge(this, a, arguments.length > 1 ? arguments[1] : void 0)
        }
    }),ke = !s(function () {
        function a() {
        }

        return a.prototype.constructor = null, Object.getPrototypeOf(new a) !== a.prototype
    }),le = hb("IE_PROTO"),me = Object.prototype,ne = ke ? Object.getPrototypeOf : function (a) {
        return a = dc(a), J(a, le) ? a[le] : "function" == typeof a.constructor && a instanceof a.constructor ? a.constructor.prototype : a instanceof Object ? me : null
    },oe = Cc("iterator"),pe = !1,qe = function () {
        return this
    },[].keys && (te = [].keys(), "next" in te ? (se = ne(ne(te)), se !== Object.prototype && (re = se)) : pe = !0),void 0 == re && (re = {}),J(re, oe) || V(re, oe, qe),ue = {
        IteratorPrototype: re,
        BUGGY_SAFARI_ITERATORS: pe
    },ve = ue.IteratorPrototype,we = function (a, b, c) {
        var d = b + " Iterator";
        return a.prototype = sc(ve, {next: z(1, c)}), Jc(a, d, !1), a
    },xe = function (a) {
        if (!G(a) && null !== a) throw TypeError("Can't set " + String(a) + " as a prototype");
        return a
    },ye = Object.setPrototypeOf || ("__proto__" in {} ? function () {
        var a, b = !1, c = {};
        try {
            a = Object.getOwnPropertyDescriptor(Object.prototype, "__proto__").set, a.call(c, []), b = c instanceof Array
        } catch (d) {
        }
        return function (c, d) {
            return R(c), xe(d), b ? a.call(c, d) : c.__proto__ = d, c
        }
    }() : void 0),ze = ue.IteratorPrototype,Ae = ue.BUGGY_SAFARI_ITERATORS,Be = Cc("iterator"),Ce = "keys",De = "values",Ee = "entries",Fe = function () {
        return this
    },Ge = function (a, b, c, d, e, f, g) {
        var h, i, j, k, l, m, n, o, p, q;
        if (we(c, b, d), h = function (a) {
            if (a === e && m) return m;
            if (!Ae && a in k) return k[a];
            switch (a) {
                case Ce:
                    return function () {
                        return new c(this, a)
                    };
                case De:
                    return function () {
                        return new c(this, a)
                    };
                case Ee:
                    return function () {
                        return new c(this, a)
                    }
            }
            return function () {
                return new c(this)
            }
        }, i = b + " Iterator", j = !1, k = a.prototype, l = k[Be] || k["@@iterator"] || e && k[e], m = !Ae && l || h(e), n = "Array" == b ? k.entries || l : l, n && (o = ne(n.call(new a)), ze !== Object.prototype && o.next && (ne(o) !== ze && (ye ? ye(o, ze) : "function" != typeof o[Be] && V(o, Be, Fe)), Jc(o, i, !0))), e == De && l && l.name !== De && (j = !0, m = function () {
            return l.call(this)
        }), k[Be] !== m && V(k, Be, m), e) if (p = {
            values: h(De),
            keys: f ? m : h(Ce),
            entries: h(Ee)
        }, g) for (q in p) !Ae && !j && q in k || vb(k, q, p[q]); else _b({target: b, proto: !0, forced: Ae || j}, p);
        return p
    },He = "Array Iterator",Ie = ub.set,Je = ub.getterFor(He),Ke = Ge(Array, "Array", function (a, b) {
        Ie(this, {type: He, target: F(a), index: 0, kind: b})
    }, function () {
        var a = Je(this), b = a.target, c = a.kind, d = a.index++;
        return !b || d >= b.length ? (a.target = void 0, {value: void 0, done: !0}) : "keys" == c ? {
            value: d,
            done: !1
        } : "values" == c ? {value: b[d], done: !1} : {value: [d, b[d]], done: !1}
    }, "values"),Zd("keys"),Zd("values"),Zd("entries"),Le = [].join,Me = D != Object,Ne = fe("join", ","),_b({
        target: "Array",
        proto: !0,
        forced: Me || Ne
    }, {
        join: function (a) {
            return Le.call(F(this), void 0 === a ? "," : a)
        }
    }),Oe = Qc.map,Pe = Md("map"),Qe = Pe && !s(function () {
        [].map.call({length: -1, 0: 1}, function (a) {
            throw a
        })
    }),_b({target: "Array", proto: !0, forced: !Pe || !Qe}, {
        map: function (a) {
            return Oe(this, a, arguments.length > 1 ? arguments[1] : void 0)
        }
    }),Re = [].reverse,Se = [1, 2],_b({
        target: "Array",
        proto: !0,
        forced: String(Se) === String(Se.reverse())
    }, {
        reverse: function () {
            return cc(this) && (this.length = this.length), Re.call(this)
        }
    }),Te = Cc("species"),Ue = [].slice,Ve = Math.max,_b({
        target: "Array",
        proto: !0,
        forced: !Md("slice")
    }, {
        slice: function (a, b) {
            var c, d, e, f = F(this), g = Db(f.length), h = Gb(a, g), i = Gb(void 0 === b ? g : b, g);
            if (cc(f) && (c = f.constructor, "function" != typeof c || c !== Array && !cc(c.prototype) ? G(c) && (c = c[Te], null === c && (c = void 0)) : c = void 0, c === Array || void 0 === c)) return Ue.call(f, h, i);
            for (d = new (void 0 === c ? Array : c)(Ve(i - h, 0)), e = 0; i > h; h++, e++) h in f && Dd(d, e, f[h]);
            return d.length = e, d
        }
    }),We = [],Xe = We.sort,Ye = s(function () {
        We.sort(void 0)
    }),Ze = s(function () {
        We.sort(null)
    }),$e = fe("sort"),_e = Ye || !Ze || $e,_b({target: "Array", proto: !0, forced: _e}, {
        sort: function (a) {
            return void 0 === a ? Xe.call(dc(this)) : Xe.call(dc(this), Kc(a))
        }
    }),af = Math.max,bf = Math.min,cf = 9007199254740991,df = "Maximum allowed length exceeded",_b({
        target: "Array",
        proto: !0,
        forced: !Md("splice")
    }, {
        splice: function (a, b) {
            var c, d, e, f, g, h, i = dc(this), j = Db(i.length), k = Gb(a, j), l = arguments.length;
            if (0 === l ? c = d = 0 : 1 === l ? (c = 0, d = j - k) : (c = l - 2, d = bf(af(Bb(b), 0), j - k)), j + c - d > cf) throw TypeError(df);
            for (e = Nc(i, d), f = 0; d > f; f++) g = k + f, g in i && Dd(e, f, i[g]);
            if (e.length = d, d > c) {
                for (f = k; j - d > f; f++) g = f + d, h = f + c, g in i ? i[h] = i[g] : delete i[h];
                for (f = j; f > j - d + c; f--) delete i[f - 1]
            } else if (c > d) for (f = j - d; f > k; f--) g = f + d - 1, h = f + c - 1, g in i ? i[h] = i[g] : delete i[h];
            for (f = 0; c > f; f++) i[f + k] = arguments[f + 2];
            return i.length = j - d + c, e
        }
    }),ef = function (a, b, c) {
        var d, e;
        return ye && "function" == typeof (d = b.constructor) && d !== c && G(e = d.prototype) && e !== c.prototype && ye(a, e), a
    },ff = "	\n\f\r                　\u2028\u2029",gf = "[" + ff + "]",hf = RegExp("^" + gf + gf + "*"),jf = RegExp(gf + gf + "*$"),kf = function (a) {
        return function (b) {
            var c = String(E(b));
            return 1 & a && (c = c.replace(hf, "")), 2 & a && (c = c.replace(jf, "")), c
        }
    },lf = {
        start: kf(1),
        end: kf(2),
        trim: kf(3)
    },mf = Ob.f,nf = Q.f,of = U.f,pf = lf.trim,qf = "Number",rf = r[qf],sf = rf.prototype,tf = B(sc(sf)) == qf,uf = function (a) {
        var b, c, d, e, f, g, h, i, j = H(a, !1);
        if ("string" == typeof j && j.length > 2) if (j = pf(j), b = j.charCodeAt(0), 43 === b || 45 === b) {
            if (c = j.charCodeAt(2), 88 === c || 120 === c) return 0 / 0
        } else if (48 === b) {
            switch (j.charCodeAt(1)) {
                case 66:
                case 98:
                    d = 2, e = 49;
                    break;
                case 79:
                case 111:
                    d = 8, e = 55;
                    break;
                default:
                    return +j
            }
            for (f = j.slice(2), g = f.length, h = 0; g > h; h++) if (i = f.charCodeAt(h), 48 > i || i > e) return 0 / 0;
            return parseInt(f, d)
        }
        return +j
    },Zb(qf, !rf(" 0o1") || !rf("0b1") || rf("+0x1"))) {
        for (vf = function (a) {
            var b = arguments.length < 1 ? 0 : a, c = this;
            return c instanceof vf && (tf ? s(function () {
                sf.valueOf.call(c)
            }) : B(c) != qf) ? ef(new rf(uf(b)), c, vf) : uf(b)
        }, xf = t ? mf(rf) : "MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","), yf = 0; xf.length > yf; yf++) J(rf, wf = xf[yf]) && !J(vf, wf) && of(vf, wf, nf(rf, wf));
        vf.prototype = sf, sf.constructor = vf, vb(r, qf, vf)
    }
    zf = Object.assign, Af = Object.defineProperty, Bf = !zf || s(function () {
        var a, b, c, d;
        return t && 1 !== zf({b: 1}, zf(Af({}, "a", {
            enumerable: !0, get: function () {
                Af(this, "b", {value: 3, enumerable: !1})
            }
        }), {b: 2})).b ? !0 : (a = {}, b = {}, c = Symbol(), d = "abcdefghijklmnopqrst", a[c] = 7, d.split("").forEach(function (a) {
            b[a] = a
        }), 7 != zf({}, a)[c] || ec(zf({}, b)).join("") != d)
    }) ? function (a) {
        for (var b, c, d, e, f, g = dc(a), h = arguments.length, i = 1, j = Qb.f, k = y.f; h > i;) for (b = D(arguments[i++]), c = j ? ec(b).concat(j(b)) : ec(b), d = c.length, e = 0; d > e;) f = c[e++], (!t || k.call(b, f)) && (g[f] = b[f]);
        return g
    } : zf, _b({target: "Object", stat: !0, forced: Object.assign !== Bf}, {assign: Bf}), Cf = y.f, Df = function (a) {
        return function (b) {
            for (var c, d = F(b), e = ec(d), f = e.length, g = 0, h = []; f > g;) c = e[g++], (!t || Cf.call(d, c)) && h.push(a ? [c, d[c]] : d[c]);
            return h
        }
    }, Ef = {entries: Df(!0), values: Df(!1)}, Ff = Ef.entries, _b({
        target: "Object",
        stat: !0
    }, {
        entries: function (a) {
            return Ff(a)
        }
    }), Gf = Cc("toStringTag"), Hf = {}, Hf[Gf] = "z", If = "[object z]" === String(Hf), Jf = Cc("toStringTag"), Kf = "Arguments" == B(function () {
        return arguments
    }()), Lf = function (a, b) {
        try {
            return a[b]
        } catch (c) {
        }
    }, Mf = If ? B : function (a) {
        var b, c, d;
        return void 0 === a ? "Undefined" : null === a ? "Null" : "string" == typeof (c = Lf(b = Object(a), Jf)) ? c : Kf ? B(b) : "Object" == (d = B(b)) && "function" == typeof b.callee ? "Arguments" : d
    }, Nf = If ? {}.toString : function () {
        return "[object " + Mf(this) + "]"
    }, If || vb(Object.prototype, "toString", Nf, {unsafe: !0}), Of = lf.trim, Pf = r.parseFloat, Qf = 1 / Pf(ff + "-0") !== -1 / 0, Rf = Qf ? function (a) {
        var b = Of(String(a)), c = Pf(b);
        return 0 === c && "-" == b.charAt(0) ? -0 : c
    } : Pf, _b({
        global: !0,
        forced: parseFloat != Rf
    }, {parseFloat: Rf}), Sf = lf.trim, Tf = r.parseInt, Uf = /^[+-]?0[Xx]/, Vf = 8 !== Tf(ff + "08") || 22 !== Tf(ff + "0x16"), Wf = Vf ? function (a, b) {
        var c = Sf(String(a));
        return Tf(c, b >>> 0 || (Uf.test(c) ? 16 : 10))
    } : Tf, _b({global: !0, forced: parseInt != Wf}, {parseInt: Wf}), Xf = function () {
        var a = R(this), b = "";
        return a.global && (b += "g"), a.ignoreCase && (b += "i"), a.multiline && (b += "m"), a.dotAll && (b += "s"), a.unicode && (b += "u"), a.sticky && (b += "y"), b
    }, Yf = s(function () {
        var a = c("a", "y");
        return a.lastIndex = 2, null != a.exec("abcd")
    }), Zf = s(function () {
        var a = c("^r", "gy");
        return a.lastIndex = 2, null != a.exec("str")
    }), $f = {
        UNSUPPORTED_Y: Yf,
        BROKEN_CARET: Zf
    }, _f = RegExp.prototype.exec, ag = String.prototype.replace, bg = _f, cg = function () {
        var a = /a/, b = /b*/g;
        return _f.call(a, "a"), _f.call(b, "a"), 0 !== a.lastIndex || 0 !== b.lastIndex
    }(), dg = $f.UNSUPPORTED_Y || $f.BROKEN_CARET, eg = void 0 !== /()??/.exec("")[1], fg = cg || eg || dg, fg && (bg = function (a) {
        var b, c, d, e, f = this, g = dg && f.sticky, h = Xf.call(f), i = f.source, j = 0, k = a;
        return g && (h = h.replace("y", ""), -1 === h.indexOf("g") && (h += "g"), k = String(a).slice(f.lastIndex), f.lastIndex > 0 && (!f.multiline || f.multiline && "\n" !== a[f.lastIndex - 1]) && (i = "(?: " + i + ")", k = " " + k, j++), c = new RegExp("^(?:" + i + ")", h)), eg && (c = new RegExp("^" + i + "$(?!\\s)", h)), cg && (b = f.lastIndex), d = _f.call(g ? c : f, k), g ? d ? (d.input = d.input.slice(j), d[0] = d[0].slice(j), d.index = f.lastIndex, f.lastIndex += d[0].length) : f.lastIndex = 0 : cg && d && (f.lastIndex = f.global ? d.index + d[0].length : b), eg && d && d.length > 1 && ag.call(d[0], c, function () {
            for (e = 1; e < arguments.length - 2; e++) void 0 === arguments[e] && (d[e] = void 0)
        }), d
    }), gg = bg, _b({
        target: "RegExp",
        proto: !0,
        forced: /./.exec !== gg
    }, {exec: gg}), hg = "toString", ig = RegExp.prototype, jg = ig[hg], kg = s(function () {
        return "/a/b" != jg.call({source: "a", flags: "b"})
    }), lg = jg.name != hg, (kg || lg) && vb(RegExp.prototype, hg, function () {
        var a = R(this), b = String(a.source), c = a.flags,
            d = String(void 0 === c && a instanceof RegExp && !("flags" in ig) ? Xf.call(a) : c);
        return "/" + b + "/" + d
    }, {unsafe: !0}), mg = Cc("match"), ng = function (a) {
        var b;
        return G(a) && (void 0 !== (b = a[mg]) ? !!b : "RegExp" == B(a))
    }, og = function (a) {
        if (ng(a)) throw TypeError("The method doesn't accept regular expressions");
        return a
    }, pg = Cc("match"), qg = function (a) {
        var b = /./;
        try {
            "/./"[a](b)
        } catch (c) {
            try {
                return b[pg] = !1, "/./"[a](b)
            } catch (d) {
            }
        }
        return !1
    }, _b({target: "String", proto: !0, forced: !qg("includes")}, {
        includes: function (a) {
            return !!~String(E(this)).indexOf(og(a), arguments.length > 1 ? arguments[1] : void 0)
        }
    }), rg = function (a) {
        return function (b, c) {
            var d, e, f = String(E(b)), g = Bb(c), h = f.length;
            return 0 > g || g >= h ? a ? "" : void 0 : (d = f.charCodeAt(g), 55296 > d || d > 56319 || g + 1 === h || (e = f.charCodeAt(g + 1)) < 56320 || e > 57343 ? a ? f.charAt(g) : d : a ? f.slice(g, g + 2) : (d - 55296 << 10) + (e - 56320) + 65536)
        }
    }, sg = {
        codeAt: rg(!1),
        charAt: rg(!0)
    }, tg = sg.charAt, ug = "String Iterator", vg = ub.set, wg = ub.getterFor(ug), Ge(String, "String", function (a) {
        vg(this, {type: ug, string: String(a), index: 0})
    }, function () {
        var a, b = wg(this), c = b.string, d = b.index;
        return d >= c.length ? {value: void 0, done: !0} : (a = tg(c, d), b.index += a.length, {value: a, done: !1})
    }), xg = Cc("species"), yg = !s(function () {
        var a = /./;
        return a.exec = function () {
            var a = [];
            return a.groups = {a: "7"}, a
        }, "7" !== "".replace(a, "$<a>")
    }), zg = function () {
        return "$0" === "a".replace(/./, "$0")
    }(), Ag = !s(function () {
        var a, b = /(?:)/, c = b.exec;
        return b.exec = function () {
            return c.apply(this, arguments)
        }, a = "ab".split(b), 2 !== a.length || "a" !== a[0] || "b" !== a[1]
    }), Bg = function (a, b, c, d) {
        var e, f, g, h, i = Cc(a), j = !s(function () {
            var b = {};
            return b[i] = function () {
                return 7
            }, 7 != ""[a](b)
        }), k = j && !s(function () {
            var b = !1, c = /a/;
            return "split" === a && (c = {}, c.constructor = {}, c.constructor[xg] = function () {
                return c
            }, c.flags = "", c[i] = /./[i]), c.exec = function () {
                return b = !0, null
            }, c[i](""), !b
        });
        j && k && ("replace" !== a || yg && zg) && ("split" !== a || Ag) || (e = /./[i], f = c(i, ""[a], function (a, b, c, d, f) {
            return b.exec === gg ? j && !f ? {done: !0, value: e.call(b, c, d)} : {
                done: !0,
                value: a.call(c, b, d)
            } : {done: !1}
        }, {REPLACE_KEEPS_$0: zg}), g = f[0], h = f[1], vb(String.prototype, a, g), vb(RegExp.prototype, i, 2 == b ? function (a, b) {
            return h.call(a, this, b)
        } : function (a) {
            return h.call(a, this)
        })), d && V(RegExp.prototype[i], "sham", !0)
    }, Cg = sg.charAt, Dg = function (a, b, c) {
        return b + (c ? Cg(a, b).length : 1)
    }, Eg = function (a, b) {
        var c, d = a.exec;
        if ("function" == typeof d) {
            if (c = d.call(a, b), "object" != typeof c) throw TypeError("RegExp exec method returned something other than an Object or null");
            return c
        }
        if ("RegExp" !== B(a)) throw TypeError("RegExp#exec called on incompatible receiver");
        return gg.call(a, b)
    }, Fg = Math.max, Gg = Math.min, Hg = Math.floor, Ig = /\$([$&'`]|\d\d?|<[^>]*>)/g, Jg = /\$([$&'`]|\d\d?)/g, Kg = function (a) {
        return void 0 === a ? a : String(a)
    }, Bg("replace", 2, function (a, b, c, d) {
        function e(a, c, d, e, f, g) {
            var h = d + a.length, i = e.length, j = Jg;
            return void 0 !== f && (f = dc(f), j = Ig), b.call(g, j, function (b, g) {
                var j, k, l;
                switch (g.charAt(0)) {
                    case"$":
                        return "$";
                    case"&":
                        return a;
                    case"`":
                        return c.slice(0, d);
                    case"'":
                        return c.slice(h);
                    case"<":
                        j = f[g.slice(1, -1)];
                        break;
                    default:
                        if (k = +g, 0 === k) return b;
                        if (k > i) return l = Hg(k / 10), 0 === l ? b : i >= l ? void 0 === e[l - 1] ? g.charAt(1) : e[l - 1] + g.charAt(1) : b;
                        j = e[k - 1]
                }
                return void 0 === j ? "" : j
            })
        }

        return [function (c, d) {
            var e = E(this), f = void 0 == c ? void 0 : c[a];
            return void 0 !== f ? f.call(c, e, d) : b.call(String(e), c, d)
        }, function (a, f) {
            var g, h, i, j, k, l, m, n, o, p, q, r, s, t, u, v, w, x, y;
            if ((d.REPLACE_KEEPS_$0 || "string" == typeof f && -1 === f.indexOf("$0")) && (g = c(b, a, this, f), g.done)) return g.value;
            for (h = R(a), i = String(this), j = "function" == typeof f, j || (f = String(f)), k = h.global, k && (l = h.unicode, h.lastIndex = 0), m = []; (n = Eg(h, i), null !== n) && (m.push(n), k);) o = String(n[0]), "" === o && (h.lastIndex = Dg(i, Db(h.lastIndex), l));
            for (p = "", q = 0, r = 0; r < m.length; r++) {
                for (n = m[r], s = String(n[0]), t = Fg(Gg(Bb(n.index), i.length), 0), u = [], v = 1; v < n.length; v++) u.push(Kg(n[v]));
                w = n.groups, j ? (x = [s].concat(u, t, i), void 0 !== w && x.push(w), y = String(f.apply(void 0, x))) : y = e(s, i, t, u, w, f), t >= q && (p += i.slice(q, t) + y, q = t + s.length)
            }
            return p + i.slice(q)
        }]
    }), Lg = Object.is || function (a, b) {
        return a === b ? 0 !== a || 1 / a === 1 / b : a != a && b != b
    }, Bg("search", 1, function (a, b, c) {
        return [function (b) {
            var c = E(this), d = void 0 == b ? void 0 : b[a];
            return void 0 !== d ? d.call(b, c) : new RegExp(b)[a](String(c))
        }, function (a) {
            var d, e, f, g, h = c(b, a, this);
            return h.done ? h.value : (d = R(a), e = String(this), f = d.lastIndex, Lg(f, 0) || (d.lastIndex = 0), g = Eg(d, e), Lg(d.lastIndex, f) || (d.lastIndex = f), null === g ? -1 : g.index)
        }]
    }), Mg = Cc("species"), Ng = function (a, b) {
        var c, d = R(a).constructor;
        return void 0 === d || void 0 == (c = R(d)[Mg]) ? b : Kc(c)
    }, Og = [].push, Pg = Math.min, Qg = 4294967295, Rg = !s(function () {
        return !RegExp(Qg, "y")
    }), Bg("split", 2, function (a, b, c) {
        var d;
        return d = "c" == "abbc".split(/(b)*/)[1] || 4 != "test".split(/(?:)/, -1).length || 2 != "ab".split(/(?:ab)*/).length || 4 != ".".split(/(.?)(.?)/).length || ".".split(/()()/).length > 1 || "".split(/.?/).length ? function (a, c) {
            var d, e, f, g, h, i, j, k = String(E(this)), l = void 0 === c ? Qg : c >>> 0;
            if (0 === l) return [];
            if (void 0 === a) return [k];
            if (!ng(a)) return b.call(k, a, l);
            for (d = [], e = (a.ignoreCase ? "i" : "") + (a.multiline ? "m" : "") + (a.unicode ? "u" : "") + (a.sticky ? "y" : ""), f = 0, g = new RegExp(a.source, e + "g"); (h = gg.call(g, k)) && (i = g.lastIndex, !(i > f && (d.push(k.slice(f, h.index)), h.length > 1 && h.index < k.length && Og.apply(d, h.slice(1)), j = h[0].length, f = i, d.length >= l)));) g.lastIndex === h.index && g.lastIndex++;
            return f === k.length ? (j || !g.test("")) && d.push("") : d.push(k.slice(f)), d.length > l ? d.slice(0, l) : d
        } : "0".split(void 0, 0).length ? function (a, c) {
            return void 0 === a && 0 === c ? [] : b.call(this, a, c)
        } : b, [function (b, c) {
            var e = E(this), f = void 0 == b ? void 0 : b[a];
            return void 0 !== f ? f.call(b, e, c) : d.call(String(e), b, c)
        }, function (a, e) {
            var f, g, h, i, j, k, l, m, n, o, p, q, r, s = c(d, a, this, e, d !== b);
            if (s.done) return s.value;
            if (f = R(a), g = String(this), h = Ng(f, RegExp), i = f.unicode, j = (f.ignoreCase ? "i" : "") + (f.multiline ? "m" : "") + (f.unicode ? "u" : "") + (Rg ? "y" : "g"), k = new h(Rg ? f : "^(?:" + f.source + ")", j), l = void 0 === e ? Qg : e >>> 0, 0 === l) return [];
            if (0 === g.length) return null === Eg(k, g) ? [g] : [];
            for (m = 0, n = 0, o = []; n < g.length;) if (k.lastIndex = Rg ? n : 0, p = Eg(k, Rg ? g : g.slice(n)), null === p || (q = Pg(Db(k.lastIndex + (Rg ? 0 : n)), g.length)) === m) n = Dg(g, n, i); else {
                if (o.push(g.slice(m, n)), o.length === l) return o;
                for (r = 1; r <= p.length - 1; r++) if (o.push(p[r]), o.length === l) return o;
                n = m = q
            }
            return o.push(g.slice(m)), o
        }]
    }, !Rg), Sg = "​᠎", Tg = function (a) {
        return s(function () {
            return !!ff[a]() || Sg[a]() != Sg || ff[a].name !== a
        })
    }, Ug = lf.trim, _b({target: "String", proto: !0, forced: Tg("trim")}, {
        trim: function () {
            return Ug(this)
        }
    }), Vg = {
        CSSRuleList: 0,
        CSSStyleDeclaration: 0,
        CSSValueList: 0,
        ClientRectList: 0,
        DOMRectList: 0,
        DOMStringList: 0,
        DOMTokenList: 1,
        DataTransferItemList: 0,
        FileList: 0,
        HTMLAllCollection: 0,
        HTMLCollection: 0,
        HTMLFormElement: 0,
        HTMLSelectElement: 0,
        MediaList: 0,
        MimeTypeArray: 0,
        NamedNodeMap: 0,
        NodeList: 1,
        PaintRequestList: 0,
        Plugin: 0,
        PluginArray: 0,
        SVGLengthList: 0,
        SVGNumberList: 0,
        SVGPathSegList: 0,
        SVGPointList: 0,
        SVGStringList: 0,
        SVGTransformList: 0,
        SourceBufferList: 0,
        StyleSheetList: 0,
        TextTrackCueList: 0,
        TextTrackList: 0,
        TouchList: 0
    }, Wg = Qc.forEach, Xg = fe("forEach") ? function (a) {
        return Wg(this, a, arguments.length > 1 ? arguments[1] : void 0)
    } : [].forEach;
    for (Yg in Vg) if (Zg = r[Yg], $g = Zg && Zg.prototype, $g && $g.forEach !== Xg) try {
        V($g, "forEach", Xg)
    } catch (Hh) {
        $g.forEach = Xg
    }
    _g = Cc("iterator"), ah = Cc("toStringTag"), bh = Ke.values;
    for (ch in Vg) if (dh = r[ch], eh = dh && dh.prototype) {
        if (eh[_g] !== bh) try {
            V(eh, _g, bh)
        } catch (Hh) {
            eh[_g] = bh
        }
        if (eh[ah] || V(eh, ah, ch), Vg[ch]) for (fh in Ke) if (eh[fh] !== Ke[fh]) try {
            V(eh, fh, Ke[fh])
        } catch (Hh) {
            eh[fh] = Ke[fh]
        }
    }
    gh = "1.17.1", hh = 4;
    try {
        ih = a.fn.dropdown.Constructor.VERSION, void 0 !== ih && (hh = parseInt(ih, 10))
    } catch (Ih) {
    }
    try {
        jh = bootstrap.Tooltip.VERSION, void 0 !== jh && (hh = parseInt(jh, 10))
    } catch (Ih) {
    }
    return kh = {
        3: {
            iconsPrefix: "glyphicon",
            icons: {
                paginationSwitchDown: "glyphicon-collapse-down icon-chevron-down",
                paginationSwitchUp: "glyphicon-collapse-up icon-chevron-up",
                refresh: "glyphicon-refresh icon-refresh",
                toggleOff: "glyphicon-list-alt icon-list-alt",
                toggleOn: "glyphicon-list-alt icon-list-alt",
                columns: "glyphicon-th icon-th",
                detailOpen: "glyphicon-plus icon-plus",
                detailClose: "glyphicon-minus icon-minus",
                fullscreen: "glyphicon-fullscreen",
                search: "glyphicon-search",
                clearSearch: "glyphicon-trash"
            },
            classes: {
                buttonsPrefix: "btn",
                buttons: "default",
                buttonsGroup: "btn-group",
                buttonsDropdown: "btn-group",
                pull: "pull",
                inputGroup: "input-group",
                inputPrefix: "input-",
                input: "form-control",
                paginationDropdown: "btn-group dropdown",
                dropup: "dropup",
                dropdownActive: "active",
                paginationActive: "active",
                buttonActive: "active"
            },
            html: {
                toolbarDropdown: ['<ul class="dropdown-menu" role="menu">', "</ul>"],
                toolbarDropdownItem: '<li class="dropdown-item-marker" role="menuitem"><label>%s</label></li>',
                toolbarDropdownSeparator: '<li class="divider"></li>',
                pageDropdown: ['<ul class="dropdown-menu" role="menu">', "</ul>"],
                pageDropdownItem: '<li role="menuitem" class="%s"><a href="#">%s</a></li>',
                dropdownCaret: '<span class="caret"></span>',
                pagination: ['<ul class="pagination%s">', "</ul>"],
                paginationItem: '<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',
                icon: '<i class="%s %s"></i>',
                inputGroup: '<div class="input-group">%s<span class="input-group-btn">%s</span></div>',
                searchInput: '<input class="%s%s" type="text" placeholder="%s">',
                searchButton: '<button class="%s" type="button" name="search" title="%s">%s %s</button>',
                searchClearButton: '<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'
            }
        }, 4: {
            iconsPrefix: "fa",
            icons: {
                paginationSwitchDown: "fa-caret-square-down",
                paginationSwitchUp: "fa-caret-square-up",
                refresh: "fa-sync",
                toggleOff: "fa-toggle-off",
                toggleOn: "fa-toggle-on",
                columns: "fa-th-list",
                detailOpen: "fa-plus",
                detailClose: "fa-minus",
                fullscreen: "fa-arrows-alt",
                search: "fa-search",
                clearSearch: "fa-trash"
            },
            classes: {
                buttonsPrefix: "btn",
                buttons: "secondary",
                buttonsGroup: "btn-group",
                buttonsDropdown: "btn-group",
                pull: "float",
                inputGroup: "btn-group",
                inputPrefix: "form-control-",
                input: "form-control",
                paginationDropdown: "btn-group dropdown",
                dropup: "dropup",
                dropdownActive: "active",
                paginationActive: "active",
                buttonActive: "active"
            },
            html: {
                toolbarDropdown: ['<div class="dropdown-menu dropdown-menu-right">', "</div>"],
                toolbarDropdownItem: '<label class="dropdown-item dropdown-item-marker">%s</label>',
                pageDropdown: ['<div class="dropdown-menu">', "</div>"],
                pageDropdownItem: '<a class="dropdown-item %s" href="#">%s</a>',
                toolbarDropdownSeparator: '<div class="dropdown-divider"></div>',
                dropdownCaret: '<span class="caret"></span>',
                pagination: ['<ul class="pagination%s">', "</ul>"],
                paginationItem: '<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',
                icon: '<i class="%s %s"></i>',
                inputGroup: '<div class="input-group">%s<div class="input-group-append">%s</div></div>',
                searchInput: '<input class="%s%s" type="text" placeholder="%s">',
                searchButton: '<button class="%s" type="button" name="search" title="%s">%s %s</button>',
                searchClearButton: '<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'
            }
        }, 5: {
            iconsPrefix: "fa",
            icons: {
                paginationSwitchDown: "fa-caret-square-down",
                paginationSwitchUp: "fa-caret-square-up",
                refresh: "fa-sync",
                toggleOff: "fa-toggle-off",
                toggleOn: "fa-toggle-on",
                columns: "fa-th-list",
                detailOpen: "fa-plus",
                detailClose: "fa-minus",
                fullscreen: "fa-arrows-alt",
                search: "fa-search",
                clearSearch: "fa-trash"
            },
            classes: {
                buttonsPrefix: "btn",
                buttons: "secondary",
                buttonsGroup: "btn-group",
                buttonsDropdown: "btn-group",
                pull: "float",
                inputGroup: "btn-group",
                inputPrefix: "form-control-",
                input: "form-control",
                paginationDropdown: "btn-group dropdown",
                dropup: "dropup",
                dropdownActive: "active",
                paginationActive: "active",
                buttonActive: "active"
            },
            html: {
                toolbarDropdown: ['<div class="dropdown-menu dropdown-menu-right">', "</div>"],
                toolbarDropdownItem: '<label class="dropdown-item dropdown-item-marker">%s</label>',
                pageDropdown: ['<div class="dropdown-menu">', "</div>"],
                pageDropdownItem: '<a class="dropdown-item %s" href="#">%s</a>',
                toolbarDropdownSeparator: '<div class="dropdown-divider"></div>',
                dropdownCaret: '<span class="caret"></span>',
                pagination: ['<ul class="pagination%s">', "</ul>"],
                paginationItem: '<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',
                icon: '<i class="%s %s"></i>',
                inputGroup: '<div class="input-group">%s<div class="input-group-append">%s</div></div>',
                searchInput: '<input class="%s%s" type="text" placeholder="%s">',
                searchButton: '<button class="%s" type="button" name="search" title="%s">%s %s</button>',
                searchClearButton: '<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'
            }
        }
    }[hh], lh = {
        id: void 0,
        firstLoad: !0,
        height: void 0,
        classes: "table table-bordered table-hover",
        theadClasses: "",
        headerStyle: function () {
            return {}
        },
        rowStyle: function () {
            return {}
        },
        rowAttributes: function () {
            return {}
        },
        undefinedText: "-",
        locale: void 0,
        virtualScroll: !1,
        virtualScrollItemHeight: void 0,
        sortable: !0,
        sortClass: void 0,
        silentSort: !0,
        sortName: void 0,
        sortOrder: void 0,
        sortReset: !1,
        sortStable: !1,
        rememberOrder: !1,
        serverSort: !0,
        customSort: void 0,
        columns: [[]],
        data: [],
        url: void 0,
        method: "get",
        cache: !0,
        contentType: "application/json",
        dataType: "json",
        ajax: void 0,
        ajaxOptions: {},
        queryParams: function (a) {
            return a
        },
        queryParamsType: "limit",
        responseHandler: function (a) {
            return a
        },
        totalField: "total",
        totalNotFilteredField: "totalNotFiltered",
        dataField: "rows",
        pagination: !1,
        paginationParts: ["pageInfo", "pageSize", "pageList"],
        showExtendedPagination: !1,
        paginationLoop: !0,
        sidePagination: "client",
        totalRows: 0,
        totalNotFiltered: 0,
        pageNumber: 1,
        pageSize: 10,
        pageList: [10, 25, 50, 100],
        paginationHAlign: "right",
        paginationVAlign: "bottom",
        paginationDetailHAlign: "left",
        paginationPreText: "&lsaquo;",
        paginationNextText: "&rsaquo;",
        paginationSuccessivelySize: 5,
        paginationPagesBySide: 1,
        paginationUseIntermediate: !1,
        search: !1,
        searchOnEnterKey: !1,
        strictSearch: !1,
        visibleSearch: !1,
        showButtonIcons: !0,
        showButtonText: !1,
        showSearchButton: !1,
        showSearchClearButton: !1,
        trimOnSearch: !0,
        searchAlign: "right",
        searchTimeOut: 500,
        searchText: "",
        customSearch: void 0,
        showHeader: !0,
        showFooter: !1,
        footerStyle: function () {
            return {}
        },
        searchAccentNeutralise: !1,
        showColumns: !1,
        showSearch: !1,
        showPageGo: !1,
        showColumnsToggleAll: !1,
        showColumnsSearch: !1,
        minimumCountColumns: 1,
        showPaginationSwitch: !1,
        showRefresh: !1,
        showToggle: !1,
        showFullscreen: !1,
        smartDisplay: !0,
        escape: !1,
        filterOptions: {filterAlgorithm: "and"},
        idField: void 0,
        selectItemName: "btSelectItem",
        clickToSelect: !1,
        ignoreClickToSelectOn: function (a) {
            var b = a.tagName;
            return ["A", "BUTTON"].includes(b)
        },
        singleSelect: !1,
        checkboxHeader: !0,
        maintainMetaData: !1,
        multipleSelectRow: !1,
        uniqueId: void 0,
        cardView: !1,
        detailView: !1,
        detailViewIcon: !0,
        detailViewByClick: !1,
        detailViewAlign: "left",
        detailFormatter: function () {
            return ""
        },
        detailFilter: function () {
            return !0
        },
        toolbar: void 0,
        toolbarAlign: "left",
        buttonsToolbar: void 0,
        buttonsAlign: "right",
        buttonsOrder: ["search", "paginationSwitch", "refresh", "toggle", "fullscreen", "columns"],
        buttonsPrefix: kh.classes.buttonsPrefix,
        buttonsClass: kh.classes.buttons,
        icons: kh.icons,
        iconSize: void 0,
        iconsPrefix: kh.iconsPrefix,
        loadingFontSize: "auto",
        loadingTemplate: function (a) {
            return '<span class="loading-wrap">\n      <span class="loading-text">'.concat(a, '</span>\n      <span class="animation-wrap"><span class="animation-dot"></span></span>\n      </span>\n    ')
        },
        onAll: function () {
            return !1
        },
        onClickCell: function () {
            return !1
        },
        onDblClickCell: function () {
            return !1
        },
        onClickRow: function () {
            return !1
        },
        onDblClickRow: function () {
            return !1
        },
        onSort: function () {
            return !1
        },
        onCheck: function () {
            return !1
        },
        onUncheck: function () {
            return !1
        },
        onCheckAll: function () {
            return !1
        },
        onUncheckAll: function () {
            return !1
        },
        onCheckSome: function () {
            return !1
        },
        onUncheckSome: function () {
            return !1
        },
        onLoadSuccess: function () {
            return !1
        },
        onLoadError: function () {
            return !1
        },
        onColumnSwitch: function () {
            return !1
        },
        onPageChange: function () {
            return !1
        },
        onSearch: function () {
            return !1
        },
        onShowSearch: function () {
            return !1
        },
        onToggle: function () {
            return !1
        },
        onPreBody: function () {
            return !1
        },
        onPostBody: function () {
            return !1
        },
        onPostHeader: function () {
            return !1
        },
        onPostFooter: function () {
            return !1
        },
        onExpandRow: function () {
            return !1
        },
        onCollapseRow: function () {
            return !1
        },
        onRefreshOptions: function () {
            return !1
        },
        onRefresh: function () {
            return !1
        },
        onResetView: function () {
            return !1
        },
        onScrollBody: function () {
            return !1
        }
    }, mh = {
        formatLoadingMessage: function () {
            return "Loading, please wait"
        }, formatRecordsPerPage: function (a) {
            return "".concat(a, " rows per page")
        }, formatShowingRows: function (a, b, c, d) {
            return void 0 !== d && d > 0 && d > c ? "Showing ".concat(a, " to ").concat(b, " of ").concat(c, " rows (filtered from ").concat(d, " total rows)") : "Showing ".concat(a, " to ").concat(b, " of ").concat(c, " rows")
        }, formatSRPaginationPreText: function () {
            return "previous page"
        }, formatSRPaginationPageText: function (a) {
            return "to page ".concat(a)
        }, formatSRPaginationNextText: function () {
            return "next page"
        }, formatDetailPagination: function (a) {
            return "Showing ".concat(a, " rows")
        }, formatSearch: function () {
            return "Search"
        }, formatPageGo: function () {
            return "跳转"
        }, formatClearSearch: function () {
            return "Clear Search"
        }, formatNoMatches: function () {
            return "No matching records found"
        }, formatPaginationSwitch: function () {
            return "Hide/Show pagination"
        }, formatPaginationSwitchDown: function () {
            return "Show pagination"
        }, formatPaginationSwitchUp: function () {
            return "Hide pagination"
        }, formatRefresh: function () {
            return "Refresh"
        }, formatToggle: function () {
            return "Toggle"
        }, formatToggleOn: function () {
            return "Show card view"
        }, formatToggleOff: function () {
            return "Hide card view"
        }, formatColumns: function () {
            return "Columns"
        }, formatColumnsToggleAll: function () {
            return "Toggle all"
        }, formatFullscreen: function () {
            return "Fullscreen"
        }, formatAllRows: function () {
            return "All"
        }
    }, nh = {
        field: void 0,
        title: void 0,
        titleTooltip: void 0,
        "class": void 0,
        width: void 0,
        widthUnit: "px",
        rowspan: void 0,
        colspan: void 0,
        align: void 0,
        halign: void 0,
        falign: void 0,
        valign: void 0,
        cellStyle: void 0,
        radio: !1,
        checkbox: !1,
        checkboxEnabled: !0,
        clickToSelect: !0,
        showSelectTitle: !1,
        sortable: !1,
        sortName: void 0,
        order: "asc",
        sorter: void 0,
        visible: !0,
        ignore: !1,
        switchable: !0,
        cardVisible: !0,
        searchable: !0,
        formatter: void 0,
        footerFormatter: void 0,
        detailFormatter: void 0,
        searchFormatter: !0,
        escape: !1,
        events: void 0
    }, oh = ["getOptions", "refreshOptions", "getData", "getSelections", "getAllSelections", "load", "append", "prepend", "remove", "removeAll", "insertRow", "updateRow", "getRowByUniqueId", "updateByUniqueId", "removeByUniqueId", "updateCell", "updateCellByUniqueId", "showRow", "hideRow", "getHiddenRows", "showColumn", "hideColumn", "getVisibleColumns", "getHiddenColumns", "showAllColumns", "hideAllColumns", "mergeCells", "checkAll", "uncheckAll", "checkInvert", "check", "uncheck", "checkBy", "uncheckBy", "refresh", "destroy", "resetView", "showLoading", "hideLoading", "togglePagination", "toggleFullscreen", "toggleView", "resetSearch", "filterBy", "scrollTo", "getScrollPosition", "selectPage", "prevPage", "nextPage", "toggleDetailView", "expandRow", "collapseRow", "expandRowByUniqueId", "collapseRowByUniqueId", "expandAllRows", "collapseAllRows", "updateColumnTitle", "updateFormatText"], ph = {
        "all.bs.table": "onAll",
        "click-row.bs.table": "onClickRow",
        "dbl-click-row.bs.table": "onDblClickRow",
        "click-cell.bs.table": "onClickCell",
        "dbl-click-cell.bs.table": "onDblClickCell",
        "sort.bs.table": "onSort",
        "check.bs.table": "onCheck",
        "uncheck.bs.table": "onUncheck",
        "check-all.bs.table": "onCheckAll",
        "uncheck-all.bs.table": "onUncheckAll",
        "check-some.bs.table": "onCheckSome",
        "uncheck-some.bs.table": "onUncheckSome",
        "load-success.bs.table": "onLoadSuccess",
        "load-error.bs.table": "onLoadError",
        "column-switch.bs.table": "onColumnSwitch",
        "page-change.bs.table": "onPageChange",
        "search.bs.table": "onSearch",
        "toggle.bs.table": "onToggle",
        "pre-body.bs.table": "onPreBody",
        "post-body.bs.table": "onPostBody",
        "post-header.bs.table": "onPostHeader",
        "post-footer.bs.table": "onPostFooter",
        "expand-row.bs.table": "onExpandRow",
        "collapse-row.bs.table": "onCollapseRow",
        "refresh-options.bs.table": "onRefreshOptions",
        "reset-view.bs.table": "onResetView",
        "refresh.bs.table": "onRefresh",
        "scroll-body.bs.table": "onScrollBody"
    }, Object.assign(lh, mh), qh = {
        VERSION: gh,
        THEME: "bootstrap".concat(hh),
        CONSTANTS: kh,
        DEFAULTS: lh,
        COLUMN_DEFAULTS: nh,
        METHODS: oh,
        EVENTS: ph,
        LOCALES: {en: mh, "en-US": mh}
    }, rh = s(function () {
        ec(1)
    }), _b({target: "Object", stat: !0, forced: rh}, {
        keys: function (a) {
            return ec(dc(a))
        }
    }), sh = Q.f, th = "".endsWith, uh = Math.min, vh = qg("endsWith"), wh = !vh && !!function () {
        var a = sh(String.prototype, "endsWith");
        return a && !a.writable
    }(), _b({target: "String", proto: !0, forced: !wh && !vh}, {
        endsWith: function (a) {
            var b, c, d, e, f = String(E(this));
            return og(a), b = arguments.length > 1 ? arguments[1] : void 0, c = Db(f.length), d = void 0 === b ? c : uh(Db(b), c), e = String(a), th ? th.call(f, e, d) : f.slice(d - e.length, d) === e
        }
    }), xh = Q.f, yh = "".startsWith, zh = Math.min, Ah = qg("startsWith"), Bh = !Ah && !!function () {
        var a = xh(String.prototype, "startsWith");
        return a && !a.writable
    }(), _b({target: "String", proto: !0, forced: !Bh && !Ah}, {
        startsWith: function (a) {
            var b, c, d = String(E(this));
            return og(a), b = Db(zh(arguments.length > 1 ? arguments[1] : void 0, d.length)), c = String(a), yh ? yh.call(d, c, b) : d.slice(b, b + c.length) === c
        }
    }), Ch = {
        sprintf: function (a) {
            var b, c, d, e, f, g;
            for (b = arguments.length, c = new Array(b > 1 ? b - 1 : 0), d = 1; b > d; d++) c[d - 1] = arguments[d];
            return e = !0, f = 0, g = a.replace(/%s/g, function () {
                var a = c[f++];
                return "undefined" == typeof a ? (e = !1, "") : a
            }), e ? g : ""
        }, isEmptyObject: function () {
            var a = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
            return 0 === Object.entries(a).length && a.constructor === Object
        }, isNumeric: function (a) {
            return !isNaN(parseFloat(a)) && isFinite(a)
        }, getFieldTitle: function (a, b) {
            var c, d, e, f = !0, g = !1, h = void 0;
            try {
                for (d = a[Symbol.iterator](); !(f = (c = d.next()).done); f = !0) if (e = c.value, e.field === b) return e.title
            } catch (i) {
                g = !0, h = i
            } finally {
                try {
                    f || null == d.return || d.return()
                } finally {
                    if (g) throw h
                }
            }
            return ""
        }, setFieldIndex: function (a) {
            var b, c, d, e, f, g, h, i, j, k, l, m, n, o, p, q, r, s = 0, t = [], u = !0, v = !1, w = void 0;
            try {
                for (c = a[0][Symbol.iterator](); !(u = (b = c.next()).done); u = !0) d = b.value, s += d.colspan || 1
            } catch (x) {
                v = !0, w = x
            } finally {
                try {
                    u || null == c.return || c.return()
                } finally {
                    if (v) throw w
                }
            }
            for (e = 0; e < a.length; e++) for (t[e] = [], f = 0; s > f; f++) t[e][f] = !1;
            for (g = 0; g < a.length; g++) {
                h = !0, i = !1, j = void 0;
                try {
                    for (l = a[g][Symbol.iterator](); !(h = (k = l.next()).done); h = !0) {
                        for (m = k.value, n = m.rowspan || 1, o = m.colspan || 1, p = t[g].indexOf(!1), m.colspanIndex = p, 1 === o ? (m.fieldIndex = p, "undefined" == typeof m.field && (m.field = p)) : m.colspanGroup = m.colspan, q = 0; n > q; q++) t[g + q][p] = !0;
                        for (r = 0; o > r; r++) t[g][p + r] = !0
                    }
                } catch (x) {
                    i = !0, j = x
                } finally {
                    try {
                        h || null == l.return || l.return()
                    } finally {
                        if (i) throw j
                    }
                }
            }
        }, normalizeAccent: function (a) {
            return a.normalize("NFD").replace(/[\u0300-\u036f]/g, "")
        }, updateFieldGroup: function (a) {
            var b, c, d, e, f, g, h, j, k, l, m, n, o, p = (b = []).concat.apply(b, i(a)), q = !0, r = !1, s = void 0;
            try {
                for (d = a[Symbol.iterator](); !(q = (c = d.next()).done); q = !0) {
                    e = c.value, f = !0, g = !1, h = void 0;
                    try {
                        for (k = e[Symbol.iterator](); !(f = (j = k.next()).done); f = !0) if (l = j.value, l.colspanGroup > 1) {
                            for (m = 0, n = function (a) {
                                var b = p.find(function (b) {
                                    return b.fieldIndex === a
                                });
                                b.visible && m++
                            }, o = l.colspanIndex; o < l.colspanIndex + l.colspanGroup; o++) n(o);
                            l.colspan = m, l.visible = m > 0
                        }
                    } catch (t) {
                        g = !0, h = t
                    } finally {
                        try {
                            f || null == k.return || k.return()
                        } finally {
                            if (g) throw h
                        }
                    }
                }
            } catch (t) {
                r = !0, s = t
            } finally {
                try {
                    q || null == d.return || d.return()
                } finally {
                    if (r) throw s
                }
            }
        }, getScrollBarWidth: function () {
            var b, c, d, e;
            return void 0 === this.cachedWidth && (b = a("<div/>").addClass("fixed-table-scroll-inner"), c = a("<div/>").addClass("fixed-table-scroll-outer"), c.append(b), a("body").append(c), d = b[0].offsetWidth, c.css("overflow", "scroll"), e = b[0].offsetWidth, d === e && (e = c[0].clientWidth), c.remove(), this.cachedWidth = d - e), this.cachedWidth
        }, calculateObjectValue: function (a, b, c, e) {
            var f, g, h, j, k, l, m, n = b;
            if ("string" == typeof b) if (f = b.split("."), f.length > 1) {
                n = window, g = !0, h = !1, j = void 0;
                try {
                    for (l = f[Symbol.iterator](); !(g = (k = l.next()).done); g = !0) m = k.value, n = n[m]
                } catch (o) {
                    h = !0, j = o
                } finally {
                    try {
                        g || null == l.return || l.return()
                    } finally {
                        if (h) throw j
                    }
                }
            } else n = window[b];
            return null !== n && "object" === d(n) ? n : "function" == typeof n ? n.apply(a, c || []) : !n && "string" == typeof b && this.sprintf.apply(this, [b].concat(i(c))) ? this.sprintf.apply(this, [b].concat(i(c))) : e
        }, compareObjects: function (a, b, c) {
            var d, e, f, g = Object.keys(a), h = Object.keys(b);
            if (c && g.length !== h.length) return !1;
            for (d = 0, e = g; d < e.length; d++) if (f = e[d], h.includes(f) && a[f] !== b[f]) return !1;
            return !0
        }, escapeHTML: function (a) {
            return "string" == typeof a ? a.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;").replace(/`/g, "&#x60;") : a
        }, unescapeHTML: function (a) {
            return "string" == typeof a ? a.replace(/&amp;/g, "&").replace(/&lt;/g, "<").replace(/&gt;/g, ">").replace(/&quot;/g, '"').replace(/&#039;/g, "'").replace(/&#x60;/g, "`") : a
        }, getRealDataAttr: function (a) {
            var b, c, d, e, f, g;
            for (b = 0, c = Object.entries(a); b < c.length; b++) d = h(c[b], 2), e = d[0], f = d[1], g = e.split(/(?=[A-Z])/).join("-").toLowerCase(), g !== e && (a[g] = f, delete a[e]);
            return a
        }, getItemField: function (a, b, c) {
            var d, e, f, g, h, i, j, k = a;
            if ("string" != typeof b || a.hasOwnProperty(b)) return c ? this.escapeHTML(a[b]) : a[b];
            d = b.split("."), e = !0, f = !1, g = void 0;
            try {
                for (i = d[Symbol.iterator](); !(e = (h = i.next()).done); e = !0) j = h.value, k = k && k[j]
            } catch (l) {
                f = !0, g = l
            } finally {
                try {
                    e || null == i.return || i.return()
                } finally {
                    if (f) throw g
                }
            }
            return c ? this.escapeHTML(k) : k
        }, isIEBrowser: function () {
            return navigator.userAgent.includes("MSIE ") || /Trident.*rv:11\./.test(navigator.userAgent)
        }, findIndex: function (a, b) {
            var c, d, e, f = !0, g = !1, h = void 0;
            try {
                for (d = a[Symbol.iterator](); !(f = (c = d.next()).done); f = !0) if (e = c.value, JSON.stringify(e) === JSON.stringify(b)) return a.indexOf(e)
            } catch (i) {
                g = !0, h = i
            } finally {
                try {
                    f || null == d.return || d.return()
                } finally {
                    if (g) throw h
                }
            }
            return -1
        }, trToData: function (b, c) {
            var d = this, e = [], f = [];
            return c.each(function (c, g) {
                var h = {};
                h._id = a(g).attr("id"), h._class = a(g).attr("class"), h._data = d.getRealDataAttr(a(g).data()), a(g).find(">td,>th").each(function (e, g) {
                    for (var i, j, k, l = +a(g).attr("colspan") || 1, m = +a(g).attr("rowspan") || 1, n = e; f[c] && f[c][n]; n++) ;
                    for (i = n; n + l > i; i++) for (j = c; c + m > j; j++) f[j] || (f[j] = []), f[j][i] = !0;
                    k = b[n].field, h[k] = a(g).html().trim(), h["_".concat(k, "_id")] = a(g).attr("id"), h["_".concat(k, "_class")] = a(g).attr("class"), h["_".concat(k, "_rowspan")] = a(g).attr("rowspan"), h["_".concat(k, "_colspan")] = a(g).attr("colspan"), h["_".concat(k, "_title")] = a(g).attr("title"), h["_".concat(k, "_data")] = d.getRealDataAttr(a(g).data())
                }), e.push(h)
            }), e
        }, sort: function (a, b, c, d, e, f) {
            return (void 0 === a || null === a) && (a = ""), (void 0 === b || null === b) && (b = ""), d && a === b && (a = e, b = f), this.isNumeric(a) && this.isNumeric(b) ? (a = parseFloat(a), b = parseFloat(b), b > a ? -1 * c : a > b ? c : 0) : a === b ? 0 : ("string" != typeof a && (a = a.toString()), -1 === a.localeCompare(b) ? -1 * c : c)
        }, getResizeEventName: function () {
            var a = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "";
            return a = a || "".concat(+new Date).concat(~~(1e6 * Math.random())), "resize.bootstrap-table-".concat(a)
        }, hasDetailViewIcon: function (a) {
            return a.detailView && a.detailViewIcon && !a.cardView
        }, checkAutoMergeCells: function (a) {
            var b, c, d, e, f, g, h = !0, i = !1, j = void 0;
            try {
                for (c = a[Symbol.iterator](); !(h = (b = c.next()).done); h = !0) for (d = b.value, e = 0, f = Object.keys(d); e < f.length; e++) if (g = f[e], g.startsWith("_") && (g.endsWith("_rowspan") || g.endsWith("_colspan"))) return !0
            } catch (k) {
                i = !0, j = k
            } finally {
                try {
                    h || null == c.return || c.return()
                } finally {
                    if (i) throw j
                }
            }
            return !1
        }
    }, Dh = 50, Eh = 4, Fh = function () {
        function a(b) {
            var c, d = this;
            e(this, a), this.rows = b.rows, this.scrollEl = b.scrollEl, this.contentEl = b.contentEl, this.callback = b.callback, this.itemHeight = b.itemHeight, this.cache = {}, this.scrollTop = this.scrollEl.scrollTop, this.initDOM(this.rows, b.fixedScroll), this.scrollEl.scrollTop = this.scrollTop, this.lastCluster = 0, c = function () {
                d.lastCluster !== (d.lastCluster = d.getNum()) && (d.initDOM(d.rows), d.callback())
            }, this.scrollEl.addEventListener("scroll", c, !1), this.destroy = function () {
                d.contentEl.innerHtml = "", d.scrollEl.removeEventListener("scroll", c, !1)
            }
        }

        return g(a, [{
            key: "initDOM", value: function (a, b) {
                var c, d, e, f, g, h;
                "undefined" == typeof this.clusterHeight && (this.cache.scrollTop = this.scrollEl.scrollTop, this.cache.data = this.contentEl.innerHTML = a[0] + a[0] + a[0], this.getRowsHeight(a)), c = this.initData(a, this.getNum(b)), d = c.rows.join(""), e = this.checkChanges("data", d), f = this.checkChanges("top", c.topOffset), g = this.checkChanges("bottom", c.bottomOffset), h = [], e && f ? (c.topOffset && h.push(this.getExtra("top", c.topOffset)), h.push(d), c.bottomOffset && h.push(this.getExtra("bottom", c.bottomOffset)), this.contentEl.innerHTML = h.join(""), b && (this.contentEl.scrollTop = this.cache.scrollTop)) : g && (this.contentEl.lastChild.style.height = "".concat(c.bottomOffset, "px"))
            }
        }, {
            key: "getRowsHeight", value: function () {
                var a, b;
                "undefined" == typeof this.itemHeight && (a = this.contentEl.children, b = a[Math.floor(a.length / 2)], this.itemHeight = b.offsetHeight), this.blockHeight = this.itemHeight * Dh, this.clusterRows = Dh * Eh, this.clusterHeight = this.blockHeight * Eh
            }
        }, {
            key: "getNum", value: function (a) {
                return this.scrollTop = a ? this.cache.scrollTop : this.scrollEl.scrollTop, Math.floor(this.scrollTop / (this.clusterHeight - this.blockHeight)) || 0
            }
        }, {
            key: "initData", value: function (a, b) {
                var c, d, e, f, g, h, i;
                if (a.length < Dh) return {topOffset: 0, bottomOffset: 0, rowsAbove: 0, rows: a};
                for (c = Math.max((this.clusterRows - Dh) * b, 0), d = c + this.clusterRows, e = Math.max(c * this.itemHeight, 0), f = Math.max((a.length - d) * this.itemHeight, 0), g = [], h = c, 1 > e && h++, i = c; d > i; i++) a[i] && g.push(a[i]);
                return {topOffset: e, bottomOffset: f, rowsAbove: h, rows: g}
            }
        }, {
            key: "checkChanges", value: function (a, b) {
                var c = b !== this.cache[a];
                return this.cache[a] = b, c
            }
        }, {
            key: "getExtra", value: function (a, b) {
                var c = document.createElement("tr");
                return c.className = "virtual-scroll-".concat(a), b && (c.style.height = "".concat(b, "px")), c.outerHTML
            }
        }]), a
    }(), Gh = function () {
        function b(c, d) {
            e(this, b), this.options = d, this.$el = a(c), this.$el_ = this.$el.clone(), this.timeoutId_ = 0, this.timeoutFooter_ = 0, this.init()
        }

        return g(b, [{
            key: "init", value: function () {
                this.initConstants(), this.initLocale(), this.initContainer(), this.initTable(), this.initHeader(), this.initData(), this.initHiddenRows(), this.initToolbar(), this.initPagination(), this.initBody(), this.initSearchText(), this.initServer()
            }
        }, {
            key: "initConstants", value: function () {
                var b, c = this.options;
                this.constants = qh.CONSTANTS, this.constants.theme = a.fn.bootstrapTable.theme, b = c.buttonsPrefix ? "".concat(c.buttonsPrefix, "-") : "", this.constants.buttonsClass = [c.buttonsPrefix, b + c.buttonsClass, Ch.sprintf("".concat(b, "%s"), c.iconSize)].join(" ").trim()
            }
        }, {
            key: "initLocale", value: function () {
                var b, c;
                this.options.locale && (b = a.fn.bootstrapTable.locales, c = this.options.locale.split(/-|_/), c[0] = c[0].toLowerCase(), c[1] && (c[1] = c[1].toUpperCase()), b[this.options.locale] ? a.extend(this.options, b[this.options.locale]) : b[c.join("-")] ? a.extend(this.options, b[c.join("-")]) : b[c[0]] && a.extend(this.options, b[c[0]]))
            }
        }, {
            key: "initContainer", value: function () {
                var b = ["top", "both"].includes(this.options.paginationVAlign) ? '<div class="fixed-table-pagination clearfix"></div>' : "",
                    c = ["bottom", "both"].includes(this.options.paginationVAlign) ? '<div class="fixed-table-pagination"></div>' : "",
                    d = Ch.calculateObjectValue(this.options, this.options.loadingTemplate, [this.options.formatLoadingMessage()]);
                this.$container = a('\n      <div class="bootstrap-table '.concat(this.constants.theme, '">\n      <div class="fixed-table-toolbar"></div>\n      ').concat(b, '\n      <div class="fixed-table-container">\n      <div class="fixed-table-header"><table></table></div>\n      <div class="fixed-table-body">\n      <div class="fixed-table-loading">\n      ').concat(d, '\n      </div>\n      </div>\n      <div class="fixed-table-footer"><table><thead><tr></tr></thead></table></div>\n      </div>\n      ').concat(c, "\n      </div>\n    ")), this.$container.insertAfter(this.$el), this.$tableContainer = this.$container.find(".fixed-table-container"), this.$tableHeader = this.$container.find(".fixed-table-header"), this.$tableBody = this.$container.find(".fixed-table-body"), this.$tableLoading = this.$container.find(".fixed-table-loading"), this.$tableFooter = this.$el.find("tfoot"), this.$toolbar = this.options.buttonsToolbar ? a("body").find(this.options.buttonsToolbar) : this.$container.find(".fixed-table-toolbar"), this.$pagination = this.$container.find(".fixed-table-pagination"), this.$tableBody.append(this.$el), this.$container.after('<div class="clearfix"></div>'), this.$el.addClass(this.options.classes), this.$tableLoading.addClass(this.options.classes), this.options.height && (this.$tableContainer.addClass("fixed-height"), this.options.showFooter && this.$tableContainer.addClass("has-footer"), this.options.classes.split(" ").includes("table-bordered") && (this.$tableBody.append('<div class="fixed-table-border"></div>'), this.$tableBorder = this.$tableBody.find(".fixed-table-border"), this.$tableLoading.addClass("fixed-table-border")), this.$tableFooter = this.$container.find(".fixed-table-footer"))
            }
        }, {
            key: "initTable", value: function () {
                var c, d = this, e = [];
                this.$header = this.$el.find(">thead"), this.$header.length ? this.options.theadClasses && this.$header.addClass(this.options.theadClasses) : this.$header = a('<thead class="'.concat(this.options.theadClasses, '"></thead>')).appendTo(this.$el), this._headerTrClasses = [], this.$header.find("tr").each(function (b, c) {
                    var f = a(c), g = [];
                    f.find("th").each(function (b, c) {
                        var d = a(c);
                        "undefined" != typeof d.data("field") && d.data("field", "".concat(d.data("field"))), g.push(a.extend({}, {
                            title: d.html(),
                            "class": d.attr("class"),
                            titleTooltip: d.attr("title"),
                            rowspan: d.attr("rowspan") ? +d.attr("rowspan") : void 0,
                            colspan: d.attr("colspan") ? +d.attr("colspan") : void 0
                        }, d.data()))
                    }), e.push(g), f.attr("class") && d._headerTrClasses.push(f.attr("class"))
                }), Array.isArray(this.options.columns[0]) || (this.options.columns = [this.options.columns]), this.options.columns = a.extend(!0, [], e, this.options.columns), this.columns = [], this.fieldsColumnsIndex = [], Ch.setFieldIndex(this.options.columns), this.options.columns.forEach(function (c, e) {
                    c.forEach(function (c, f) {
                        var g = a.extend({}, b.COLUMN_DEFAULTS, c);
                        "undefined" != typeof g.fieldIndex && (d.columns[g.fieldIndex] = g, d.fieldsColumnsIndex[g.field] = g.fieldIndex), d.options.columns[e][f] = g
                    })
                }), this.options.data.length || (c = Ch.trToData(this.columns, this.$el.find(">tbody>tr")), c.length && (this.options.data = c, this.fromHtml = !0)), this.footerData = Ch.trToData(this.columns, this.$el.find(">tfoot>tr")), this.footerData && this.$el.find("tfoot").html("<tr></tr>"), !this.options.showFooter || this.options.cardView ? this.$tableFooter.hide() : this.$tableFooter.show()
            }
        }, {
            key: "initHeader", value: function () {
                var b, c = this, d = {}, e = [];
                this.header = {
                    fields: [],
                    styles: [],
                    classes: [],
                    formatters: [],
                    detailFormatters: [],
                    events: [],
                    sorters: [],
                    sortNames: [],
                    cellStyles: [],
                    searchables: []
                }, Ch.updateFieldGroup(this.options.columns), this.options.columns.forEach(function (a, b) {
                    e.push("<tr".concat(Ch.sprintf(' class="%s"', c._headerTrClasses[b]), ">"));
                    var f = "";
                    0 === b && Ch.hasDetailViewIcon(c.options) && (f = '<th class="detail" rowspan="'.concat(c.options.columns.length, '">\n          <div class="fht-cell"></div>\n          </th>')), f && "right" !== c.options.detailViewAlign && e.push(f), a.forEach(function (a, f) {
                        var g, i, j, k, l, m, n, o, p, q, r = Ch.sprintf(' class="%s"', a["class"]), s = a.widthUnit,
                            t = parseFloat(a.width), u = Ch.sprintf("text-align: %s; ", a.halign ? a.halign : a.align),
                            v = Ch.sprintf("text-align: %s; ", a.align),
                            w = Ch.sprintf("vertical-align: %s; ", a.valign);
                        if (w += Ch.sprintf("width: %s; ", !a.checkbox && !a.radio || t ? t ? t + s : void 0 : a.showSelectTitle ? void 0 : "36px"), "undefined" != typeof a.fieldIndex || a.visible) {
                            if (g = Ch.calculateObjectValue(null, c.options.headerStyle, [a]), i = [], j = "", g && g.css) for (k = 0, l = Object.entries(g.css); k < l.length; k++) m = h(l[k], 2), n = m[0], o = m[1], i.push("".concat(n, ": ").concat(o));
                            if (g && g.classes && (j = Ch.sprintf(' class="%s"', a["class"] ? [a["class"], g.classes].join(" ") : g.classes)), "undefined" != typeof a.fieldIndex) {
                                if (c.header.fields[a.fieldIndex] = a.field, c.header.styles[a.fieldIndex] = v + w, c.header.classes[a.fieldIndex] = r, c.header.formatters[a.fieldIndex] = a.formatter, c.header.detailFormatters[a.fieldIndex] = a.detailFormatter, c.header.events[a.fieldIndex] = a.events, c.header.sorters[a.fieldIndex] = a.sorter, c.header.sortNames[a.fieldIndex] = a.sortName, c.header.cellStyles[a.fieldIndex] = a.cellStyle, c.header.searchables[a.fieldIndex] = a.searchable, !a.visible) return;
                                if (c.options.cardView && !a.cardVisible) return;
                                d[a.field] = a
                            }
                            e.push("<th".concat(Ch.sprintf(' title="%s"', a.titleTooltip)), a.checkbox || a.radio ? Ch.sprintf(' class="bs-checkbox %s"', a["class"] || "") : j || r, Ch.sprintf(' style="%s"', u + w + i.join("; ")), Ch.sprintf(' rowspan="%s"', a.rowspan), Ch.sprintf(' colspan="%s"', a.colspan), Ch.sprintf(' data-field="%s"', a.field), 0 === f && b > 0 ? " data-not-first-th" : "", ">"), e.push(Ch.sprintf('<div class="th-inner %s">', c.options.sortable && a.sortable ? "sortable both" : "")), p = c.options.escape ? Ch.escapeHTML(a.title) : a.title, q = p, a.checkbox && (p = "", !c.options.singleSelect && c.options.checkboxHeader && (p = '<label><input name="btSelectAll" type="checkbox" /><span></span></label>'), c.header.stateField = a.field), a.radio && (p = "", c.header.stateField = a.field), !p && a.showSelectTitle && (p += q), e.push(p), e.push("</div>"), e.push('<div class="fht-cell"></div>'), e.push("</div>"), e.push("</th>")
                        }
                    }), f && "right" === c.options.detailViewAlign && e.push(f), e.push("</tr>")
                }), this.$header.html(e.join("")), this.$header.find("th[data-field]").each(function (b, c) {
                    a(c).data(d[a(c).data("field")])
                }), this.$container.off("click", ".th-inner").on("click", ".th-inner", function (b) {
                    var d = a(b.currentTarget);
                    return c.options.detailView && !d.parent().hasClass("bs-checkbox") && d.closest(".bootstrap-table")[0] !== c.$container[0] ? !1 : (c.options.sortable && d.parent().data().sortable && c.onSort(b), void 0)
                }), this.$header.children().children().off("keypress").on("keypress", function (b) {
                    if (c.options.sortable && a(b.currentTarget).data().sortable) {
                        var d = b.keyCode || b.which;
                        13 === d && c.onSort(b)
                    }
                }), b = Ch.getResizeEventName(this.$el.attr("id")), a(window).off(b), !this.options.showHeader || this.options.cardView ? (this.$header.hide(), this.$tableHeader.hide(), this.$tableLoading.css("top", 0)) : (this.$header.show(), this.$tableHeader.show(), this.$tableLoading.css("top", this.$header.outerHeight() + 1), this.getCaret(), a(window).on(b, function () {
                    return c.resetView()
                })), this.$selectAll = this.$header.find('[name="btSelectAll"]'), this.$selectAll.off("click").on("click", function (b) {
                    b.stopPropagation();
                    var d = a(b.currentTarget).prop("checked");
                    c[d ? "checkAll" : "uncheckAll"](), c.updateSelected()
                })
            }
        }, {
            key: "initData", value: function (a, b) {
                "append" === b ? this.options.data = this.options.data.concat(a) : "prepend" === b ? this.options.data = [].concat(a).concat(this.options.data) : (a = a || this.options.data, this.options.data = Array.isArray(a) ? a : a[this.options.dataField]), this.data = i(this.options.data), this.options.sortReset && (this.unsortedData = i(this.data)), "server" !== this.options.sidePagination && this.initSort()
            }
        }, {
            key: "initSort", value: function () {
                var a = this, b = this.options.sortName, c = "desc" === this.options.sortOrder ? -1 : 1,
                    d = this.header.fields.indexOf(this.options.sortName), e = 0;
                -1 !== d ? (this.options.sortStable && this.data.forEach(function (a, b) {
                    a.hasOwnProperty("_position") || (a._position = b)
                }), this.options.customSort ? Ch.calculateObjectValue(this.options, this.options.customSort, [this.options.sortName, this.options.sortOrder, this.data]) : this.data.sort(function (e, f) {
                    var g, h, i;
                    return a.header.sortNames[d] && (b = a.header.sortNames[d]), g = Ch.getItemField(e, b, a.options.escape), h = Ch.getItemField(f, b, a.options.escape), i = Ch.calculateObjectValue(a.header, a.header.sorters[d], [g, h, e, f]), void 0 !== i ? a.options.sortStable && 0 === i ? c * (e._position - f._position) : c * i : Ch.sort(g, h, c, a.options.sortStable, e._position, f._position)
                }), void 0 !== this.options.sortClass && (clearTimeout(e), e = setTimeout(function () {
                    a.$el.removeClass(a.options.sortClass);
                    var b = a.$header.find('[data-field="'.concat(a.options.sortName, '"]')).index();
                    a.$el.find("tr td:nth-child(".concat(b + 1, ")")).addClass(a.options.sortClass)
                }, 250))) : this.options.sortReset && (this.data = i(this.unsortedData))
            }
        }, {
            key: "onSort", value: function (b) {
                var c, d = b.type, e = b.currentTarget, f = "keypress" === d ? a(e) : a(e).parent(),
                    g = this.$header.find("th").eq(f.index());
                return this.$header.add(this.$header_).find("span.order").remove(), this.options.sortName === f.data("field") ? (c = this.options.sortOrder, void 0 === c ? this.options.sortOrder = "asc" : "asc" === c ? this.options.sortOrder = "desc" : "desc" === this.options.sortOrder && (this.options.sortOrder = this.options.sortReset ? void 0 : "asc"), void 0 === this.options.sortOrder && (this.options.sortName = void 0)) : (this.options.sortName = f.data("field"), this.options.sortOrder = this.options.rememberOrder ? "asc" === f.data("order") ? "desc" : "asc" : this.columns[this.fieldsColumnsIndex[f.data("field")]].sortOrder || this.columns[this.fieldsColumnsIndex[f.data("field")]].order), this.trigger("sort", this.options.sortName, this.options.sortOrder), f.add(g).data("order", this.options.sortOrder), this.getCaret(), "server" === this.options.sidePagination && this.options.serverSort ? (this.options.pageNumber = 1, this.initServer(this.options.silentSort), void 0) : (this.initSort(), this.initBody(), void 0)
            }
        }, {
            key: "initToolbar", value: function () {
                var b, c, e, f, g, h, i, j, k, l, m, n, o, p, q, r, s, t, u, v = this, w = this.options, x = [], y = 0,
                    z = 0;
                this.$toolbar.find(".bs-bars").children().length && a("body").append(a(w.toolbar)), this.$toolbar.html(""), ("string" == typeof w.toolbar || "object" === d(w.toolbar)) && a(Ch.sprintf('<div class="bs-bars %s-%s"></div>', this.constants.classes.pull, w.toolbarAlign)).appendTo(this.$toolbar).append(a(w.toolbar)), x = ['<div class="'.concat(["columns", "columns-".concat(w.buttonsAlign), this.constants.classes.buttonsGroup, "".concat(this.constants.classes.pull, "-").concat(w.buttonsAlign)].join(" "), '">')], "string" == typeof w.icons && (w.icons = Ch.calculateObjectValue(null, w.icons)), c = {
                    search: '<button class="'.concat(this.constants.buttonsClass, '" type="button" name="showSearch"\n        aria-label="show Search" title="').concat(w.formatSearch(), '">\n        ').concat(w.showButtonIcons ? Ch.sprintf(this.constants.html.icon, w.iconsPrefix, w.icons.search) : "", "\n        ").concat(w.showButtonText ? w.formatSearch() : "", "\n        </button>"),
                    paginationSwitch: '<button class="'.concat(this.constants.buttonsClass, '" type="button" name="paginationSwitch"\n        aria-label="Pagination Switch" title="').concat(w.formatPaginationSwitch(), '">\n        ').concat(w.showButtonIcons ? Ch.sprintf(this.constants.html.icon, w.iconsPrefix, w.icons.paginationSwitchDown) : "", "\n        ").concat(w.showButtonText ? w.formatPaginationSwitchUp() : "", "\n        </button>"),
                    refresh: '<button class="'.concat(this.constants.buttonsClass, '" type="button" name="refresh"\n        aria-label="Refresh" title="').concat(w.formatRefresh(), '">\n        ').concat(w.showButtonIcons ? Ch.sprintf(this.constants.html.icon, w.iconsPrefix, w.icons.refresh) : "", "\n        ").concat(w.showButtonText ? w.formatRefresh() : "", "\n        </button>"),
                    toggle: '<button class="'.concat(this.constants.buttonsClass, '" type="button" name="toggle"\n        aria-label="Toggle" title="').concat(w.formatToggle(), '">\n        ').concat(w.showButtonIcons ? Ch.sprintf(this.constants.html.icon, w.iconsPrefix, w.icons.toggleOff) : "", "\n        ").concat(w.showButtonText ? w.formatToggleOn() : "", "\n        </button>"),
                    fullscreen: '<button class="'.concat(this.constants.buttonsClass, '" type="button" name="fullscreen"\n        aria-label="Fullscreen" title="').concat(w.formatFullscreen(), '">\n        ').concat(w.showButtonIcons ? Ch.sprintf(this.constants.html.icon, w.iconsPrefix, w.icons.fullscreen) : "", "\n        ").concat(w.showButtonText ? w.formatFullscreen() : "", "\n        </button>"),
                    columns: function () {
                        var a, b, c = [];
                        return c.push('<div class="keep-open '.concat(v.constants.classes.buttonsDropdown, '" title="').concat(w.formatColumns(), '">\n          <button class="').concat(v.constants.buttonsClass, ' dropdown-toggle" type="button" data-toggle="dropdown"\n          aria-label="Columns" title="').concat(w.formatColumns(), '">\n          ').concat(w.showButtonIcons ? Ch.sprintf(v.constants.html.icon, w.iconsPrefix, w.icons.columns) : "", "\n          ").concat(w.showButtonText ? w.formatColumns() : "", "\n          ").concat(v.constants.html.dropdownCaret, "\n          </button>\n          ").concat(v.constants.html.toolbarDropdown[0])), w.showColumnsSearch && (c.push(Ch.sprintf(v.constants.html.toolbarDropdownItem, Ch.sprintf('<input type="text" class="%s" name="columnsSearch" placeholder="%s" autocomplete="off">', v.constants.classes.input, w.formatSearch()))), c.push(v.constants.html.toolbarDropdownSeparator)), w.showColumnsToggleAll && (a = v.getVisibleColumns().length === v.columns.filter(function (a) {
                            return !v.isSelectionColumn(a)
                        }).length, c.push(Ch.sprintf(v.constants.html.toolbarDropdownItem, Ch.sprintf('<input type="checkbox" class="toggle-all" %s> <span>%s</span>', a ? 'checked="checked"' : "", w.formatColumnsToggleAll()))), c.push(v.constants.html.toolbarDropdownSeparator)), b = 0, v.columns.forEach(function (a) {
                            a.visible && b++
                        }), v.columns.forEach(function (a, d) {
                            var e, f;
                            v.isSelectionColumn(a) || (!w.cardView || a.cardVisible) && (a.ignore || (e = a.visible ? ' checked="checked"' : "", f = b <= v.options.minimumCountColumns && e ? ' disabled="disabled"' : "", a.switchable && (c.push(Ch.sprintf(v.constants.html.toolbarDropdownItem, Ch.sprintf('<input type="checkbox" data-field="%s" value="%s"%s%s> <span>%s</span>', a.field, d, e, f, a.title))), z++)))
                        }), c.push(v.constants.html.toolbarDropdown[1], "</div>"), c.join("")
                    }()
                }, "string" == typeof w.buttonsOrder && (w.buttonsOrder = w.buttonsOrder.replace(/\[|\]| |'/g, "").toLowerCase().split(",")), e = !0, f = !1, g = void 0;
                try {
                    for (i = w.buttonsOrder[Symbol.iterator](); !(e = (h = i.next()).done); e = !0) j = h.value, w["show" + j.charAt(0).toUpperCase() + j.substring(1)] && x.push(c[j])
                } catch (A) {
                    f = !0, g = A
                } finally {
                    try {
                        e || null == i.return || i.return()
                    } finally {
                        if (f) throw g
                    }
                }
                x.push("</div>"), (this.showToolbar || x.length > 2) && this.$toolbar.append(x.join("")), w.showSearch && this.$toolbar.find('button[name="showSearch"]').off("click").on("click", function () {
                    return v.toggleShowSearch()
                }), w.showPaginationSwitch && this.$toolbar.find('button[name="paginationSwitch"]').off("click").on("click", function () {
                    return v.togglePagination()
                }), w.showFullscreen && this.$toolbar.find('button[name="fullscreen"]').off("click").on("click", function () {
                    return v.toggleFullscreen()
                }), w.showRefresh && this.$toolbar.find('button[name="refresh"]').off("click").on("click", function () {
                    return v.refresh()
                }), w.showToggle && this.$toolbar.find('button[name="toggle"]').off("click").on("click", function () {
                    v.toggleView()
                }), w.showColumns && (b = this.$toolbar.find(".keep-open"), k = b.find('input[type="checkbox"]:not(".toggle-all")'), l = b.find('input[type="checkbox"].toggle-all'), z <= w.minimumCountColumns && b.find("input").prop("disabled", !0), b.find("li, label").off("click").on("click", function (a) {
                    a.stopImmediatePropagation()
                }), k.off("click").on("click", function (b) {
                    var c = b.currentTarget, d = a(c);
                    v._toggleColumn(d.val(), d.prop("checked"), !1), v.trigger("column-switch", d.data("field"), d.prop("checked")), l.prop("checked", k.filter(":checked").length === v.columns.filter(function (a) {
                        return !v.isSelectionColumn(a)
                    }).length)
                }), l.off("click").on("click", function (b) {
                    var c = b.currentTarget;
                    v._toggleAllColumns(a(c).prop("checked"))
                }), w.showColumnsSearch && (m = b.find('[name="columnsSearch"]'), n = b.find(".dropdown-item-marker"), m.on("keyup paste change", function (b) {
                    var c = b.currentTarget, d = a(c), e = d.val().toLowerCase();
                    n.show(), k.each(function (b, c) {
                        var d = a(c), f = d.parents(".dropdown-item-marker"), g = f.text().toLowerCase();
                        g.includes(e) || f.hide()
                    })
                }))), (w.search || this.showSearchClearButton) && (x = [], o = Ch.sprintf(this.constants.html.searchButton, this.constants.buttonsClass, w.formatSearch(), w.showButtonIcons ? Ch.sprintf(this.constants.html.icon, w.iconsPrefix, w.icons.search) : "", w.showButtonText ? w.formatSearch() : ""), p = Ch.sprintf(this.constants.html.searchClearButton, this.constants.buttonsClass, w.formatClearSearch(), w.showButtonIcons ? Ch.sprintf(this.constants.html.icon, w.iconsPrefix, w.icons.clearSearch) : "", w.showButtonText ? w.formatClearSearch() : ""), q = '<input class="'.concat(this.constants.classes.input, "\n        ").concat(Ch.sprintf(" %s%s", this.constants.classes.inputPrefix, w.iconSize), '\n        search-input" type="text" placeholder="').concat(w.formatSearch(), '" autocomplete="off">'), r = q, (w.showSearchButton || w.showSearchClearButton) && (s = (w.showSearchButton ? o : "") + (w.showSearchClearButton ? p : ""), r = w.search ? Ch.sprintf(this.constants.html.inputGroup, q, s) : s), x.push(Ch.sprintf('\n        <div class="'.concat(this.constants.classes.pull, "-").concat(w.searchAlign, " search ").concat(this.constants.classes.inputGroup, '">\n          %s\n        </div>\n      '), r)), this.$toolbar.append(x.join("")), t = this.$toolbar.find(".search input"), u = function () {
                    var a = "keyup drop blur ".concat(Ch.isIEBrowser() ? "mouseup" : "");
                    t.off(a).on(a, function (a) {
                        w.searchOnEnterKey && 13 !== a.keyCode || [37, 38, 39, 40].includes(a.keyCode) || (clearTimeout(y), y = setTimeout(function () {
                            v.onSearch({currentTarget: a.currentTarget})
                        }, w.searchTimeOut))
                    })
                }, w.showSearchButton ? (this.$toolbar.find(".search button[name=search]").off("click").on("click", function () {
                    clearTimeout(y), y = setTimeout(function () {
                        v.onSearch({currentTarget: t})
                    }, w.searchTimeOut)
                }), w.searchOnEnterKey && u()) : u(), w.showSearchClearButton && this.$toolbar.find(".search button[name=clearSearch]").click(function () {
                    v.resetSearch()
                }))
            }
        }, {
            key: "onSearch", value: function () {
                var b, c = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, d = c.currentTarget,
                    e = c.firedByInitSearchText,
                    f = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : !0;
                if (void 0 !== d && a(d).length && f) {
                    if (b = a(d).val().trim(), this.options.trimOnSearch && a(d).val() !== b && a(d).val(b), this.searchText === b && b.length > 0) return;
                    a(d).hasClass("search-input") && (this.searchText = b, this.options.searchText = b)
                }
                e || (this.options.pageNumber = 1), this.initSearch(), e ? "client" === this.options.sidePagination && this.updatePagination() : this.updatePagination(), this.trigger("search", this.searchText)
            }
        }, {
            key: "initSearch", value: function () {
                var a, b, c, d = this;
                if (this.filterOptions = this.filterOptions || this.options.filterOptions, "server" !== this.options.sidePagination) {
                    if (this.options.customSearch) return this.data = Ch.calculateObjectValue(this.options, this.options.customSearch, [this.options.data, this.searchText, this.filterColumns]), void 0;
                    a = this.searchText && (this.fromHtml ? Ch.escapeHTML(this.searchText) : this.searchText).toLowerCase(), b = Ch.isEmptyObject(this.filterColumns) ? null : this.filterColumns, "function" == typeof this.filterOptions.filterAlgorithm ? this.data = this.options.data.filter(function (a) {
                        return d.filterOptions.filterAlgorithm.apply(null, [a, b])
                    }) : "string" == typeof this.filterOptions.filterAlgorithm && (this.data = b ? this.options.data.filter(function (a) {
                        var c, e, f, g = d.filterOptions.filterAlgorithm;
                        if ("and" === g) {
                            for (c in b) if (Array.isArray(b[c]) && !b[c].includes(a[c]) || !Array.isArray(b[c]) && a[c] !== b[c]) return !1
                        } else if ("or" === g) {
                            e = !1;
                            for (f in b) (Array.isArray(b[f]) && b[f].includes(a[f]) || !Array.isArray(b[f]) && a[f] === b[f]) && (e = !0);
                            return e
                        }
                        return !0
                    }) : i(this.options.data)), c = this.getVisibleFields(), this.data = a ? this.data.filter(function (b, e) {
                        var f, g, h, i, j, k, l, m, n, o, p, q, r;
                        for (f = 0; f < d.header.fields.length; f++) if (d.header.searchables[f] && (!d.options.visibleSearch || -1 !== c.indexOf(d.header.fields[f]))) {
                            if (g = Ch.isNumeric(d.header.fields[f]) ? parseInt(d.header.fields[f], 10) : d.header.fields[f], h = d.columns[d.fieldsColumnsIndex[g]], i = void 0, "string" == typeof g) for (i = b, j = g.split("."), k = 0; k < j.length; k++) null !== i[j[k]] && (i = i[j[k]]); else i = b[g];
                            if (d.options.searchAccentNeutralise && (i = Ch.normalizeAccent(i)), h && h.searchFormatter && (i = Ch.calculateObjectValue(h, d.header.formatters[f], [i, b, e, h.field], i)), "string" == typeof i || "number" == typeof i) if (d.options.strictSearch) {
                                if ("".concat(i).toLowerCase() === a) return !0
                            } else {
                                if (l = /(?:(<=|=>|=<|>=|>|<)(?:\s+)?(\d+)?|(\d+)?(\s+)?(<=|=>|=<|>=|>|<))/gm, m = l.exec(a), n = !1, m) switch (o = m[1] || "".concat(m[5], "l"), p = m[2] || m[3], q = parseInt(i, 10), r = parseInt(p, 10), o) {
                                    case">":
                                    case"<l":
                                        n = q > r;
                                        break;
                                    case"<":
                                    case">l":
                                        n = r > q;
                                        break;
                                    case"<=":
                                    case"=<":
                                    case">=l":
                                    case"=>l":
                                        n = r >= q;
                                        break;
                                    case">=":
                                    case"=>":
                                    case"<=l":
                                    case"=<l":
                                        n = q >= r
                                }
                                if (n || "".concat(i).toLowerCase().includes(a)) return !0
                            }
                        }
                        return !1
                    }) : this.data, this.options.sortReset && (this.unsortedData = i(this.data)), this.initSort()
                }
            }
        }, {
            key: "initPagination", value: function () {
                var b, c, d, e, f, g, h, i, j, k, l, m, n, o, p, q, r, s, t, u, v, w = this, x = this.options;
                if (!x.pagination) return this.$pagination.hide(), void 0;
                if (this.$pagination.show(), b = [], c = !1, k = this.getData({includeHiddenRows: !1}), l = x.pageList, "string" == typeof l && (l = l.replace(/\[|\]| /g, "").toLowerCase().split(",")), l = l.map(function (a) {
                    return "string" == typeof a ? a.toLowerCase() === x.formatAllRows().toLowerCase() || ["all", "unlimited"].includes(a.toLowerCase()) ? x.formatAllRows() : +a : a
                }), this.paginationParts = x.paginationParts, "string" == typeof this.paginationParts && (this.paginationParts = this.paginationParts.replace(/\[|\]| |'/g, "").split(",")), "server" !== x.sidePagination && (x.totalRows = k.length), this.totalPages = 0, x.totalRows && (x.pageSize === x.formatAllRows() && (x.pageSize = x.totalRows, c = !0), this.totalPages = ~~((x.totalRows - 1) / x.pageSize) + 1, x.totalPages = this.totalPages), this.totalPages > 0 && x.pageNumber > this.totalPages && (x.pageNumber = this.totalPages), this.pageFrom = (x.pageNumber - 1) * x.pageSize + 1, this.pageTo = x.pageNumber * x.pageSize, this.pageTo > x.totalRows && (this.pageTo = x.totalRows), this.options.pagination && "server" !== this.options.sidePagination && (this.options.totalNotFiltered = this.options.data.length), this.options.showExtendedPagination || (this.options.totalNotFiltered = void 0), (this.paginationParts.includes("pageInfo") || this.paginationParts.includes("pageInfoShort") || this.paginationParts.includes("pageSize")) && b.push('<div class="'.concat(this.constants.classes.pull, "-").concat(x.paginationDetailHAlign, ' pagination-detail">')), (this.paginationParts.includes("pageInfo") || this.paginationParts.includes("pageInfoShort")) && (m = this.paginationParts.includes("pageInfoShort") ? x.formatDetailPagination(x.totalRows) : x.formatShowingRows(this.pageFrom, this.pageTo, x.totalRows, x.totalNotFiltered), b.push('<span class="pagination-info">\n      '.concat(m, "\n      </span>"))), this.paginationParts.includes("pageSize") && (b.push('<span class="page-list">'), n = ['<span class="'.concat(this.constants.classes.paginationDropdown, '">\n        <button class="').concat(this.constants.buttonsClass, ' dropdown-toggle" type="button" data-toggle="dropdown">\n        <span class="page-size">\n        ').concat(c ? x.formatAllRows() : x.pageSize, "\n        </span>\n        ").concat(this.constants.html.dropdownCaret, "\n        </button>\n        ").concat(this.constants.html.pageDropdown[0])], l.forEach(function (a, b) {
                    if (!x.smartDisplay || 0 === b || l[b - 1] < x.totalRows) {
                        var d;
                        d = c ? a === x.formatAllRows() ? w.constants.classes.dropdownActive : "" : a === x.pageSize ? w.constants.classes.dropdownActive : "", n.push(Ch.sprintf(w.constants.html.pageDropdownItem, d, a))
                    }
                }), n.push("".concat(this.constants.html.pageDropdown[1], "</span>")), b.push(x.formatRecordsPerPage(n.join("")))), (this.paginationParts.includes("pageInfo") || this.paginationParts.includes("pageInfoShort") || this.paginationParts.includes("pageSize")) && b.push("</span></div>"), this.paginationParts.includes("pageList")) {
                    if (b.push('<div class="'.concat(this.constants.classes.pull, "-").concat(x.paginationHAlign, ' pagination">'), Ch.sprintf(this.constants.html.pagination[0], Ch.sprintf(" pagination-%s", x.iconSize)), Ch.sprintf(this.constants.html.paginationItem, " page-pre", x.formatSRPaginationPreText(), x.paginationPreText)), this.totalPages < x.paginationSuccessivelySize ? (e = 1, f = this.totalPages) : (e = x.pageNumber - x.paginationPagesBySide, f = e + 2 * x.paginationPagesBySide), x.pageNumber < x.paginationSuccessivelySize - 1 && (f = x.paginationSuccessivelySize), x.paginationSuccessivelySize > this.totalPages - e && (e = e - (x.paginationSuccessivelySize - (this.totalPages - e)) + 1), 1 > e && (e = 1), f > this.totalPages && (f = this.totalPages), o = Math.round(x.paginationPagesBySide / 2), p = function (a) {
                        var b = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "";
                        return Ch.sprintf(w.constants.html.paginationItem, b + (a === x.pageNumber ? " ".concat(w.constants.classes.paginationActive) : ""), x.formatSRPaginationPageText(a), a)
                    }, e > 1) {
                        for (q = x.paginationPagesBySide, q >= e && (q = e - 1), d = 1; q >= d; d++) b.push(p(d));
                        e - 1 === q + 1 ? (d = e - 1, b.push(p(d))) : e - 1 > q && (e - 2 * x.paginationPagesBySide > x.paginationPagesBySide && x.paginationUseIntermediate ? (d = Math.round((e - o) / 2 + o), b.push(p(d, " page-intermediate"))) : b.push(Ch.sprintf(this.constants.html.paginationItem, " page-first-separator disabled", "", "...")))
                    }
                    for (d = e; f >= d; d++) b.push(p(d));
                    if (this.totalPages > f) for (r = this.totalPages - (x.paginationPagesBySide - 1), f >= r && (r = f + 1), f + 1 === r - 1 ? (d = f + 1, b.push(p(d))) : r > f + 1 && (this.totalPages - f > 2 * x.paginationPagesBySide && x.paginationUseIntermediate ? (d = Math.round((this.totalPages - o - f) / 2 + f), b.push(p(d, " page-intermediate"))) : b.push(Ch.sprintf(this.constants.html.paginationItem, " page-last-separator disabled", "", "..."))), d = r; d <= this.totalPages; d++) b.push(p(d));
                    b.push(Ch.sprintf(this.constants.html.paginationItem, " page-next", x.formatSRPaginationNextText(), x.paginationNextText)), b.push(this.constants.html.pagination[1], "</div>")
                }
                this.$pagination.html(b.join("")), s = ["bottom", "both"].includes(x.paginationVAlign) ? " ".concat(this.constants.classes.dropup) : "", this.$pagination.last().find(".page-list > span").addClass(s), x.onlyInfoPagination || (g = this.$pagination.find(".page-list a"), h = this.$pagination.find(".page-pre"), i = this.$pagination.find(".page-next"), j = this.$pagination.find(".page-item").not(".page-next, .page-pre, .page-last-separator, .page-first-separator"), this.totalPages <= 1 && this.$pagination.find("div.pagination").hide(), x.smartDisplay && (l.length < 2 || x.totalRows <= l[0]) && this.$pagination.find("span.page-list").hide(), this.$pagination[this.getData().length ? "show" : "hide"](), x.paginationLoop || (1 === x.pageNumber && h.addClass("disabled"), x.pageNumber === this.totalPages && i.addClass("disabled")), c && (x.pageSize = x.formatAllRows()), g.off("click").on("click", function (a) {
                    return w.onPageListChange(a)
                }), h.off("click").on("click", function (a) {
                    return w.onPagePre(a)
                }), i.off("click").on("click", function (a) {
                    return w.onPageNext(a)
                }), j.off("click").on("click", function (a) {
                    return w.onPageNumber(a)
                })), this.options.showPageGo && (t = this, u = this.$pagination.find("ul.pagination"), v = u.find("li.pageGo"), v.length || (v = a(['<li class="pageGo">', Ch.sprintf('<input type="text" class="form-control" value="%s">', this.options.pageNumber), '<button class="btn' + Ch.sprintf(" btn-%s", this.constants.buttonsClass) + Ch.sprintf(" btn-%s", x.iconSize) + '" title="' + x.formatPageGo() + '" ' + ' type="button">' + x.formatPageGo(), "</button>", "</li>"].join("")).appendTo(u), v.find("button").click(function () {
                    var a = parseInt(v.find("input").val()) || 1;
                    (1 > a || a > t.options.totalPages) && (a = 1), t.selectPage(a)
                })))
            }
        }, {
            key: "updatePagination", value: function (b) {
                b && a(b.currentTarget).hasClass("disabled") || (this.options.maintainMetaData || this.resetRows(), this.initPagination(), this.trigger("page-change", this.options.pageNumber, this.options.pageSize), "server" === this.options.sidePagination ? this.initServer() : this.initBody())
            }
        }, {
            key: "onPageListChange", value: function (b) {
                b.preventDefault();
                var c = a(b.currentTarget);
                return c.parent().addClass(this.constants.classes.dropdownActive).siblings().removeClass(this.constants.classes.dropdownActive), this.options.pageSize = c.text().toUpperCase() === this.options.formatAllRows().toUpperCase() ? this.options.formatAllRows() : +c.text(), this.$toolbar.find(".page-size").text(this.options.pageSize), this.updatePagination(b), !1
            }
        }, {
            key: "onPagePre", value: function (a) {
                return a.preventDefault(), 0 === this.options.pageNumber - 1 ? this.options.pageNumber = this.options.totalPages : this.options.pageNumber--, this.updatePagination(a), !1
            }
        }, {
            key: "onPageNext", value: function (a) {
                return a.preventDefault(), this.options.pageNumber + 1 > this.options.totalPages ? this.options.pageNumber = 1 : this.options.pageNumber++, this.updatePagination(a), !1
            }
        }, {
            key: "onPageNumber", value: function (b) {
                return b.preventDefault(), this.options.pageNumber !== +a(b.currentTarget).text() ? (this.options.pageNumber = +a(b.currentTarget).text(), this.updatePagination(b), !1) : void 0
            }
        }, {
            key: "initRow", value: function (a, b) {
                var c, e, f, g, i, j, k, l, m, n, o, p, q, r, s, t, u = this, v = [], w = {}, x = [], y = "", z = {},
                    A = [];
                if (!(Ch.findIndex(this.hiddenRows, a) > -1)) {
                    if (w = Ch.calculateObjectValue(this.options, this.options.rowStyle, [a, b], w), w && w.css) for (c = 0, e = Object.entries(w.css); c < e.length; c++) f = h(e[c], 2), g = f[0], i = f[1], x.push("".concat(g, ": ").concat(i));
                    if (z = Ch.calculateObjectValue(this.options, this.options.rowAttributes, [a, b], z)) for (j = 0, k = Object.entries(z); j < k.length; j++) l = h(k[j], 2), m = l[0], n = l[1], A.push("".concat(m, '="').concat(Ch.escapeHTML(n), '"'));
                    if (a._data && !Ch.isEmptyObject(a._data)) for (o = 0, p = Object.entries(a._data); o < p.length; o++) {
                        if (q = h(p[o], 2), r = q[0], s = q[1], "index" === r) return;
                        y += " data-".concat(r, "='").concat("object" === d(s) ? JSON.stringify(s) : s, "'")
                    }
                    return v.push("<tr", Ch.sprintf(" %s", A.length ? A.join(" ") : void 0), Ch.sprintf(' id="%s"', Array.isArray(a) ? void 0 : a._id), Ch.sprintf(' class="%s"', w.classes || (Array.isArray(a) ? void 0 : a._class)), ' data-index="'.concat(b, '"'), Ch.sprintf(' data-uniqueid="%s"', Ch.getItemField(a, this.options.uniqueId, !1)), Ch.sprintf(' data-has-detail-view="%s"', this.options.detailView && Ch.calculateObjectValue(null, this.options.detailFilter, [b, a]) ? "true" : void 0), Ch.sprintf("%s", y), ">"), this.options.cardView && v.push('<td colspan="'.concat(this.header.fields.length, '"><div class="card-views">')), t = "", Ch.hasDetailViewIcon(this.options) && (t = "<td>", Ch.calculateObjectValue(null, this.options.detailFilter, [b, a]) && (t += '\n          <a class="detail-icon" href="#">\n          '.concat(Ch.sprintf(this.constants.html.icon, this.options.iconsPrefix, this.options.icons.detailOpen), "\n          </a>\n        ")), t += "</td>"), t && "right" !== this.options.detailViewAlign && v.push(t), this.header.fields.forEach(function (c, d) {
                        var e, f, g, i, j, k, l, m, n, o, p, q, r, s, t, w = "",
                            y = Ch.getItemField(a, c, u.options.escape), z = "", A = "", B = {}, C = "",
                            D = u.header.classes[d], E = "", F = "", G = "", H = "", I = "", J = u.columns[d];
                        if ((!u.fromHtml && !u.autoMergeCells || "undefined" != typeof y || J.checkbox || J.radio) && J.visible && (!u.options.cardView || J.cardVisible)) {
                            if (J.escape && (y = Ch.escapeHTML(y)), x.concat([u.header.styles[d]]).length && (E = ' style="'.concat(x.concat([u.header.styles[d]]).join("; "), '"')), a["_".concat(c, "_id")] && (C = Ch.sprintf(' id="%s"', a["_".concat(c, "_id")])), a["_".concat(c, "_class")] && (D = Ch.sprintf(' class="%s"', a["_".concat(c, "_class")])), a["_".concat(c, "_rowspan")] && (G = Ch.sprintf(' rowspan="%s"', a["_".concat(c, "_rowspan")])), a["_".concat(c, "_colspan")] && (H = Ch.sprintf(' colspan="%s"', a["_".concat(c, "_colspan")])), a["_".concat(c, "_title")] && (I = Ch.sprintf(' title="%s"', a["_".concat(c, "_title")])), B = Ch.calculateObjectValue(u.header, u.header.cellStyles[d], [y, a, b, c], B), B.classes && (D = ' class="'.concat(B.classes, '"')), B.css) {
                                for (e = [], f = 0, g = Object.entries(B.css); f < g.length; f++) i = h(g[f], 2), j = i[0], k = i[1], e.push("".concat(j, ": ").concat(k));
                                E = ' style="'.concat(e.concat(u.header.styles[d]).join("; "), '"')
                            }
                            if (z = Ch.calculateObjectValue(J, u.header.formatters[d], [y, a, b, c], y), a["_".concat(c, "_data")] && !Ch.isEmptyObject(a["_".concat(c, "_data")])) for (l = 0, m = Object.entries(a["_".concat(c, "_data")]); l < m.length; l++) {
                                if (n = h(m[l], 2), o = n[0], p = n[1], "index" === o) return;
                                F += " data-".concat(o, '="').concat(p, '"')
                            }
                            J.checkbox || J.radio ? (A = J.checkbox ? "checkbox" : A, A = J.radio ? "radio" : A, q = J["class"] || "", r = (z === !0 || y || z && z.checked) && z !== !1, s = !J.checkboxEnabled || z && z.disabled, w = [u.options.cardView ? '<div class="card-view '.concat(q, '">') : '<td class="bs-checkbox '.concat(q, '"').concat(D).concat(E, ">"), '<label>\n            <input\n            data-index="'.concat(b, '"\n            name="').concat(u.options.selectItemName, '"\n            type="').concat(A, '"\n            ').concat(Ch.sprintf('value="%s"', a[u.options.idField]), "\n            ").concat(Ch.sprintf('checked="%s"', r ? "checked" : void 0), "\n            ").concat(Ch.sprintf('disabled="%s"', s ? "disabled" : void 0), " />\n            <span></span>\n            </label>"), u.header.formatters[d] && "string" == typeof z ? z : "", u.options.cardView ? "</div>" : "</td>"].join(""), a[u.header.stateField] = z === !0 || !!y || z && z.checked) : (z = "undefined" == typeof z || null === z ? u.options.undefinedText : z, u.options.cardView ? (t = u.options.showHeader ? '<span class="card-view-title"'.concat(E, ">").concat(Ch.getFieldTitle(u.columns, c), "</span>") : "", w = '<div class="card-view">'.concat(t, '<span class="card-view-value">').concat(z, "</span></div>"), u.options.smartDisplay && "" === z && (w = '<div class="card-view"></div>')) : w = "<td".concat(C).concat(D).concat(E).concat(F).concat(G).concat(H).concat(I, ">").concat(z, "</td>")), v.push(w)
                        }
                    }), t && "right" === this.options.detailViewAlign && v.push(t), this.options.cardView && v.push("</div></td>"), v.push("</tr>"), v.join("")
                }
            }
        }, {
            key: "initBody", value: function (b) {
                var c, d, e, f, g, h, i = this, j = this.getData();
                for (this.trigger("pre-body", j), this.$body = this.$el.find(">tbody"), this.$body.length || (this.$body = a("<tbody></tbody>").appendTo(this.$el)), this.options.pagination && "server" !== this.options.sidePagination || (this.pageFrom = 1, this.pageTo = j.length), c = [], d = a(document.createDocumentFragment()), e = !1, this.autoMergeCells = Ch.checkAutoMergeCells(j.slice(this.pageFrom - 1, this.pageTo)), f = this.pageFrom - 1; f < this.pageTo; f++) g = j[f], h = this.initRow(g, f, j, d), e = e || !!h, h && "string" == typeof h && (this.options.virtualScroll ? c.push(h) : d.append(h));
                e ? this.options.virtualScroll ? (this.virtualScroll && this.virtualScroll.destroy(), this.virtualScroll = new Fh({
                    rows: c,
                    fixedScroll: b,
                    scrollEl: this.$tableBody[0],
                    contentEl: this.$body[0],
                    itemHeight: this.options.virtualScrollItemHeight,
                    callback: function () {
                        i.fitHeader(), i.initBodyEvent()
                    }
                })) : this.$body.html(d) : this.$body.html('<tr class="no-records-found">'.concat(Ch.sprintf('<td colspan="%s">%s</td>', this.getVisibleFields().length, this.options.formatNoMatches()), "</tr>")), b || this.scrollTo(0), this.initBodyEvent(), this.updateSelected(), this.initFooter(), this.resetView(), "server" !== this.options.sidePagination && (this.options.totalRows = j.length), this.trigger("post-body", j)
            }
        }, {
            key: "initBodyEvent", value: function () {
                var b = this;
                this.$body.find("> tr[data-index] > td").off("click dblclick").on("click dblclick", function (c) {
                    var d, e = a(c.currentTarget), f = e.parent(), g = a(c.target).parents(".card-views").children(),
                        h = a(c.target).parents(".card-view"), i = f.data("index"), j = b.data[i],
                        k = b.options.cardView ? g.index(h) : e[0].cellIndex, l = b.getVisibleFields(),
                        m = l[Ch.hasDetailViewIcon(b.options) && "right" !== b.options.detailViewAlign ? k - 1 : k],
                        n = b.columns[b.fieldsColumnsIndex[m]], o = Ch.getItemField(j, m, b.options.escape);
                    e.find(".detail-icon").length || (b.trigger("click" === c.type ? "click-cell" : "dbl-click-cell", m, o, j, e), b.trigger("click" === c.type ? "click-row" : "dbl-click-row", j, f, m), "click" === c.type && b.options.clickToSelect && n.clickToSelect && !Ch.calculateObjectValue(b.options, b.options.ignoreClickToSelectOn, [c.target]) && (d = f.find(Ch.sprintf('[name="%s"]', b.options.selectItemName)), d.length && d[0].click()), "click" === c.type && b.options.detailViewByClick && b.toggleDetailView(i, b.header.detailFormatters[b.fieldsColumnsIndex[m]]))
                }).off("mousedown").on("mousedown", function (a) {
                    b.multipleSelectRowCtrlKey = a.ctrlKey || a.metaKey, b.multipleSelectRowShiftKey = a.shiftKey
                }), this.$body.find("> tr[data-index] > td > .detail-icon").off("click").on("click", function (c) {
                    return c.preventDefault(), b.toggleDetailView(a(c.currentTarget).parent().parent().data("index")), !1
                }), this.$selectItem = this.$body.find(Ch.sprintf('[name="%s"]', this.options.selectItemName)), this.$selectItem.off("click").on("click", function (c) {
                    c.stopImmediatePropagation();
                    var d = a(c.currentTarget);
                    b._toggleCheck(d.prop("checked"), d.data("index"))
                }), this.header.events.forEach(function (c, d) {
                    var e, f, g, h, i, j = c;
                    if (j && ("string" == typeof j && (j = Ch.calculateObjectValue(null, j)), e = b.header.fields[d], f = b.getVisibleFields().indexOf(e), -1 !== f)) {
                        Ch.hasDetailViewIcon(b.options) && "right" !== b.options.detailViewAlign && (f += 1), g = function (c) {
                            if (!j.hasOwnProperty(c)) return "continue";
                            var d = j[c];
                            b.$body.find(">tr:not(.no-records-found)").each(function (g, h) {
                                var i = a(h), j = i.find(b.options.cardView ? ".card-views>.card-view" : ">td").eq(f),
                                    k = c.indexOf(" "), l = c.substring(0, k), m = c.substring(k + 1);
                                j.find(m).off(l).on(l, function (a) {
                                    var c = i.data("index"), f = b.data[c], g = f[e];
                                    d.apply(b, [a, g, f, c])
                                })
                            })
                        };
                        for (h in j) i = g(h)
                    }
                })
            }
        }, {
            key: "initServer", value: function (b, c, d) {
                var e, f, g, h, i, j, k, l = this, m = {}, n = this.header.fields.indexOf(this.options.sortName), o = {
                    searchText: this.searchText,
                    sortName: this.options.sortName,
                    sortOrder: this.options.sortOrder
                };
                if (this.header.sortNames[n] && (o.sortName = this.header.sortNames[n]), this.options.pagination && "server" === this.options.sidePagination && (o.pageSize = this.options.pageSize === this.options.formatAllRows() ? this.options.totalRows : this.options.pageSize, o.pageNumber = this.options.pageNumber), !this.options.firstLoad && !firstLoadTable.includes(this.options.id)) return firstLoadTable.push(this.options.id), void 0;
                if (d || this.options.url || this.options.ajax) {
                    if ("limit" === this.options.queryParamsType && (o = {
                        search: o.searchText,
                        sort: o.sortName,
                        order: o.sortOrder
                    }, this.options.pagination && "server" === this.options.sidePagination && (o.offset = this.options.pageSize === this.options.formatAllRows() ? 0 : this.options.pageSize * (this.options.pageNumber - 1), o.limit = this.options.pageSize === this.options.formatAllRows() ? this.options.totalRows : this.options.pageSize, 0 === o.limit && delete o.limit)), this.options.search && "server" === this.options.sidePagination && this.columns.filter(function (a) {
                        return !a.searchable
                    }).length) {
                        o.searchable = [], e = !0, f = !1, g = void 0;
                        try {
                            for (i = this.columns[Symbol.iterator](); !(e = (h = i.next()).done); e = !0) j = h.value, !j.checkbox && j.searchable && (this.options.visibleSearch && j.visible || !this.options.visibleSearch) && o.searchable.push(j.field)
                        } catch (p) {
                            f = !0, g = p
                        } finally {
                            try {
                                e || null == i.return || i.return()
                            } finally {
                                if (f) throw g
                            }
                        }
                    }
                    if (Ch.isEmptyObject(this.filterColumnsPartial) || (o.filter = JSON.stringify(this.filterColumnsPartial, null)), a.extend(o, c || {}), m = Ch.calculateObjectValue(this.options, this.options.queryParams, [o], m), m !== !1) return b || this.showLoading(), k = a.extend({}, Ch.calculateObjectValue(null, this.options.ajaxOptions), {
                        type: this.options.method,
                        url: d || this.options.url,
                        data: "application/json" === this.options.contentType && "post" === this.options.method ? JSON.stringify(m) : m,
                        cache: this.options.cache,
                        contentType: this.options.contentType,
                        dataType: this.options.dataType,
                        success: function (a, c, d) {
                            var e = Ch.calculateObjectValue(l.options, l.options.responseHandler, [a, d], a);
                            l.load(e), l.trigger("load-success", e, d && d.status, d), b || l.hideLoading(), "server" === l.options.sidePagination && e[l.options.totalField] > 0 && !e[l.options.dataField].length && l.updatePagination()
                        },
                        error: function (a) {
                            var c = [];
                            "server" === l.options.sidePagination && (c = {}, c[l.options.totalField] = 0, c[l.options.dataField] = []), l.load(c), l.trigger("load-error", a && a.status, a), b || l.$tableLoading.hide()
                        }
                    }), this.options.ajax ? Ch.calculateObjectValue(this, this.options.ajax, [k], null) : (this._xhr && 4 !== this._xhr.readyState && this._xhr.abort(), this._xhr = a.ajax(k)), m
                }
            }
        }, {
            key: "initSearchText", value: function () {
                if (this.options.search && (this.searchText = "", "" !== this.options.searchText)) {
                    var a = this.$toolbar.find(".search input");
                    a.val(this.options.searchText), this.onSearch({currentTarget: a, firedByInitSearchText: !0})
                }
            }
        }, {
            key: "getCaret", value: function () {
                var b = this;
                this.$header.find("th").each(function (c, d) {
                    a(d).find(".sortable").removeClass("desc asc").addClass(a(d).data("field") === b.options.sortName ? b.options.sortOrder : "both")
                })
            }
        }, {
            key: "updateSelected", value: function () {
                var b = this.$selectItem.filter(":enabled").length && this.$selectItem.filter(":enabled").length === this.$selectItem.filter(":enabled").filter(":checked").length;
                this.$selectAll.add(this.$selectAll_).prop("checked", b), this.$selectItem.each(function (b, c) {
                    a(c).closest("tr")[a(c).prop("checked") ? "addClass" : "removeClass"]("selected")
                })
            }
        }, {
            key: "updateRows", value: function () {
                var b = this;
                this.$selectItem.each(function (c, d) {
                    b.data[a(d).data("index")][b.header.stateField] = a(d).prop("checked")
                })
            }
        }, {
            key: "resetRows", value: function () {
                var a, b, c, d = !0, e = !1, f = void 0;
                try {
                    for (b = this.data[Symbol.iterator](); !(d = (a = b.next()).done); d = !0) c = a.value, this.$selectAll.prop("checked", !1), this.$selectItem.prop("checked", !1), this.header.stateField && (c[this.header.stateField] = !1)
                } catch (g) {
                    e = !0, f = g
                } finally {
                    try {
                        d || null == b.return || b.return()
                    } finally {
                        if (e) throw f
                    }
                }
                this.initHiddenRows()
            }
        }, {
            key: "trigger", value: function (c) {
                var d, e, f, g, h = "".concat(c, ".bs.table");
                for (e = arguments.length, f = new Array(e > 1 ? e - 1 : 0), g = 1; e > g; g++) f[g - 1] = arguments[g];
                (d = this.options)[b.EVENTS[h]].apply(d, f), this.$el.trigger(a.Event(h), f), this.options.onAll(h, f), this.$el.trigger(a.Event("all.bs.table"), [h, f])
            }
        }, {
            key: "resetHeader", value: function () {
                var a = this;
                clearTimeout(this.timeoutId_), this.timeoutId_ = setTimeout(function () {
                    return a.fitHeader()
                }, this.$el.is(":hidden") ? 100 : 0)
            }
        }, {
            key: "fitHeader", value: function () {
                var b, c, d, e, f, g, h, i, j, k, l, m = this;
                if (this.$el.is(":hidden")) return this.timeoutId_ = setTimeout(function () {
                    return m.fitHeader()
                }, 100), void 0;
                for (b = this.$tableBody.get(0), c = b.scrollWidth > b.clientWidth && b.scrollHeight > b.clientHeight + this.$header.outerHeight() ? Ch.getScrollBarWidth() : 0, this.$el.css("margin-top", -this.$header.outerHeight()), d = a(":focus"), d.length > 0 && (e = d.parents("th"), e.length > 0 && (f = e.attr("data-field"), void 0 !== f && (g = this.$header.find("[data-field='".concat(f, "']")), g.length > 0 && g.find(":input").addClass("focus-temp")))), this.$header_ = this.$header.clone(!0, !0), this.$selectAll_ = this.$header_.find('[name="btSelectAll"]'), this.$tableHeader.css("margin-right", c).find("table").css("width", this.$el.outerWidth()).html("").attr("class", this.$el.attr("class")).append(this.$header_), this.$tableLoading.css("width", this.$el.outerWidth()), h = a(".focus-temp:visible:eq(0)"), h.length > 0 && (h.focus(), this.$header.find(".focus-temp").removeClass("focus-temp")), this.$header.find("th[data-field]").each(function (b, c) {
                    m.$header_.find(Ch.sprintf('th[data-field="%s"]', a(c).data("field"))).data(a(c).data())
                }), i = this.getVisibleFields(), j = this.$header_.find("th"), k = this.$body.find(">tr:not(.no-records-found,.virtual-scroll-top)").eq(0); k.length && k.find('>td[colspan]:not([colspan="1"])').length;) k = k.next();
                l = k.find("> *").length, k.find("> *").each(function (b, c) {
                    var d, e, f, g, h, k = a(c);
                    return Ch.hasDetailViewIcon(m.options) && (0 === b && "right" !== m.options.detailViewAlign || b === l - 1 && "right" === m.options.detailViewAlign) ? (d = j.filter(".detail"), e = d.innerWidth() - d.find(".fht-cell").width(), d.find(".fht-cell").width(k.innerWidth() - e), void 0) : (f = Ch.hasDetailViewIcon(m.options) && "right" !== m.options.detailViewAlign ? b - 1 : b, g = m.$header_.find(Ch.sprintf('th[data-field="%s"]', i[f])), g.length > 1 && (g = a(j[k[0].cellIndex])), h = g.innerWidth() - g.find(".fht-cell").width(), g.find(".fht-cell").width(k.innerWidth() - h), void 0)
                }), this.horizontalScroll(), this.trigger("post-header")
            }
        }, {
            key: "initFooter", value: function () {
                var a, b, c, d, e, f, g, i, j, k, l, m, n, o, p, q, r, s, t;
                if (this.options.showFooter && !this.options.cardView) {
                    a = this.getData(), b = [], c = "", Ch.hasDetailViewIcon(this.options) && (c = '<th class="detail"><div class="th-inner"></div><div class="fht-cell"></div></th>'), c && "right" !== this.options.detailViewAlign && b.push(c), d = !0, e = !1, f = void 0;
                    try {
                        for (i = this.columns[Symbol.iterator](); !(d = (g = i.next()).done); d = !0) if (j = g.value, k = "", l = "", m = [], n = {}, o = Ch.sprintf(' class="%s"', j["class"]), j.visible) {
                            if (this.options.cardView && !j.cardVisible) return;
                            if (k = Ch.sprintf("text-align: %s; ", j.falign ? j.falign : j.align), l = Ch.sprintf("vertical-align: %s; ", j.valign), n = Ch.calculateObjectValue(null, this.options.footerStyle, [j]), n && n.css) for (p = 0, q = Object.entries(n.css); p < q.length; p++) r = h(q[p], 2), s = r[0], t = r[1], m.push("".concat(s, ": ").concat(t));
                            n && n.classes && (o = Ch.sprintf(' class="%s"', j["class"] ? [j["class"], n.classes].join(" ") : n.classes)), b.push("<th", o, Ch.sprintf(' style="%s"', k + l + m.concat().join("; ")), ">"), b.push('<div class="th-inner">'), b.push(Ch.calculateObjectValue(j, j.footerFormatter, [a], this.footerData[0] && this.footerData[0][j.field] || "")), b.push("</div>"), b.push('<div class="fht-cell"></div>'), b.push("</div>"), b.push("</th>")
                        }
                    } catch (u) {
                        e = !0, f = u
                    } finally {
                        try {
                            d || null == i.return || i.return()
                        } finally {
                            if (e) throw f
                        }
                    }
                    c && "right" === this.options.detailViewAlign && b.push(c), this.options.height || this.$tableFooter.length || (this.$el.append("<tfoot><tr></tr></tfoot>"), this.$tableFooter = this.$el.find("tfoot")), this.$tableFooter.find("tr").html(b.join("")), this.trigger("post-footer", this.$tableFooter)
                }
            }
        }, {
            key: "fitFooter", value: function () {
                var b, c, d, e, f, g = this;
                if (this.$el.is(":hidden")) return setTimeout(function () {
                    return g.fitFooter()
                }, 100), void 0;
                for (b = this.$tableBody.get(0), c = b.scrollWidth > b.clientWidth && b.scrollHeight > b.clientHeight + this.$header.outerHeight() ? Ch.getScrollBarWidth() : 0, this.$tableFooter.css("margin-right", c).find("table").css("width", this.$el.outerWidth()).attr("class", this.$el.attr("class")), this.getVisibleFields(), d = this.$tableFooter.find("th"), e = this.$body.find(">tr:first-child:not(.no-records-found)"); e.length && e.find('>td[colspan]:not([colspan="1"])').length;) e = e.next();
                f = e.find("> *").length, e.find("> *").each(function (b, c) {
                    var e, h, i, j, k = a(c);
                    return Ch.hasDetailViewIcon(g.options) && (0 === b && "left" === g.options.detailViewAlign || b === f - 1 && "right" === g.options.detailViewAlign) ? (e = d.filter(".detail"), h = e.innerWidth() - e.find(".fht-cell").width(), e.find(".fht-cell").width(k.innerWidth() - h), void 0) : (i = d.eq(b), j = i.innerWidth() - i.find(".fht-cell").width(), i.find(".fht-cell").width(k.innerWidth() - j), void 0)
                }), this.horizontalScroll()
            }
        }, {
            key: "horizontalScroll", value: function () {
                var a = this;
                this.$tableBody.off("scroll").on("scroll", function () {
                    var b = a.$tableBody.scrollLeft();
                    a.options.showHeader && a.options.height && a.$tableHeader.scrollLeft(b), a.options.showFooter && !a.options.cardView && a.$tableFooter.scrollLeft(b), a.trigger("scroll-body", a.$tableBody)
                })
            }
        }, {
            key: "getVisibleFields", value: function () {
                var a, b, c, d, e = [], f = !0, g = !1, h = void 0;
                try {
                    for (b = this.header.fields[Symbol.iterator](); !(f = (a = b.next()).done); f = !0) c = a.value, d = this.columns[this.fieldsColumnsIndex[c]], d && d.visible && e.push(c)
                } catch (i) {
                    g = !0, h = i
                } finally {
                    try {
                        f || null == b.return || b.return()
                    } finally {
                        if (g) throw h
                    }
                }
                return e
            }
        }, {
            key: "initHiddenRows", value: function () {
                this.hiddenRows = []
            }
        }, {
            key: "getOptions", value: function () {
                var b = a.extend({}, this.options);
                return delete b.data, a.extend(!0, {}, b)
            }
        }, {
            key: "refreshOptions", value: function (b) {
                Ch.compareObjects(this.options, b, !0) || (this.options = a.extend(this.options, b), this.trigger("refresh-options", this.options), this.destroy(), this.init())
            }
        }, {
            key: "getData", value: function (a) {
                var b, c = this, d = this.options.data;
                return !(this.searchText || this.options.customSearch || this.options.sortName) && Ch.isEmptyObject(this.filterColumns) && Ch.isEmptyObject(this.filterColumnsPartial) || a && a.unfiltered || (d = this.data), a && a.useCurrentPage && (d = d.slice(this.pageFrom - 1, this.pageTo)), a && !a.includeHiddenRows && (b = this.getHiddenRows(), d = d.filter(function (a) {
                    return -1 === Ch.findIndex(b, a)
                })), a && a.formatted && d.forEach(function (a) {
                    var b, d, e, f, g, i;
                    for (b = 0, d = Object.entries(a); b < d.length; b++) {
                        if (e = h(d[b], 2), f = e[0], g = e[1], i = c.columns[c.fieldsColumnsIndex[f]], !i) return;
                        a[f] = Ch.calculateObjectValue(i, c.header.formatters[i.fieldIndex], [g, a, a.index, i.field], g)
                    }
                }), d
            }
        }, {
            key: "getSelections", value: function () {
                var a = this;
                return this.data.filter(function (b) {
                    return b[a.header.stateField] === !0
                })
            }
        }, {
            key: "getAllSelections", value: function () {
                var a = this;
                return this.options.data.filter(function (b) {
                    return b[a.header.stateField] === !0
                })
            }
        }, {
            key: "load", value: function (a) {
                var b = !1, c = a;
                this.options.pagination && "server" === this.options.sidePagination && (this.options.totalRows = c[this.options.totalField]), this.options.pagination && "server" === this.options.sidePagination && (this.options.totalNotFiltered = c[this.options.totalNotFilteredField]), b = c.fixedScroll, c = Array.isArray(c) ? c : c[this.options.dataField], this.initData(c), this.initSearch(), this.initPagination(), this.initBody(b)
            }
        }, {
            key: "append", value: function (a) {
                this.initData(a, "append"), this.initSearch(), this.initPagination(), this.initSort(), this.initBody(!0)
            }
        }, {
            key: "prepend", value: function (a) {
                this.initData(a, "prepend"), this.initSearch(), this.initPagination(), this.initSort(), this.initBody(!0)
            }
        }, {
            key: "remove", value: function (a) {
                var b, c, d, e = this.options.data.length;
                if (a.hasOwnProperty("field") && a.hasOwnProperty("values")) {
                    for (b = e - 1; b >= 0; b--) d = !1, c = this.options.data[b], (c.hasOwnProperty(a.field) || "$index" === a.field) && (d = c.hasOwnProperty(a.field) || "$index" !== a.field ? a.values.includes(c[a.field]) : a.values.includes(b), d && (this.options.data.splice(b, 1), "server" === this.options.sidePagination && (this.options.totalRows -= 1)));
                    e !== this.options.data.length && (this.initSearch(), this.initPagination(), this.initSort(), this.initBody(!0))
                }
            }
        }, {
            key: "removeAll", value: function () {
                this.options.data.length > 0 && (this.options.data.splice(0, this.options.data.length), this.initSearch(), this.initPagination(), this.initBody(!0))
            }
        }, {
            key: "insertRow", value: function (a) {
                a.hasOwnProperty("index") && a.hasOwnProperty("row") && (this.options.data.splice(a.index, 0, a.row), this.initSearch(), this.initPagination(), this.initSort(), this.initBody(!0))
            }
        }, {
            key: "updateRow", value: function (b) {
                var c, d, e, f = Array.isArray(b) ? b : [b], g = !0, h = !1, i = void 0;
                try {
                    for (d = f[Symbol.iterator](); !(g = (c = d.next()).done); g = !0) e = c.value, e.hasOwnProperty("index") && e.hasOwnProperty("row") && (e.hasOwnProperty("replace") && e.replace ? this.options.data[e.index] = e.row : a.extend(this.options.data[e.index], e.row))
                } catch (j) {
                    h = !0, i = j
                } finally {
                    try {
                        g || null == d.return || d.return()
                    } finally {
                        if (h) throw i
                    }
                }
                this.initSearch(), this.initPagination(), this.initSort(), this.initBody(!0)
            }
        }, {
            key: "getRowByUniqueId", value: function (a) {
                var b, c, d, e = this.options.uniqueId, f = this.options.data.length, g = a, h = null;
                for (b = f - 1; b >= 0; b--) {
                    if (c = this.options.data[b], c.hasOwnProperty(e)) d = c[e]; else {
                        if (!c._data || !c._data.hasOwnProperty(e)) continue;
                        d = c._data[e]
                    }
                    if ("string" == typeof d ? g = g.toString() : "number" == typeof d && (Number(d) === d && 0 === d % 1 ? g = parseInt(g) : d === Number(d) && 0 !== d && (g = parseFloat(g))), d === g) {
                        h = c;
                        break
                    }
                }
                return h
            }
        }, {
            key: "updateByUniqueId", value: function (b) {
                var c, d, e, f, g = Array.isArray(b) ? b : [b], h = !0, i = !1, j = void 0;
                try {
                    for (d = g[Symbol.iterator](); !(h = (c = d.next()).done); h = !0) e = c.value, e.hasOwnProperty("id") && e.hasOwnProperty("row") && (f = this.options.data.indexOf(this.getRowByUniqueId(e.id)), -1 !== f && (e.hasOwnProperty("replace") && e.replace ? this.options.data[f] = e.row : a.extend(this.options.data[f], e.row)))
                } catch (k) {
                    i = !0, j = k
                } finally {
                    try {
                        h || null == d.return || d.return()
                    } finally {
                        if (i) throw j
                    }
                }
                this.initSearch(), this.initPagination(), this.initSort(), this.initBody(!0)
            }
        }, {
            key: "removeByUniqueId", value: function (a) {
                var b = this.options.data.length, c = this.getRowByUniqueId(a);
                c && this.options.data.splice(this.options.data.indexOf(c), 1), b !== this.options.data.length && (this.initSearch(), this.initPagination(), this.initBody(!0))
            }
        }, {
            key: "updateCell", value: function (a) {
                a.hasOwnProperty("index") && a.hasOwnProperty("field") && a.hasOwnProperty("value") && (this.data[a.index][a.field] = a.value, a.reinit !== !1 && (this.initSort(), this.initBody(!0)))
            }
        }, {
            key: "updateCellByUniqueId", value: function (a) {
                var b = this, c = Array.isArray(a) ? a : [a];
                c.forEach(function (a) {
                    var c = a.id, d = a.field, e = a.value, f = b.options.data.indexOf(b.getRowByUniqueId(c));
                    -1 !== f && (b.options.data[f][d] = e)
                }), a.reinit !== !1 && (this.initSort(), this.initBody(!0))
            }
        }, {
            key: "showRow", value: function (a) {
                this._toggleRow(a, !0)
            }
        }, {
            key: "hideRow", value: function (a) {
                this._toggleRow(a, !1)
            }
        }, {
            key: "_toggleRow", value: function (a, b) {
                var c, d;
                a.hasOwnProperty("index") ? c = this.getData()[a.index] : a.hasOwnProperty("uniqueId") && (c = this.getRowByUniqueId(a.uniqueId)), c && (d = Ch.findIndex(this.hiddenRows, c), b || -1 !== d ? b && d > -1 && this.hiddenRows.splice(d, 1) : this.hiddenRows.push(c), this.initBody(!0), this.initPagination())
            }
        }, {
            key: "getHiddenRows", value: function (a) {
                var b, c, d, e, f, g, h, i;
                if (a) return this.initHiddenRows(), this.initBody(!0), this.initPagination(), void 0;
                b = this.getData(), c = [], d = !0, e = !1, f = void 0;
                try {
                    for (h = b[Symbol.iterator](); !(d = (g = h.next()).done); d = !0) i = g.value, this.hiddenRows.includes(i) && c.push(i)
                } catch (j) {
                    e = !0, f = j
                } finally {
                    try {
                        d || null == h.return || h.return()
                    } finally {
                        if (e) throw f
                    }
                }
                return this.hiddenRows = c, c
            }
        }, {
            key: "showColumn", value: function (a) {
                var b = this, c = Array.isArray(a) ? a : [a];
                c.forEach(function (a) {
                    b._toggleColumn(b.fieldsColumnsIndex[a], !0, !0)
                })
            }
        }, {
            key: "hideColumn", value: function (a) {
                var b = this, c = Array.isArray(a) ? a : [a];
                c.forEach(function (a) {
                    b._toggleColumn(b.fieldsColumnsIndex[a], !1, !0)
                })
            }
        }, {
            key: "_toggleColumn", value: function (a, b, c) {
                if (-1 !== a && this.columns[a].visible !== b && (this.columns[a].visible = b, this.initHeader(), this.initSearch(), this.initPagination(), this.initBody(), this.options.showColumns)) {
                    var d = this.$toolbar.find('.keep-open input:not(".toggle-all")').prop("disabled", !1);
                    c && d.filter(Ch.sprintf('[value="%s"]', a)).prop("checked", b), d.filter(":checked").length <= this.options.minimumCountColumns && d.filter(":checked").prop("disabled", !0)
                }
            }
        }, {
            key: "getVisibleColumns", value: function () {
                var a = this;
                return this.columns.filter(function (b) {
                    return b.visible && !a.isSelectionColumn(b)
                })
            }
        }, {
            key: "getHiddenColumns", value: function () {
                return this.columns.filter(function (a) {
                    var b = a.visible;
                    return !b
                })
            }
        }, {
            key: "isSelectionColumn", value: function (a) {
                return a.radio || a.checkbox
            }
        }, {
            key: "showAllColumns", value: function () {
                this._toggleAllColumns(!0)
            }
        }, {
            key: "hideAllColumns", value: function () {
                this._toggleAllColumns(!1)
            }
        }, {
            key: "_toggleAllColumns", value: function (b) {
                var c, d, e, f, g = this, h = !0, i = !1, j = void 0;
                try {
                    for (d = this.columns.slice().reverse()[Symbol.iterator](); !(h = (c = d.next()).done); h = !0) if (e = c.value, e.switchable) {
                        if (!b && this.options.showColumns && this.getVisibleColumns().length === this.options.minimumCountColumns) continue;
                        e.visible = b
                    }
                } catch (k) {
                    i = !0, j = k
                } finally {
                    try {
                        h || null == d.return || d.return()
                    } finally {
                        if (i) throw j
                    }
                }
                this.initHeader(), this.initSearch(), this.initPagination(), this.initBody(), this.options.showColumns && (f = this.$toolbar.find('.keep-open input[type="checkbox"]:not(".toggle-all")').prop("disabled", !1), b ? f.prop("checked", b) : f.get().reverse().forEach(function (c) {
                    f.filter(":checked").length > g.options.minimumCountColumns && a(c).prop("checked", b)
                }), f.filter(":checked").length <= this.options.minimumCountColumns && f.filter(":checked").prop("disabled", !0))
            }
        }, {
            key: "mergeCells", value: function (a) {
                var b, c, d, e = a.index, f = this.getVisibleFields().indexOf(a.field), g = a.rowspan || 1,
                    h = a.colspan || 1, i = this.$body.find(">tr");
                if (Ch.hasDetailViewIcon(this.options) && (f += 1), d = i.eq(e).find(">td").eq(f), !(0 > e || 0 > f || e >= this.data.length)) {
                    for (b = e; e + g > b; b++) for (c = f; f + h > c; c++) i.eq(b).find(">td").eq(c).hide();
                    d.attr("rowspan", g).attr("colspan", h).show()
                }
            }
        }, {
            key: "checkAll", value: function () {
                this._toggleCheckAll(!0)
            }
        }, {
            key: "uncheckAll", value: function () {
                this._toggleCheckAll(!1)
            }
        }, {
            key: "_toggleCheckAll", value: function (a) {
                var b, c = this.getSelections();
                return this.$selectAll.add(this.$selectAll_).prop("checked", a), this.$selectItem.filter(":enabled").prop("checked", a), this.updateRows(), b = this.getSelections(), a ? (this.trigger("check-all", b, c), void 0) : (this.trigger("uncheck-all", b, c), void 0)
            }
        }, {
            key: "checkInvert", value: function () {
                var b = this.$selectItem.filter(":enabled"), c = b.filter(":checked");
                b.each(function (b, c) {
                    a(c).prop("checked", !a(c).prop("checked"))
                }), this.updateRows(), this.updateSelected(), this.trigger("uncheck-some", c), c = this.getSelections(), this.trigger("check-some", c)
            }
        }, {
            key: "check", value: function (a) {
                this._toggleCheck(!0, a)
            }
        }, {
            key: "uncheck", value: function (a) {
                this._toggleCheck(!1, a)
            }
        }, {
            key: "_toggleCheck", value: function (a, b) {
                var c, d, e, f, g, h, i, j, k = this.$selectItem.filter('[data-index="'.concat(b, '"]')),
                    l = this.data[b];
                if (k.is(":radio") || this.options.singleSelect || this.options.multipleSelectRow && !this.multipleSelectRowCtrlKey && !this.multipleSelectRowShiftKey) {
                    c = !0, d = !1, e = void 0;
                    try {
                        for (g = this.options.data[Symbol.iterator](); !(c = (f = g.next()).done); c = !0) h = f.value, h[this.header.stateField] = !1
                    } catch (m) {
                        d = !0, e = m
                    } finally {
                        try {
                            c || null == g.return || g.return()
                        } finally {
                            if (d) throw e
                        }
                    }
                    this.$selectItem.filter(":checked").not(k).prop("checked", !1)
                }
                if (l[this.header.stateField] = a, this.options.multipleSelectRow) {
                    if (this.multipleSelectRowShiftKey && this.multipleSelectRowLastSelectedIndex >= 0) for (i = [this.multipleSelectRowLastSelectedIndex, b].sort(), j = i[0] + 1; j < i[1]; j++) this.data[j][this.header.stateField] = !0, this.$selectItem.filter('[data-index="'.concat(j, '"]')).prop("checked", !0);
                    this.multipleSelectRowCtrlKey = !1, this.multipleSelectRowShiftKey = !1, this.multipleSelectRowLastSelectedIndex = a ? b : -1
                }
                k.prop("checked", a), this.updateSelected(), this.trigger(a ? "check" : "uncheck", this.data[b], k)
            }
        }, {
            key: "checkBy", value: function (a) {
                this._toggleCheckBy(!0, a)
            }
        }, {
            key: "uncheckBy", value: function (a) {
                this._toggleCheckBy(!1, a)
            }
        }, {
            key: "_toggleCheckBy", value: function (a, b) {
                var c, d = this;
                b.hasOwnProperty("field") && b.hasOwnProperty("values") && (c = [], this.data.forEach(function (e, f) {
                    if (!e.hasOwnProperty(b.field)) return !1;
                    if (b.values.includes(e[b.field])) {
                        var g = d.$selectItem.filter(":enabled").filter(Ch.sprintf('[data-index="%s"]', f));
                        if (g = a ? g.not(":checked") : g.filter(":checked"), !g.length) return;
                        g.prop("checked", a), e[d.header.stateField] = a, c.push(e), d.trigger(a ? "check" : "uncheck", e, g)
                    }
                }), this.updateSelected(), this.trigger(a ? "check-some" : "uncheck-some", c))
            }
        }, {
            key: "refresh", value: function (a) {
                a && a.url && (this.options.url = a.url), a && a.pageNumber && (this.options.pageNumber = a.pageNumber), a && a.pageSize && (this.options.pageSize = a.pageSize), table.rememberSelecteds = {}, table.rememberSelectedIds = {}, this.trigger("refresh", this.initServer(a && a.silent, a && a.query, a && a.url))
            }
        }, {
            key: "destroy", value: function () {
                this.$el.insertBefore(this.$container), a(this.options.toolbar).insertBefore(this.$el), this.$container.next().remove(), this.$container.remove(), this.$el.html(this.$el_.html()).css("margin-top", "0").attr("class", this.$el_.attr("class") || "")
            }
        }, {
            key: "resetView", value: function (a) {
                var b, c, d, e, f, g, h = 0;
                a && a.height && (this.options.height = a.height), this.$selectAll.prop("checked", this.$selectItem.length > 0 && this.$selectItem.length === this.$selectItem.filter(":checked").length), this.$tableContainer.toggleClass("has-card-view", this.options.cardView), !this.options.cardView && this.options.showHeader && this.options.height ? (this.$tableHeader.show(), this.resetHeader(), h += this.$header.outerHeight(!0) + 1) : (this.$tableHeader.hide(), this.trigger("post-header")), !this.options.cardView && this.options.showFooter && (this.$tableFooter.show(), this.fitFooter(), this.options.height && (h += this.$tableFooter.outerHeight(!0))), this.$container.hasClass("fullscreen") ? (this.$tableContainer.css("height", ""), this.$tableContainer.css("width", "")) : this.options.height && (this.$tableBorder && (this.$tableBorder.css("width", ""), this.$tableBorder.css("height", "")), b = this.$toolbar.outerHeight(!0), c = this.$pagination.outerHeight(!0), d = this.options.height - b - c, e = this.$tableBody.find(">table"), f = e.outerHeight(), this.$tableContainer.css("height", "".concat(d, "px")), this.$tableBorder && e.is(":visible") && (g = d - f - 2, this.$tableBody[0].scrollWidth - this.$tableBody.innerWidth() && (g -= Ch.getScrollBarWidth()), this.$tableBorder.css("width", "".concat(e.outerWidth(), "px")), this.$tableBorder.css("height", "".concat(g, "px")))), this.options.cardView ? (this.$el.css("margin-top", "0"), this.$tableContainer.css("padding-bottom", "0"), this.$tableFooter.hide()) : (this.getCaret(), this.$tableContainer.css("padding-bottom", "".concat(h, "px"))), this.trigger("reset-view")
            }
        }, {
            key: "showLoading", value: function () {
                this.$tableLoading.css("display", "flex");
                var a = this.options.loadingFontSize;
                "auto" === this.options.loadingFontSize && (a = .04 * this.$tableLoading.width(), a = Math.max(12, a), a = Math.min(32, a), a = "".concat(a, "px")), this.$tableLoading.find(".loading-text").css("font-size", a)
            }
        }, {
            key: "hideLoading", value: function () {
                this.$tableLoading.css("display", "none")
            }
        }, {
            key: "toggleShowSearch", value: function () {
                this.$el.parents(".select-table").siblings().slideToggle()
            }
        }, {
            key: "togglePagination", value: function () {
                var a, b;
                this.options.pagination = !this.options.pagination, a = this.options.showButtonIcons ? this.options.pagination ? this.options.icons.paginationSwitchDown : this.options.icons.paginationSwitchUp : "", b = this.options.showButtonText ? this.options.pagination ? this.options.formatPaginationSwitchUp() : this.options.formatPaginationSwitchDown() : "", this.$toolbar.find('button[name="paginationSwitch"]').html(Ch.sprintf(this.constants.html.icon, this.options.iconsPrefix, a) + " " + b), this.updatePagination()
            }
        }, {
            key: "toggleFullscreen", value: function () {
                this.$el.closest(".bootstrap-table").toggleClass("fullscreen"), this.resetView()
            }
        }, {
            key: "toggleView", value: function () {
                var a, b;
                this.options.cardView = !this.options.cardView, this.initHeader(), a = this.options.showButtonIcons ? this.options.cardView ? this.options.icons.toggleOn : this.options.icons.toggleOff : "", b = this.options.showButtonText ? this.options.cardView ? this.options.formatToggleOff() : this.options.formatToggleOn() : "", this.$toolbar.find('button[name="toggle"]').html(Ch.sprintf(this.constants.html.icon, this.options.iconsPrefix, a) + " " + b), this.initBody(), this.trigger("toggle", this.options.cardView)
            }
        }, {
            key: "resetSearch", value: function (a) {
                var b = this.$toolbar.find(".search input");
                b.val(a || ""), this.onSearch({currentTarget: b})
            }
        }, {
            key: "filterBy", value: function (b, c) {
                this.filterOptions = Ch.isEmptyObject(c) ? this.options.filterOptions : a.extend(this.options.filterOptions, c), this.filterColumns = Ch.isEmptyObject(b) ? {} : b, this.options.pageNumber = 1, this.initSearch(), this.updatePagination()
            }
        }, {
            key: "scrollTo", value: function c(b) {
                var e, c;
                return "undefined" == typeof b ? this.$tableBody.scrollTop() : (e = {
                    unit: "px",
                    value: 0
                }, "object" === d(b) ? e = Object.assign(e, b) : "string" == typeof b && "bottom" === b ? e.value = this.$tableBody[0].scrollHeight : ("string" == typeof b || "number" == typeof b) && (e.value = b), c = e.value, "rows" === e.unit && (c = 0, this.$body.find("> tr:lt(".concat(e.value, ")")).each(function (b, d) {
                    c += a(d).outerHeight(!0)
                })), this.$tableBody.scrollTop(c), void 0)
            }
        }, {
            key: "getScrollPosition", value: function () {
                return this.scrollTo()
            }
        }, {
            key: "selectPage", value: function (a) {
                a > 0 && a <= this.options.totalPages && (this.options.pageNumber = a, this.updatePagination())
            }
        }, {
            key: "prevPage", value: function () {
                this.options.pageNumber > 1 && (this.options.pageNumber--, this.updatePagination())
            }
        }, {
            key: "nextPage", value: function () {
                this.options.pageNumber < this.options.totalPages && (this.options.pageNumber++, this.updatePagination())
            }
        }, {
            key: "toggleDetailView", value: function (a, b) {
                var c = this.$body.find(Ch.sprintf('> tr[data-index="%s"]', a));
                c.next().is("tr.detail-view") ? this.collapseRow(a) : this.expandRow(a, b), this.resetView()
            }
        }, {
            key: "expandRow", value: function (a, b) {
                var c, d, e, f = this.data[a],
                    g = this.$body.find(Ch.sprintf('> tr[data-index="%s"][data-has-detail-view]', a));
                g.next().is("tr.detail-view") || (this.options.detailViewIcon && g.find("a.detail-icon").html(Ch.sprintf(this.constants.html.icon, this.options.iconsPrefix, this.options.icons.detailClose)), g.after(Ch.sprintf('<tr class="detail-view"><td colspan="%s"></td></tr>', g.children("td").length)), c = g.next().find("td"), d = b || this.options.detailFormatter, e = Ch.calculateObjectValue(this.options, d, [a, f, c], ""), 1 === c.length && c.append(e), this.trigger("expand-row", a, f, c))
            }
        }, {
            key: "expandRowByUniqueId", value: function (a) {
                var b = this.getRowByUniqueId(a);
                b && this.expandRow(this.data.indexOf(b))
            }
        }, {
            key: "collapseRow", value: function (a) {
                var b = this.data[a], c = this.$body.find(Ch.sprintf('> tr[data-index="%s"][data-has-detail-view]', a));
                c.next().is("tr.detail-view") && (this.options.detailViewIcon && c.find("a.detail-icon").html(Ch.sprintf(this.constants.html.icon, this.options.iconsPrefix, this.options.icons.detailOpen)), this.trigger("collapse-row", a, b, c.next()), c.next().remove())
            }
        }, {
            key: "collapseRowByUniqueId", value: function (a) {
                var b = this.getRowByUniqueId(a);
                b && this.collapseRow(this.data.indexOf(b))
            }
        }, {
            key: "expandAllRows", value: function () {
                var b, c = this.$body.find("> tr[data-index][data-has-detail-view]");
                for (b = 0; b < c.length; b++) this.expandRow(a(c[b]).data("index"))
            }
        }, {
            key: "collapseAllRows", value: function () {
                var b, c = this.$body.find("> tr[data-index][data-has-detail-view]");
                for (b = 0; b < c.length; b++) this.collapseRow(a(c[b]).data("index"))
            }
        }, {
            key: "updateColumnTitle", value: function (b) {
                if (b.hasOwnProperty("field") && b.hasOwnProperty("title") && (this.columns[this.fieldsColumnsIndex[b.field]].title = this.options.escape ? Ch.escapeHTML(b.title) : b.title, this.columns[this.fieldsColumnsIndex[b.field]].visible)) {
                    var c = void 0 !== this.options.height ? this.$tableHeader : this.$header;
                    c.find("th[data-field]").each(function (c, d) {
                        return a(d).data("field") === b.field ? (a(a(d).find(".th-inner")[0]).text(b.title), !1) : void 0
                    })
                }
            }
        }, {
            key: "updateFormatText", value: function (a, b) {
                /^format/.test(a) && this.options[a] && ("string" == typeof b ? this.options[a] = function () {
                    return b
                } : "function" == typeof b && (this.options[a] = b), this.initToolbar(), this.initPagination(), this.initBody())
            }
        }]), b
    }(), Gh.VERSION = qh.VERSION, Gh.DEFAULTS = qh.DEFAULTS, Gh.LOCALES = qh.LOCALES, Gh.COLUMN_DEFAULTS = qh.COLUMN_DEFAULTS, Gh.METHODS = qh.METHODS, Gh.EVENTS = qh.EVENTS, a.BootstrapTable = Gh, a.fn.bootstrapTable = function (b) {
        var c, e, f, g;
        for (c = arguments.length, e = new Array(c > 1 ? c - 1 : 0), f = 1; c > f; f++) e[f - 1] = arguments[f];
        return this.each(function (c, f) {
            var h, i = a(f).data("bootstrap.table"), j = a.extend({}, Gh.DEFAULTS, a(f).data(), "object" === d(b) && b);
            if ("string" == typeof b) {
                if (!qh.METHODS.includes(b)) throw new Error("Unknown method: ".concat(b));
                if (!i) return;
                g = (h = i)[b].apply(h, e), "destroy" === b && a(f).removeData("bootstrap.table")
            }
            i || a(f).data("bootstrap.table", i = new a.BootstrapTable(f, j))
        }), "undefined" == typeof g ? this : g
    }, a.fn.bootstrapTable.Constructor = Gh, a.fn.bootstrapTable.theme = qh.THEME, a.fn.bootstrapTable.VERSION = qh.VERSION, a.fn.bootstrapTable.defaults = Gh.DEFAULTS, a.fn.bootstrapTable.columnDefaults = Gh.COLUMN_DEFAULTS, a.fn.bootstrapTable.events = Gh.EVENTS, a.fn.bootstrapTable.locales = Gh.LOCALES, a.fn.bootstrapTable.methods = Gh.METHODS, a.fn.bootstrapTable.utils = Ch, a(function () {
        a('[data-toggle="table"]').bootstrapTable()
    }), Gh
}), TABLE_EVENTS = "all.bs.table click-cell.bs.table dbl-click-cell.bs.table click-row.bs.table dbl-click-row.bs.table sort.bs.table check.bs.table uncheck.bs.table onUncheck check-all.bs.table uncheck-all.bs.table check-some.bs.table uncheck-some.bs.table load-success.bs.table load-error.bs.table column-switch.bs.table page-change.bs.table search.bs.table toggle.bs.table show-search.bs.table expand-row.bs.table collapse-row.bs.table refresh-options.bs.table reset-view.bs.table refresh.bs.table", firstLoadTable = [], union = function (a, b) {
    return $.isPlainObject(b) ? addRememberRow(a, b) : $.isArray(b) ? $.each(b, function (b, c) {
        $.isPlainObject(c) ? addRememberRow(a, c) : -1 == $.inArray(c, a) && (a[a.length] = c)
    }) : -1 == $.inArray(b, a) && (a[a.length] = b), a
}, difference = function (a, b) {
    if ($.isPlainObject(b)) removeRememberRow(a, b); else if ($.isArray(b)) $.each(b, function (b, c) {
        if ($.isPlainObject(c)) removeRememberRow(a, c); else {
            var d = $.inArray(c, a);
            -1 != d && a.splice(d, 1)
        }
    }); else {
        var c = $.inArray(b, a);
        -1 != c && a.splice(c, 1)
    }
    return a
}, _ = {union: union, difference: difference};