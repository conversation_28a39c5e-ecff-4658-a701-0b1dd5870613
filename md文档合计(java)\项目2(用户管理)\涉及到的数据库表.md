# 项目2(用户管理) - 涉及到的数据库表

## 概述
项目2(用户管理)主要实现玩家统计功能，**SQL Server数据库在本项目中占据核心地位**，承载了所有的玩家数据和统计逻辑，而MySQL数据库仅用于权限控制。

## 数据库架构分布

### SQL Server数据库 (主要数据源 - 95%的业务逻辑)
**数据库名**: `lin2db`
**服务器**: `110.42.3.94:1433`
**作用**: 游戏玩家数据的核心存储，包含所有玩家账户信息、登录状态、注册信息等

### MySQL数据库 (辅助数据源 - 5%的业务逻辑)
**数据库名**: `jeethink`
**作用**: 仅用于系统用户权限控制和菜单管理

## SQL Server核心数据表详解

### 1. user_account表 (核心玩家账户表)

**表名**: `user_account`
**表用途**: 存储所有游戏玩家的账户信息，是整个玩家统计系统的核心数据表

**完整字段结构**:

| 字段名 | 数据类型 | 说明 | 业务重要性 | 在统计中的作用 |
|--------|----------|------|------------|----------------|
| `uid` | int | 用户唯一ID | ⭐⭐⭐ | 玩家唯一标识 |
| `account` | varchar(14) | 玩家账户名 | ⭐⭐⭐⭐⭐ | **核心业务字段**，玩家识别主键 |
| `pay_stat` | int | 支付状态 | ⭐⭐ | 玩家消费能力分析 |
| `login_flag` | int | 登录标志 | ⭐⭐ | 账户状态控制 |
| `warn_flag` | int | 警告标志 | ⭐⭐ | 玩家行为管理 |
| `block_flag` | int | 封禁标志 | ⭐⭐⭐ | 账户安全管理 |
| `block_flag2` | int | 封禁标志2 | ⭐⭐ | 多级封禁控制 |
| `block_end_date` | datetime | 封禁结束时间 | ⭐⭐⭐ | 临时封禁管理 |
| `last_login` | datetime | 最后登录时间 | ⭐⭐⭐⭐⭐ | **在线状态核心字段** |
| `last_logout` | datetime | 最后登出时间 | ⭐⭐⭐⭐⭐ | **在线状态核心字段** |
| `subscription_flag` | int | 订阅标志 | ⭐⭐ | 玩家活跃度分析 |
| `last_world` | int | 最后游戏世界 | ⭐⭐ | 玩家分布统计 |
| `last_game` | int | 最后游戏 | ⭐⭐ | 游戏偏好分析 |
| `last_ip` | varchar(15) | 最后登录IP | ⭐⭐⭐⭐ | **重要查询条件**，地域分析 |
| `telephone` | varchar(20) | 电话号码 | ⭐ | 联系方式 |
| `invitation` | varchar(10) | 邀请码 | ⭐⭐⭐⭐⭐ | **权限控制核心字段** |

**核心业务逻辑**:
```sql
-- 在线状态判断 (系统最重要的业务逻辑)
CASE
    WHEN last_login > last_logout OR last_logout IS NULL THEN '在线'
    ELSE '离线'
END as onlineStatus
```

**数据特性**:
- **数据量**: 包含所有游戏玩家数据，是系统最大的数据表
- **更新频率**: 高频更新（每次玩家登录/登出都会更新）
- **查询频率**: 极高（所有统计功能都依赖此表）
- **大小写处理**: `invitation`字段使用`UPPER()`函数忽略大小写

### 2. ssn表 (玩家注册信息表)

**表名**: `ssn`
**表用途**: 存储玩家注册时的详细信息，用于注册统计和玩家档案管理

**关键字段详情**:

| 字段名 | 数据类型 | 说明 | 业务重要性 | 在统计中的作用 |
|--------|----------|------|------------|----------------|
| `ssn` | varchar(13) | 社会安全号 | ⭐⭐ | 玩家身份验证 |
| `name` | varchar(15) | 用户名 | ⭐⭐⭐⭐⭐ | **关联user_account.account** |
| `email` | varchar(50) | 邮箱地址 | ⭐⭐⭐ | 联系方式，找回密码 |
| `newsletter` | int | 新闻订阅 | ⭐ | 营销分析 |
| `job` | int | 职业 | ⭐ | 玩家画像分析 |
| `phone` | varchar(20) | 电话 | ⭐⭐ | 联系方式 |
| `mobile` | varchar(20) | 手机 | ⭐⭐ | 主要联系方式 |
| `reg_date` | datetime | 注册时间 | ⭐⭐⭐⭐⭐ | **今日注册统计核心字段** |
| `zip` | varchar(10) | 邮编 | ⭐ | 地域分析 |
| `addr_main` | varchar(100) | 主要地址 | ⭐ | 地域分析 |
| `addr_etc` | varchar(100) | 其他地址 | ⭐ | 补充地址信息 |
| `account_num` | int | 账户数量 | ⭐⭐ | 多账户管理 |
| `status_flag` | int | 状态标志 | ⭐⭐ | 账户状态管理 |
| `final_news_date` | datetime | 最终新闻日期 | ⭐ | 活跃度分析 |
| `master` | varchar(15) | 主账户 | ⭐⭐ | 账户关联管理 |
| `valid_email_date` | datetime | 邮箱验证日期 | ⭐⭐ | 账户安全 |
| `final_master_date` | datetime | 最终主账户日期 | ⭐ | 账户变更记录 |

**核心统计逻辑**:
```sql
-- 今日注册统计 (重要的业务指标)
SELECT COUNT(*) FROM ssn
WHERE CAST(reg_date AS DATE) = CAST(GETDATE() AS DATE)
```

## 表关联关系

### 主要关联
```sql
-- 核心关联关系
user_account.account = ssn.name
```

**关联说明**:
- `user_account`表通过`account`字段与`ssn`表的`name`字段关联
- 这个关联用于获取玩家的注册时间信息
- 支持LEFT JOIN，允许存在只有账户信息但没有注册详情的数据

### 权限控制关联
```sql
-- 与MySQL数据库的权限关联
user_account.invitation = mysql.user.invite (大小写不敏感)
```

## MySQL辅助数据表

### sys_user表 (权限控制表)
**表名**: `sys_user`
**数据库**: MySQL `jeethink`
**作用**: 仅用于权限控制，确定用户能查看哪些玩家数据

**关键字段**:
| 字段名 | 说明 | 作用 |
|--------|------|------|
| `login_name` | 登录名 | admin用户可查看全部数据 |
| `invite` | 邀请码 | 关联SQL Server的invitation字段 |

## 核心SQL查询模式

### 1. 玩家列表查询 (最复杂的业务查询)
```sql
SELECT ua.uid, ua.account, ua.pay_stat, ua.login_flag, ua.warn_flag,
       ua.block_flag, ua.block_flag2, ua.block_end_date, ua.last_login,
       ua.last_logout, ua.subscription_flag, ua.last_world, ua.last_game,
       ua.last_ip, ua.telephone, ua.invitation,
       CASE
           WHEN ua.last_login > ua.last_logout OR ua.last_logout IS NULL THEN '在线'
           ELSE '离线'
       END as onlineStatus,
       s.reg_date
FROM user_account ua
LEFT JOIN ssn s ON ua.account = s.name
WHERE 1=1
  AND (ua.account LIKE ? OR ? IS NULL)
  AND (UPPER(ua.invitation) = UPPER(?) OR ? IS NULL)
  AND (ua.last_ip LIKE ? OR ? IS NULL)
ORDER BY ua.last_login DESC
```

### 2. 在线玩家统计 (实时业务指标)
```sql
SELECT COUNT(*)
FROM user_account
WHERE (last_login > last_logout OR last_logout IS NULL)
  AND last_login IS NOT NULL
  AND (UPPER(invitation) = UPPER(?) OR ? IS NULL)
```

### 3. 今日注册统计 (重要业务指标)
```sql
SELECT COUNT(*)
FROM ssn s
INNER JOIN user_account ua ON s.name = ua.account
WHERE CAST(s.reg_date AS DATE) = CAST(GETDATE() AS DATE)
  AND (UPPER(ua.invitation) = UPPER(?) OR ? IS NULL)
```

## 数据访问技术架构

### JdbcTemplate实现
由于SQL Server数据库的重要性，我们使用JdbcTemplate直接访问，避免配置冲突：

```java
@Repository
public class PlayerDao {
    @Autowired
    @Qualifier("sqlServerJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    // 自定义RowMapper处理复杂的字段映射
    private final RowMapper<UserAccount> userAccountRowMapper = ...;
}
```

### 数据源配置
```java
@Bean("lin2dbDataSource")
public DataSource lin2dbDataSource() {
    DruidDataSource dataSource = new DruidDataSource();
    dataSource.setUrl("*****************************************************;...");
    // SQL Server专用配置
}
```

## 性能优化建议

### 关键索引
```sql
-- 建议在SQL Server中创建的索引
CREATE INDEX IX_user_account_invitation ON user_account(invitation);
CREATE INDEX IX_user_account_login_times ON user_account(last_login, last_logout);
CREATE INDEX IX_user_account_last_login ON user_account(last_login DESC);
CREATE INDEX IX_ssn_name ON ssn(name);
CREATE INDEX IX_ssn_reg_date ON ssn(reg_date);
```

### 查询优化
- **分页查询**: 使用`ORDER BY last_login DESC`确保最新活跃玩家优先显示
- **时间范围查询**: 使用`CAST(reg_date AS DATE)`进行日期比较
- **大小写处理**: 统一使用`UPPER()`函数处理邀请码比较

## 业务数据流转

### 1. 玩家登录流程
```
玩家登录游戏 → 更新user_account.last_login → 在线状态变为"在线"
```

### 2. 玩家登出流程
```
玩家退出游戏 → 更新user_account.last_logout → 在线状态变为"离线"
```

### 3. 统计数据生成
```
前端请求统计 → JdbcTemplate查询SQL Server → 计算在线/注册数量 → 返回统计结果
```

## 数据安全和权限

### 权限控制机制
1. **MySQL权限表**: 确定用户身份和权限范围
2. **SQL Server数据过滤**: 根据invitation字段过滤可见数据
3. **大小写不敏感**: 使用UPPER()函数确保邀请码匹配的一致性

### 数据隔离
- **Admin用户**: 可查看所有SQL Server中的玩家数据
- **普通用户**: 只能查看invitation字段匹配自己invite的玩家数据

## 总结

在项目2(用户管理)中，**SQL Server数据库lin2db是绝对的核心**：

- 🎯 **数据量占比**: 95%以上的业务数据存储在SQL Server中
- 🎯 **查询频率**: 99%的查询操作都针对SQL Server数据库
- 🎯 **业务逻辑**: 所有核心统计逻辑都基于SQL Server数据
- 🎯 **实时性要求**: SQL Server数据的实时性直接影响统计准确性

MySQL数据库仅作为权限控制的辅助，真正的业务价值和数据洞察都来自于SQL Server数据库中丰富的玩家行为数据。

### 核心数据表总结
1. **user_account表** (SQL Server) - 玩家账户核心表，包含16个关键字段
2. **ssn表** (SQL Server) - 玩家注册信息表，包含16个关键字段
3. **sys_user表** (MySQL) - 权限控制辅助表，包含2个关键字段

系统设计突出了SQL Server数据库的核心地位，通过JdbcTemplate实现高效的数据访问，确保了玩家统计功能的稳定性和性能。
