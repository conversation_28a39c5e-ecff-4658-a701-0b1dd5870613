﻿@charset "utf-8";

@font-face {
    font-family: 'games';
    src: url('game.eot');
    src: url('game.eot?#iefix') format('embedded-opentype'), url('game.woff') format('woff'), url('game.ttf') format('truetype'), url('game.svg#webfont') format('svg');
}

.con-frame {
    width: 450px;
    margin: 0 auto;
    background: #FFFFFF;
    color: #333333;
    margin-top: 20px;
    border-radius: 10px;
}

.con-frame-box {
    overflow: hidden;
    padding: 0 70px;
}

.login-form .form-group {
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 20px;
}

.con-frame-box .login-form .form-control {
    height: 40px;
    background: #FFFFFF;
    border: 1px solid #C0C0C0;
    opacity: 0.8;
    border-radius: 6px;
}

.form-tip {
    position: relative;
    margin-top: 10px;
    width: 100%;
    height: 50px;
    line-height: 50px;
    background: #272E3B;
    color: #fff;
    font-size: 14px;
    text-align: center;
}

    .form-tip:before {
        position: absolute;
        top: 3px;
        right: 124px;
        content: "";
        width: 16px;
        height: 10px;
        border-top: 1px solid #272E3B;
        border-right: 1px solid #272E3B;
        background-color: #272E3B;
        -webkit-transform: translate(-50%,-50%) rotate(-45deg);
        transform: translate(-50%,-50%) rotate(-45deg);
        -webkit-transform-origin: center;
        transform-origin: center;
    }

.pt-10 {
    padding-top: 10px;
}

.login-form .c-button, .btn-achieve {
    width: 100%;
    font-size: 16px;
}

.c-button {
    color: #FFFFFF;
    width: 100%;
    height: 50px;
    background: #1A85FF;
    border-radius: 6px;
}

.explain {
    display: block;
    clear: left;
    padding-top: 6px;
    padding-left: 2px;
    color: #333333;
}

.con-frame-box .form-control::-webkit-input-placeholder {
    color: #999999;
}

.con-frame-box .form-control:-moz-placeholder {
    color: #999999;
}

.con-frame-box .form-control::-moz-placeholder {
    color: #999999;
}

.con-frame-box .form-control:-ms-input-placeholder {
    color: #999999;
}

.btn-vcode {
    margin-left: 10px;
    width: 100px;
    background-color: #1A85FF;
    height: 40px;
    border-color: #00AAFF;
    color: #FFFFFF;
    border-radius: 5px;
}

.btn-achieve {
    height: 40px;
    color: #fff;
    background: #3FAFFF;
    border-radius: 0;
    border: none;
}
/*注册协议*/
.cloud {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 10;
    top: 0;
    left: 0;
    background: rgba(0,0,0,0.5);
}

.register-pop {
    position: fixed;
    z-index: 99;
    top: 40%;
    left: 38%;
    width: 870px;
    height: 600px;
    margin-top: -240px;
    margin-left: -300px;
    padding: 20px 30px;
    background: #fff;
}

.pop-hd {
    line-height: 1;
    padding-bottom: 10px;
    border-bottom: 1px solid #e1e1e1;
    font-size: 14px;
    color: #333;
}

.pop-close {
    float: right;
    width: 18px;
    height: 18px;
    line-height: 16px;
    font-size: 18px;
    color: #ccc;
    font-weight: bold;
    text-align: center;
    z-index: 100;
}

.pop-hd h3 {
    font-size: 16px;
    margin: 0;
    font-weight: bold;
}

.pop-bd {
    overflow: hidden;
    padding: 20px 0 0;
    height: 480px;
}

.clause-box {
    padding-bottom: 10px;
    padding-right: 1px;
    margin-bottom: 10px;
    height: 350px;
    font-size: 14px;
    color: #333;
    line-height: 24px;
    overflow-y: scroll;
}

    .clause-box::-webkit-scrollbar {
        width: 8px;
        background-color: transparent;
    }

    .clause-box::-webkit-scrollbar-track {
        border-radius: 8px;
        background-color: transparent;
    }

    .clause-box::-webkit-scrollbar-thumb {
        border-radius: 8px;
        background-color: #ccc;
    }

.sc-con {
    margin: 5px 0 10px 0;
    line-height: 1.5;
}

.pop-footer {
    padding: 30px 0;
    text-align: center;
}

    .pop-footer a {
        border-radius: 4px;
        clear: both;
        width: 160px;
        margin: 0 15px;
        display: inline-block;
        height: 40px;
        line-height: 40px;
        border: 1px solid #ccc;
    }

.reg_btn_disabled {
    color: #999;
    background: #fff;
    cursor: pointer;
}

.reg_btn {
    background-color: #1A85FF;
    border-color: #1A85FF;
    color: #fff;
}
    .reg_btn:hover {
        color: #fff;
    }

.pull-left {
    float: left !important;
}

.pull-right {
    float: right !important;
}





@media (max-width: 767px) {
    .login-logo img {
        width: 100%;
        height: auto;
    }

    .con-frame-box {
        padding: 0 15px;
    }

    .login-logo {
        padding: 15px 0 30px;
    }

    .con-frame {
        padding: 15px 0 0;
        width: 100%;
    }

    .register-pop {
        height: auto;
        top: 0;
        width: 100%;
        margin: 0;
        left: 0;
    }

    .pop-footer {
        padding: 10px 0 0 0;
    }

        .pop-footer a {
            margin-bottom: 5px;
        }
}

@media (max-width: 767px) {
    .modal-content, #imgQuestion {
        width: 100% !important;
    }
}


ul {
    list-style-type: none;
    padding: 0;
}

em {
    font-style: normal;
}

label {
    font-weight: normal;
}

.font-ico {
    font-family: 'games';
}

.icon {
    display: inline-block;
    vertical-align: top;
    zoom: 1;
    *zoom: 1;
}

input {
    background-color: transparent;
}

.navbar-compact {
    background-color: rgba(27,96,145,0.9);
    border-top: 1px solid #0E85C4;
    border-bottom: 1px solid #0E85C4;
}

.navbar {
    min-height: 80px;
}

.navbar-tip {
    padding-top: 22px;
}

    .navbar-tip p {
        margin-bottom: 0;
        line-height: 20px;
        font-size: 12px;
    }

.navbar-label {
    font-size: 14px;
}

.navbar-brand {
    height: auto;
    padding: 22px 10px 0 0;
}

    .navbar-brand img {
        width: 276px;
        height: 46px;
    }

.navbar-label .icon-down {
    padding-left: 8px;
    font-size: 28px;
}

    .navbar-label .icon-down:before {
        content: "\002C";
    }

    .navbar-label .icon-down.cur:before {
        content: "\002D";
    }

.navbar-label {
    position: relative;
    line-height: 78px;
    cursor: pointer;
}

.navbar-quit:before, .form-tip:before {
    -webkit-transform: translate(-50%,-50%) rotate(-45deg);
    transform: translate(-50%,-50%) rotate(-45deg);
    -webkit-transform-origin: center;
    transform-origin: center;
}

.navbar-quit:before {
    position: absolute;
    right: 48px;
    content: "";
    width: 10px;
    height: 10px;
    border-top: 1px solid #0E85C4;
    border-right: 1px solid #0E85C4;
    background-color: #0066ff;
    opacity: .95;
}

.form-tip:before {
    position: absolute;
    top: 3px;
    right: 124px;
    content: "";
    width: 16px;
    height: 10px;
    border-top: 1px solid #1A6BA3;
    border-right: 1px solid #1A6BA3;
    background-color: #1A6BA3;
}

.navbar-quit {
    width: 216px;
    height: 60px;
    line-height: 60px;
    position: absolute;
    top: 79px;
    left: -46px;
    text-align: center;
    background: #0066ff;
    border-bottom: 1px solid #0066ff;
    border-left: 1px solid #0066ff;
    border-right: 1px solid #0066ff;
    color: #999999;
}

    .navbar-quit .font-ico {
        margin-right: 20px;
        font-size: 20px;
    }

.flex_cont {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: space-between;
    justify-content: space-between;
}

.c-flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.mobile-logo {
    width: 200px;
}

.side-menu {
    display: none;
    position: fixed;
    max-width: 240px;
    padding-right: 0;
    top: 0;
    left: auto;
    right: 0;
    bottom: 0;
    z-index: 3;
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
}

.main-container {
    padding: 80px 20px 0;
}

.navbar-collapse {
    padding-left: 0;
}
/*左侧菜单*/
.sidebar {
    background: #FFFFFF;
}

    .sidebar ul li {
        width: 100%;
        height: 84px;
        line-height: 84px;
    }

        .sidebar ul li a {
            display: block;
            height: 100%;
            margin: 0 30px 0 25px;
            font-weight: 400;
            font-size: 20px;
            color: #333333;
        }

            .sidebar ul li a .font-ico {
                margin: 0 10px 0 16px;
                vertical-align: top;
                color: #00ABF9;
            }

        .sidebar ul li span {
            margin-left: 15px;
            color: #FFFFFF;
        }

        .sidebar ul li:last-child a {
            border-bottom: none;
        }

        .sidebar ul li.cur {
            background: #1A85FF;
            border-radius: 5px;
        }

            .sidebar ul li.cur a, .sidebar ul li.cur a .font-ico {
                color: #FFFFFF;
            }

.font-24 {
    font-size: 24px;
}

.con-wrapper .box {
    border-top: 1px solid hsla(0,0%,100%,.1);
    background-color: #FFFFFF;
    border-radius: 5px;
}

.con-wrapper .box-title {
    margin-left: 10px;
    margin-right: 10px;
    overflow: hidden;
    line-height: 1;
    padding: 20px;
    border-bottom: 1px solid #CCCCCC;
}

.subtitle {
    font-size: 20px;
    font-weight: bold;
}

.con-wrapper .box-content {
    padding: 20px 20px 6px 20px;
}
/*.form-group{overflow: hidden;}*/
.form-group .pl-0 {
    color: #333333;
}

.pl-0 {
    padding-left: 0;
}

.pb-10 {
    padding-bottom: 10px;
}

.mt-30 {
    margin-top: 15px;
}

.mt-68 {
    margin-top: 68px;
}

.mb-15 {
    margin-bottom: 15px;
}

.mb-20 {
    margin-bottom: 20px;
}

.mb-26 {
    margin-bottom: 26px;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.ml-40 {
    margin-left: 40px;
}

.font-48 {
    font-size: 48px;
}

.con-wrapper .box-content .form-group {
    margin-bottom: 24px;
}

.account-pass em {
    margin-right: 20px;
    width: 20px;
    height: 20px;
}

.account-pass p {
    margin-bottom: 18px;
}

.icon-face {
    background: url(../img/icon-face.png?v=111) no-repeat center center;
    background-size: 100% 100%;
}

.icon-unface {
    background: url(../img/icon-unface.png) no-repeat center center;
    background-size: 100% 100%;
}

.con-wrapper .box-content .table {
    margin-top: 10px;
}

    .con-wrapper .box-content .table > thead > tr > th {
        border-bottom: none;
        font-weight: normal;
        text-align: center;
        /*background: -webkit-linear-gradient(top, #159ADC 0%, #0288CB 100%); 
background: -moz-linear-gradient(top, #159ADC 0%, #0288CB 100%); 
background: -o-linear-gradient(top, #159ADC 0%, #0288CB 100%); 
background: -ms-linear-gradient(top, #159ADC 0%, #0288CB 100%); 
background: linear-gradient(top, #159ADC 0%, #0288CB 100%); */
        color: #333333;
    }

    .con-wrapper .box-content .table > tbody > tr > td {
        border-top: none;
        font-size: 14px;
        text-align: center;
        padding-top: 16px;
    }

.con-wrapper .box-content .con-form .form-group {
    margin-bottom: 20px;
}

.con-list span {
    overflow: hidden;
    display: inline-block;
    margin-bottom: 20px;
    width: 32%;
    height: 40px;
    line-height: 40px;
    border: 1px solid #1A85FF;
    text-align: center;
    font-size: 14px;
    color: #1A85FF;
    cursor: pointer;
    border-radius: 5px;
}

    .con-list span.cur {
        background: #1A85FF;
        color: #fff;
    }

.con-form .con-other .form-control {
    text-align: center;
    color: #1A85FF;
    border-color: #1A85FF;
    border-radius: 5px;
    height: 40px;
}

    .con-list-style span {
        width: 49%;
    }

.con-list-style .icon {
    margin-right: 10px;
    font-size: 20px;
}

.btn-group-note {
    width: 250px;
}


.btn-group-code {
    margin-left: 10px !important;
    width: 130px;
}

.text-divide {
    padding: 0 24px 0 30px;
}

.text-strength em {
    display: inline-block;
    margin-right: 8px;
    width: 10px;
    height: 10px;
    border: 1px solid #00AFFF;
    background: #1A85FF;
}

    .text-strength em.cur {
        background: #1A85FF;
        border-color: #1A85FF;
    }

.help-inline {
    display: inline-block;
    line-height: 40px;
    font-size: 14px;
    color: #999;
}
/*登录注册*/
.wrap {
    height: 100%;
    background: #202020 no-repeat center top;
}

.banner {
    overflow: hidden;
    width: 100%;
    height: 100%;
    background: url(../img/bg-login.jpg) no-repeat center center;
}

.login-box {
    overflow: hidden;
    position: relative;
    width: 500px;
    margin: 8% auto 0;
    background: #21252D;
}

.login-form .form-control {
    background-color: #171A1F;
}

.login-form .btn-vcode {
    margin-left: 10px;
    width: 100px;
    background-color: #1A85FF;
}

.login-form .c-button {
    font-size: 16px;
}

.login-form .form-group {
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 20px;
}

.c-gay {
    color: #999;
}

.register-box {
    margin-top: 16.5%;
}

/*注册7.23*/
.mt-8 {
    margin-top: 8px;
}

.pt-10 {
    padding-top: 10px;
}

.pt-26 {
    padding-top: 26px;
}

    .con-frame-box h2 {
        font-size: 32px;
        padding: 36px 0 32px;
    }

.con-frame-box .form-control::-webkit-input-placeholder {
    color: #999999;
}

.con-frame-box .form-control:-moz-placeholder {
    color: #999999;
}

.con-frame-box .form-control::-moz-placeholder {
    color: #999999;
}

.con-frame-box .form-control:-ms-input-placeholder {
    color: #999999;
}

.con-other input::-webkit-input-placeholder {
    color: #1A85FF;
}

.con-other input:-moz-placeholder {
    color: #1A85FF;
}

.con-other input::-moz-placeholder {
    color: #1A85FF;
}

.con-other input:-ms-input-placeholder {
    color: #1A85FF;
}

.form-tip {
    position: relative;
    margin-top: 10px;
    width: 100%;
    height: 50px;
    line-height: 50px;
    background: #1A6BA3;
    color: #fff;
    font-size: 14px;
    text-align: center;
}

.con-frame-box .login-form .btn-vcode {
    background-color: #1A85FF;
}

.photo-box {
    overflow: hidden;
    padding: 16px 0;
    width: 256px;
    height: 256px;
    background: #fff;
    text-align: center;
}

.payment h3 {
    margin-bottom: 48px;
}

.photo-box img {
    height: 100%;
}

.payment-right h3 {
    width: 256px;
}

.form-group .text-blue {
    color: #00afff;
}

.payment .form-group {
    margin-bottom: 20px !important;
}

.payment {
    padding-bottom: 100px;
}

.btn-handle {
    overflow: hidden;
    padding: 50px 0 0;
}

    .btn-handle a {
        display: inline-block;
        margin-right: 50px;
        margin-bottom: 36px;
        width: 250px;
        height: 42px;
        line-height: 42px;
        background: #0E86CA;
        color: #fff;
    }

        .btn-handle a.btn-up {
            background: #0C1C2E;
        }
/*充值状态*/
.status {
    text-align: center;
    margin: 19% 0;
}

    .status dt img {
        width: 80px;
        height: 80px;
        margin-bottom: 25px;
    }

    .status dd p {
        margin-bottom: 56px;
    }

    .status .btn {
        padding: 0;
        width: 250px;
        height: 50px;
        line-height: 50px;
        margin-bottom: 46px;
        font-size: 24px;
        text-align: center;
        border-radius: 4px;
        color: #fff;
    }

.btn_complete {
    background: #07B1FD;
}
/*8.29修改*/
.add-tip {
    position: relative;
    margin-top: 8px;
    height: 32px;
    line-height: 32px;
    background: lightgray;
    font-size: 14px;
    border-radius: 5px;
}

    .add-tip img {
        margin: 6px 5px 5px 5px;
        width: 20px;
        height: 20px;
    }

    .add-tip:before {
        position: absolute;
        top: 3px;
        right: 124px;
        content: "";
        width: 16px;
        height: 10px;
        border-top: 1px solid lightgray;
        border-right: 1px solid lightgray;
        background-color: lightgray;
    }

    .add-tip:before {
        -webkit-transform: translate(-50%,-50%) rotate(-45deg);
        transform: translate(-50%,-50%) rotate(-45deg);
        -webkit-transform-origin: center;
        transform-origin: center;
    }
/*订单提交等待支付*/
.con-wrapper .box.pay-confirm {
    position: relative;
    background: rgba(0,0,0,0.4);
}

.cloud {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 10;
    top: 0;
    left: 0;
    background: rgba(0,0,0,0.4);
}

.wait {
    position: fixed;
    z-index: 101;
    top: 50%;
    left: 50%;
    /*position:relative;*/
    padding: 20px 20px 30px 20px;
    /*margin:40px auto 60px;*/
    margin: -238px 0 0 -325px;
    width: 650px;
    height: 476px;
    background: #fff;
    color: #fff;
}

.wait-middle {
    overflow: hidden;
    height: 100%;
    background: #CCCCCC;
    font-size: 16px;
}

.w-blue {
    color: #1A85FF;
}

.pt-10 {
    padding-right: 10px;
}

.wait-quick {
    padding: 30px 0 15px;
    line-height: 1;
    color: #1A85FF;
}

.wait-scan {
    padding-top: 10px;
    background: url(../img/bg-box.png) no-repeat center center #fff;
    width: 180px;
    height: 180px;
    margin: 16px auto 0;
    text-align: center;
}

    .wait-scan img {
        max-width: 160px;
        max-height: 160px;
    }

.btn-scan {
    margin: 20px auto 0;
    background: url(../img/btn-scan.png) no-repeat center center;
    width: 300px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
}
/*分页*/
.btn-pages {
    display: table;
    margin: 0 auto;
    width: auto;
    padding-bottom: 30px;
    text-align: center;
    overflow: hidden;
}

    .btn-pages .page_btn, .btn-pages .page_num, .btn-pages .btn_page_got {
        float: left;
        height: 30px;
        line-height: 30px;
        border-radius: 5px;
        border: 1px solid #CCCCCC;
        background-color: #fff;
        padding: 0 10px;
        margin-right: 20px;
        color: #333;
        *display: inline;
        *zoom: 1;
    }

        .btn-pages .page_btn.cur, .btn-pages .cur.page_num, .btn-pages .cur.btn_page_got {
            background-color: #00AFFF;
            color: #fff;
            border-color: #CCCCCC;
        }

    .btn-pages .page_num {
        width: 30px;
        padding: 0;
    }

    .btn-pages .pages_all {
        float: left;
        line-height: 30px;
    }

        .btn-pages .pages_all .num {
            display: inline-block;
            margin: 0 20px 0 20px;
        }

        .btn-pages .pages_all .input {
            border: 1px solid #ccc;
            border-radius: 5px;
            display: inline-block;
            width: 30px;
            height: 30px;
            margin: 0 10px;
            text-align: center;
            padding: 0;
            background: #fff;
            color: #333;
            outline: none;
        }

    .btn-pages .btn_page_got {
        background-color: #fff;
        border-color: #ccc;
        color: #333;
    }

.sidebar ul li a .font-wallet {
    background: url(../img/icon-wallet-cur.png) no-repeat center center;
    width: 22px;
    height: 22px;
    margin-top: 31px !important;
}

.sidebar ul li.cur a .font-wallet {
    background: url(../img/icon-wallet.png) no-repeat center center;
}
/*我的钱包*/
.wallet-list .item {
    width: 280px;
    position: relative;
    float: left;
    padding: 0;
    margin-bottom: 20px;
    margin-left: 10px;
    color: #333333;
    border-radius: 5px;
    border: 1px solid #CCCCCC;
}

.wallet-list dl {
    padding: 10px 0 10px 10px;
    border-bottom: 1px dashed #9EAEBF;
}

.wallet-list dt {
    overflow: hidden;
    font-weight: normal;
/*    margin-bottom: 20px;*/
}

    .wallet-list dt span {
        vertical-align: middle;
    }

.bank-photo {
    float: left;
    overflow: hidden;
    border-radius: 100%;
    margin-right: 5px;
}

.wallet-list dd {
    line-height: 1;
}

.wallet-person {
    padding-left: 5px;
    height: 28px;
}

    .wallet-person span:first-child {
        padding-right: 20px;
    }

.wallet-list .item:last-child {
    height: 130px;
    cursor: pointer;
}

    .wallet-list .item:last-child img {
/*        width: 20px;
        height: 20px;*/
        margin: 20px 0 20px;
    }

.icon-closed {
    display: none;
    position: absolute;
    right: 5px;
    top: 5px;
    background: url(../img/icon-closed.png) no-repeat center center;
    width: 20px;
    height: 20px;
}

.funds {
    color: #1A85FF;
}

    .funds em {
        font-size: 12px;
    }

i {
    font-style: normal;
}

.btn-next {
    width: 360px;
    border-radius: 0;
}

.con-form .form-control.commission {
    margin-bottom: 20px;
    padding: 0 0 0 20px;
    line-height: 32px;
    text-align: center;
}

    .con-form .form-control.commission img {
        display: none;
        position: absolute;
        bottom: 0;
        right: 0;
    }

.p-double {
    padding: 0;
}

.icon-mark {
    margin: 13px 20px 0 0;
    background: url(../img/icon-bank-circle.png) no-repeat center center;
    width: 14px;
    height: 14px;
}

.con-form .form-control.commission.cur {
    color: #1A85FF;
    border: 1px solid #1A85FF;
    position: relative;
    text-align: center;
}

.con-form .form-control.commission.cur img {
    display: block;
    position: absolute;
    bottom: 0;
    right: 0;
}

.pl-20 {
    padding-left: 20px;
}

.in-box {
    position: relative;
}

    .in-box .help-inline {
        position: absolute;
        top: 0;
        left: 130px;
        color: #fff;
    }

.btn-pages .omit {
    background: transparent;
    border: none;
    color: #fff;
}
/*审核状态*/
.condition {
    display: none;
    overflow: visible;
    position: fixed;
    z-index: 9;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    width: 478px;
    padding: 10px;
    height: 327px;
    background: #00396D;
    box-sizing: border-box;
}

    .condition:before, .condition:after {
        position: absolute;
        top: 10px;
        content: "";
        display: inline-block;
        width: 23px;
        height: 307px;
    }

    .condition:before {
        left: 10px;
        background: url(../img/btn-left.png) no-repeat left;
    }

    .condition:after {
        right: 10px;
        background: url(../img/btn-right.png) no-repeat right;
    }

.condition-header {
    margin: 0 auto;
    background: url(../img/btn-top.png) no-repeat center center;
    width: 402px;
    height: 40px;
    line-height: 40px;
    color: #fff;
}

.condition-content {
    position: relative;
    padding-top: 26px;
    margin: 0 auto;
    width: 402px;
    height: 218px;
    border: 2px solid #00AFFF;
    border-bottom: none;
    color: #fff;
    background: #004D84;
}

    .condition-content dt {
        margin: 26px 20px 0 20px;
        overflow: hidden;
        width: 50px;
        height: 50px;
    }

.condition-footer {
    margin: 0 auto;
    background: url(../img/btn-bottom.png) no-repeat center center;
    width: 402px;
    height: 39px;
}

.con-sure {
    overflow: hidden;
    margin: 0 auto;
    background: url(../img/btn-sure.png) no-repeat center center;
    width: 163px;
    height: 42px;
    line-height: 42px;
    cursor: pointer;
}

#point {
    max-height: 500px;
    overflow-y: scroll;
}


@-webkit-keyframes Navbar-site-menu-in {
    0% {
        -webkit-transform: translateX(-100%);
        transform: translateX(-100%)
    }

    to {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@keyframes Navbar-site-menu-in {
    0% {
        -webkit-transform: translateX(-100%);
        transform: translateX(-100%)
    }

    to {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@-webkit-keyframes Navbar-site-menu-out {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    to {
        -webkit-transform: translateX(-100%);
        transform: translateX(-100%)
    }
}

@keyframes Navbar-site-menu-out {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    to {
        -webkit-transform: translateX(-100%);
        transform: translateX(-100%)
    }
}

@-webkit-keyframes Navbar-account-menu-in {
    0% {
        -webkit-transform: translateX(100%);
        transform: translateX(100%)
    }

    to {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@keyframes Navbar-account-menu-in {
    0% {
        -webkit-transform: translateX(100%);
        transform: translateX(100%)
    }

    to {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@-webkit-keyframes Navbar-account-menu-out {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    to {
        -webkit-transform: translateX(100%);
        transform: translateX(100%)
    }
}

@keyframes Navbar-account-menu-out {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    to {
        -webkit-transform: translateX(100%);
        transform: translateX(100%)
    }
}

.hide {
    display: none;
}
/*我的工单,问题反馈*/
.sidebar ul li .icon-order, .sidebar ul li .icon-question, .sidebar ul li .icon-cash, .sidebar ul li .icon-home, .sidebar ul li .icon-rename, .sidebar ul li .icon-charge, .sidebar ul li .icon-safe, .sidebar ul li .icon-bankcard, .sidebar ul li .icon-order, .sidebar ul li .icon-money, .sidebar ul li .icon-rename, .sidebar ul li .icon-transfer, .sidebar ul li .icon-exchange, .sidebar ul li .icon-mine, .icon-sex {
    margin-top: 30px !important;
    width: 25px;
    height: 25px;
}

/**账号预览*/
.sidebar ul li .icon-home {
    background: url(../img/icon_account_menu_home_unselected.png) no-repeat center center;
}

.sidebar ul li.cur .icon-home {
    background: url(../img/icon_account_menu_home_selected.png) no-repeat center center;
}

/**账号详情*/
.sidebar ul li .icon-mine {
    background: url(../img/icon_account_menu_mine_unselected.png) no-repeat center center;
}

.sidebar ul li.cur .icon-mine {
    background: url(../img/icon_account_menu_mine_selected.png) no-repeat center center;
}
/**通行证充值*/
.sidebar ul li .icon-charge {
    background: url(../img/icon_account_menu_charge_unselected.png) no-repeat center center;
}

.sidebar ul li.cur .icon-charge {
    background: url(../img/icon_account_menu_charge_selected.png) no-repeat center center;
}
/**账号安全*/
.sidebar ul li .icon-safe {
    background: url(../img/icon_account_menu_safe_unselected.png) no-repeat center center;
}

.sidebar ul li.cur .icon-safe {
    background: url(../img/icon_account_menu_safe_selected.png) no-repeat center center;
}
/**银行卡管理*/
.sidebar ul li .icon-bankcard {
    background: url(../img/icon_account_menu_bankcard_unselected.png) no-repeat center center;
}

.sidebar ul li.cur .icon-bankcard {
    background: url(../img/icon_account_menu_bankcard_selected.png) no-repeat center center;
}
/**交易记录*/
.sidebar ul li .icon-order {
    background: url(../img/icon_account_menu_order_unselected.png) no-repeat center center;
}

.sidebar ul li.cur .icon-order {
    background: url(../img/icon_account_menu_order_selected.png) no-repeat center center;
}
/**我的资产*/
.sidebar ul li .icon-money {
    background: url(../img/icon_account_menu_money_unselected.png) no-repeat center center;
}

.sidebar ul li.cur .icon-money {
    background: url(../img/icon_account_menu_money_selected.png) no-repeat center center;
}
/**角色改名*/
.sidebar ul li .icon-rename {
    background: url(../img/icon_account_menu_rename_unselected.png) no-repeat center center;
}

.sidebar ul li.cur .icon-rename {
    background: url(../img/icon_account_menu_rename_selected.png) no-repeat center center;
}
/**角色转区*/
.sidebar ul li .icon-transfer {
    background: url(../img/icon_account_menu_transfer_unselected.png) no-repeat center center;
}

.sidebar ul li.cur .icon-transfer {
    background: url(../img/icon_account_menu_transfer_selected.png) no-repeat center center;
}
/**元宝兑换*/
.sidebar ul li .icon-exchange {
    background: url(../img/icon_account_menu_exchange_unselected.png) no-repeat center center;
}

.sidebar ul li.cur .icon-exchange {
    background: url(../img/icon_account_menu_exchange_selected.png) no-repeat center center;
}

/**角色改性*/
.sidebar ul li .icon-sex {
    background: url(../img/icon_account_menu_sex_unselected.png) no-repeat center center;
}

.sidebar ul li.cur .icon-sex {
    background: url(../img/icon_account_menu_sex_selected.png) no-repeat center center;
}



.sidebar ul li .icon-question {
    background: url(../img/icon-question-cur.png) no-repeat center center;
}

.sidebar ul li.cur .icon-question {
    background: url(../img/icon-question.png) no-repeat center center;
}

.sidebar ul li .icon-cash {
    background: url(../img/icon-wallet-cur.png) no-repeat center center;
}

.sidebar ul li.cur .icon-cash {
    background: url(../img/icon-wallet.png) no-repeat center center;
}


.order-title {
    margin-bottom: 30px;
}

    .order-title li {
        position: relative;
        float: left;
        margin-right: 78px;
        width: 218px;
        height: 40px;
        line-height: 40px;
        background: #006DAC;
        color: #fff;
        text-align: center;
    }

        .order-title li.cur {
            background: #00AFFF;
        }

        .order-title li:last-child {
            margin-right: 0;
        }

            .order-title li:first-child::after, .order-title li:nth-child(2):before,
            .order-title li:nth-child(2):after, .order-title li:last-child::before,
            .order-title li:last-child::after {
                position: absolute;
                top: 0;
                content: "";
                display: inline-block;
                width: 20px;
                height: 40px;
            }

        .order-title li:first-child::after {
            right: -20px;
            background: url(../img/img-right.png) no-repeat center center;
        }

        .order-title li.cur:first-child::after {
            background: url(../img/img-right-cur.png) no-repeat center center;
        }

        .order-title li:nth-child(2):before, .order-title li:last-child::before {
            left: -20px;
            background: url(../img/img-left.png) no-repeat center center;
        }

        .order-title li:nth-child(2):after, .order-title li:last-child::after {
            right: -20px;
            background: url(../img/img-right.png) no-repeat center center;
        }

        .order-title li.cur:nth-child(2):before, .order-title li.cur:last-child:before {
            background: url(../img/img-left-cur.png) no-repeat center center;
        }

        .order-title li.cur:nth-child(2):after, .order-title li.cur:last-child::after {
            background: url(../img/img-right-cur.png) no-repeat center center;
        }

.con-wrapper .order-wrap .box-content {
    padding-left: 10px;
    padding-right: 10px;
    padding-bottom: 54px;
    font-size: 14px;
}

.order-infor {
    overflow: hidden;
    padding: 0 20px 17px;
    width: 100%;
    background: #012247;
}

.order-infor-list .title {
    display: block;
    padding: 18px 0 15px;
    line-height: 1;
}

.order-handle {
    overflow: hidden;
}

    .order-handle span {
        margin-right: 98px;
        float: left;
    }

    .order-handle .btn-delete {
        float: right;
        color: #0099e1;
    }

.headline {
    margin-top: 20px;
    padding-left: 20px;
    width: 100%;
    height: 58px;
    line-height: 58px;
    font-size: 20px;
    background: #012247;
}

.order-record-list {
    margin: 20px 20px 0;
    overflow: hidden;
    background: #01528B;
}

    .order-record-list dl {
        margin: 20px 20px 0;
        border-bottom: 1px solid #0081C5;
    }

        .order-record-list dl:last-child {
            border-bottom: none;
        }

        .order-record-list dl dt {
            overflow: hidden;
            float: left;
            font-weight: normal;
            margin-right: 18px;
        }

            .order-record-list dl dt .photo {
                overflow: hidden;
                float: left;
                width: 50px;
                height: 50px;
                border-radius: 100%;
            }

                .order-record-list dl dt .photo img {
                    width: 50px;
                }

        .order-record-list dl dd {
            width: 76%;
            float: left;
            padding-top: 10px;
        }

            .order-record-list dl dd li {
                float: left;
                margin-right: 18px;
                width: 80px;
                height: 80px;
                border: 2px solid #999999;
            }

            .order-record-list dl dd p {
                margin-bottom: 19px;
            }

            .order-record-list dl dd .time {
                padding: 16px 0;
            }

.feedback {
    overflow: hidden;
    margin: 0 20px 50px;
}

    .feedback .title {
        padding-top: 17px;
    }

    .feedback textarea {
        width: 100%;
        height: 80px;
        border: 1px solid #25384F;
        background: #00355B;
        color: #999;
        padding: 6px 10px 6px 20px;
        margin-bottom: 18px;
    }

.box-store {
    width: 120px;
    height: 120px;
    position: relative;
    border: 2px solid #999999;
}

.img-box a, .img-box a img {
    display: block;
    width: 100%;
    height: 100%;
}

.img-box .img-edit img {
    position: absolute;
    z-index: 2;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    margin-top: 0 !important;
}

.data-exchange {
    display: none;
    overflow: hidden;
    background: rgba(0,0,0,.5);
    font-size: 16px;
    color: #fff;
    position: absolute;
    bottom: 0;
    z-index: 4;
    width: 100%;
    height: 24px;
    text-align: center;
}

.img-box input {
    position: absolute;
    top: 0;
    opacity: 0;
    width: 100%;
}
/*问题反馈*/
.question-list {
    margin-left: 26px;
    margin-right: 26px;
}

    .question-list li {
        border-bottom: 1px solid #CCCCCC;
    }

        .question-list li a {
            display: block;
            position: relative;
            padding-bottom: 16px;
            margin-top: 16px;
            color: #fff;
        }

        .question-list li:first-child a {
            margin-top: 0;
        }

        .question-list li a:after {
            content: "";
            position: absolute;
            right: 15px;
            top: 6px;
            width: 10px;
            height: 10px;
            border: 2px solid #FCFCFD;
            border-width: 0 2px 2px 0;
            -webkit-transform: rotate(-45deg);
            transform: rotate(-45deg);
        }

.pb-100 {
    padding-bottom: 100px !important;
}

.pt-30 {
    padding-top: 30px;
}

.con-form .form-textarea {
    height: 80px;
}

.c-white {
    color: #fff;
}

.con-wrapper .order-content .table > thead > tr > th {
    background: -webkit-linear-gradient(top, #159ADC 0%, #0288CB 100%);
    background: -moz-linear-gradient(top, #159ADC 0%, #0288CB 100%);
    background: -o-linear-gradient(top, #159ADC 0%, #0288CB 100%);
    background: -ms-linear-gradient(top, #159ADC 0%, #0288CB 100%);
    background: linear-gradient(top, #159ADC 0%, #0288CB 100%);
    color: #012a59;
}

.order-content .form-control {
    padding-top: 0;
    padding-bottom: 0;
    height: 40px;
    border-radius: 0;
    font-size: 14px;
    background: #011D3E;
    border-color: #2C435F;
}

.l-40 {
    line-height: 40px;
}

.order-content .form-group {
    font-size: 14px;
}

.padding-none {
    padding: 0;
}

.o-time .form-control {
    width: 150px;
}

.btn-inquire .btn {
    background-color: #0E86CA;
    border: none;
    padding: 0;
    border-radius: 0;
    width: 100px;
    height: 40px;
    line-height: 40px;
}

.con-wrapper .order-content .table {
    margin-top: 0;
}
/*2019-10-16*/
.question-cut .icon {
    margin-bottom: 16px;
    width: 72px;
    height: 68px;
}

.question-cut p {
    color: #999;
}

.icon-account {
    background: url(../img/icon-account.png) no-repeat center center;
}

.icon-pay {
    background: url(../img/icon-pay.png) no-repeat center center;
}

.icon-game {
    background: url(../img/icon-game.png) no-repeat center center;
}

.icon-client {
    background: url(../img/icon-client.png) no-repeat center center;
}

.question-cut .item.cur p {
    color: #00afff;
}

.question-cut .item.cur .icon-account {
    background: url(../img/icon-account-cur.png) no-repeat center center;
}

.question-cut .item.cur .icon-pay {
    background: url(../img/icon-pay-cur.png) no-repeat center center;
}

.question-cut .item.cur .icon-game {
    background: url(../img/icon-game-cur.png) no-repeat center center;
}

.question-cut .item.cur .icon-client {
    background: url(../img/icon-client-cur.png) no-repeat center center;
}

.padding-both {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

.type-content {
    margin: 34px 8px 50px;
    padding-bottom: 30px;
    border: 1px solid #737373;
    background: #011D3E;
}

    .type-content .type-list {
        padding: 0 18px;
    }

.type-list {
    position: relative;
}

.triangle {
    display: block;
    width: 0;
    height: 0;
    border-width: 28px;
    border-style: solid;
    border-color: transparent transparent #4B535C transparent;
    position: absolute;
    left: 45px;
    top: -66px;
}

    .triangle:after {
        content: '';
        display: block;
        width: 0;
        height: 0;
        border-width: 26px;
        border-style: solid;
        border-color: transparent transparent #011D3E transparent;
        position: absolute;
        left: -26px;
        top: -24px;
    }

.type-list h4 {
    font-size: 20px;
    padding: 5px 0;
}

.type-list .doubt {
    margin-bottom: 4px;
    font-size: 14px;
    color: #00afff;
}

.type-list .answer {
    line-height: 2;
    font-size: 12px;
    color: #eeeeee;
}

.type-item {
    overflow: hidden;
    margin-bottom: 9px;
}

.question-cut .item {
    cursor: pointer;
}

.btn-get {
    padding: 0;
    line-height: 40px;
    color: #fff;
    font-size: 16px;
}

.pb-52 {
    padding-bottom: 52px;
}


/*媒体查询*/
@media only screen and (min-width:1401px) {
    .main-container {
        padding-left: 0;
        padding-right: 0;
    }
}

@media (min-width: 1200px) {
    .container {
        width: 1200px;
    }
}

@media (max-width: 1024px) {
    .account-pass {
        padding-right: 0;
    }
}

@media (min-width: 961px) {
    .mobile-header {
        display: none;
    }

    .main-container {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row;
        padding: 15px 0 0;
    }

    .sidebar {
        display: block;
        padding: 0;
        -ms-flex-negative: 0;
        flex-shrink: 0;
        -webkit-box-flex: 0;
        -ms-flex-positive: 0;
        flex-grow: 0;
        -ms-flex-preferred-size: 300px;
        flex-basis: 300px;
        border-radius: 5px;
        max-height: 970px;
    }

    .main-content {
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        padding-left: 15px;
    }
}

@media (max-width:960px) {
    .pc-header, #pcSidebar {
        display: none;
    }

    .navbar-compact {
        min-height: 48px;
    }
}

@media (max-width: 767px) {
    .wrap {
        height: auto;
    }

    .login-box {
        width: 95%;
    }

    .main-container {
        padding-top: 15px;
    }

    .con-s {
        padding-left: 0;
        padding-right: 0;
    }

    .con-frame {
        width: 100%;
    }

    .con-frame-box {
        margin: 0 20px;
        padding: 0;
    }

        .con-frame-box h2 {
            padding: 0;
        }

    .payment h3, .btn-handle a {
        margin-bottom: 15px;
    }

    .wait, .btn-scan {
        width: 100%;
    }

    .btn-pages {
        padding-left: 15px;
    }

    .btn-next {
        width: 100%;
    }

    .order-record-list {
        margin: 0;
    }

/*    .con-wrapper .box-content {
        padding-left: 0;
        padding-right: 0;
    }*/
}

@media only screen and (max-width:320px) {
    .wallet-list {
        font-size: 12px;
    }

    .con-form .form-control.commission {
        padding-left: 2px;
        font-size: 12px;
    }
}

/*2020/12/18*/
.sidebar ul li .icon-gmk {
    margin-top: 30px !important;
    background: url(../img/icon-gmk-cur.png) no-repeat center center;
    width: 22px;
    height: 22px;
}

.sidebar ul li.cur .icon-gmk {
    margin-top: 30px !important;
    background: url(../img/icon-gmk.png) no-repeat center center;
    width: 22px;
    height: 22px;
}

.add-tab {
    overflow: hidden;
    width: 100%;
    line-height: 60px;
    background-color: #FFFFFF;
    font-size: 18px;
    border-bottom: 1px solid #e1e1e1;
    border-radius: 5px;
}

    .add-tab .item .name {
        display: inline-block;
        cursor: pointer;
        color: #666666;
        font-size: 20px;
    }

    .add-tab .item.cur .name {
        border-bottom: 4px solid #1A85FF;
        font-weight: bold;
        color: #333333;
    }

.new-add-table {
    margin-top: 0 !important;
}

    .new-add-table > thead > tr > th {
        height: 60px;
        vertical-align: middle;
        background-color: #C6E0FF;
        color: #333333 !important;
    }

    .new-add-table > tbody > tr > td {
        color: #333333 !important;
    }

.p-12 {
    display: block;
    padding: 12px 0;
}

.pr-18 {
    padding-right: 18px;
}

.begins .img-dh {
    width: 16px;
    height: 16px;
    margin-right: 12px;
}

.begins {
    font-size: 16px;
    color: #00aaff;
}

.row .text-muted {
    color: #333333;
}

.add-card .card-item .li {
    height: 220px;
    padding: 20px 0;
    margin-bottom: 30px;
    border: 1px solid #CCCCCC;
    background-color: #FFFFFF;
    border-radius: 5px;
}

.b-blue {
    color: #00aaff;
}

.bold {
    font-weight: 700;
}

.newList1 .content {
    padding-left: 0;
}

.add-p p {
    margin: 9px 0 0 0 !important;
}

.add-gm-time {
    padding: 20px 0 0 15px;
    font-size: 14px;
    color: #999999;
}

    .add-gm-time .btn {
        padding-top: 0;
        padding-bottom: 0;
        padding-left: 30px;
        padding-right: 30px;
        height: 32px;
        line-height: 32px;
        color: white;
        background-color: #0e86ca;
        border-radius: 0;
        border-color: #0e86ca;
        margin-left: 44px !important;
    }

        .add-gm-time .btn:first-child {
            background-color: #043e6b;
            border-color: #00aaff;
        }

        .add-gm-time .btn:hover {
            background-color: #00aaff;
        }

@media (max-width: 767px) {
    .newPicBox {
        text-align: center;
    }

    .newList1 .content {
        padding-left: 15px;
    }

    .add-card .card-item .li {
        height: auto;
    }

    .add-boxs .btn-group-note {
        width: 100%;
        margin-bottom: 10px;
    }

    .add-boxs .subtitle {
        width: 50%;
    }

    .sidebar {
        height: auto;
        overflow: auto;
        z-index: 101;
    }
}

.add-modal-dialog .modal-header {
    padding-bottom: 0;
    border-bottom: none;
}

.add-modal-dialog .modal-body {
    padding-bottom: 0;
}

.add-modal-dialog .modal-footer {
    padding-top: 8px;
    border-top: none;
    text-align: center;
}

    .add-modal-dialog .modal-footer .btn {
        height: 40px;
        border-radius: 0;
    }

        .add-modal-dialog .modal-footer .btn.btn-first {
            width: 38%;
            color: #999;
        }

        .add-modal-dialog .modal-footer .btn.btn-large {
            width: 54%;
        }

.add-common-img {
    overflow: hidden;
    margin: 0 auto;
    width: 98px;
    height: 98px;
}

.no-radius {
    border-radius: 0;
}

.color-9 {
    color: #999;
}

.h40 {
    height: 40px;
    border-color: #cccccc;
}

.add-xgjs {
    height: 90px;
}

.cwidth {
    width: 100% !important;
}

.add-rule-tip {
    font-size: 14px;
    color: #ff0000;
}

    .add-rule-tip img {
        margin-right: 5px;
        width: 14px;
        height: 14px;
    }

.img-zw {
    margin: 60px 0;
    width: 132px;
    height: 127px;
}

.begins .icon-dh {
    width: 16px;
    height: 16px;
    margin: 4px 12px 0 0;
    background: url(../img/img-dh-cur.png) no-repeat center center;
}

.begins.cur .icon-dh {
    background: url(../img/img-dh.png) no-repeat center center;
}


@media (min-width: 768px) {
    .add-modal-dialog.modal-dialog {
        width: 400px;
    }
}
