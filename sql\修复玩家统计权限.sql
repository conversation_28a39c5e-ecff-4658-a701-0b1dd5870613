-- 玩家统计权限修复脚本
-- 菜单已存在，只需要为其他角色添加权限

-- 1. 检查现有权限分配情况
SELECT '=== 当前权限分配情况 ===' as info;
SELECT r.role_name, m.menu_name, m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.menu_id BETWEEN 2060 AND 2069
ORDER BY r.role_name, m.order_num;

-- 2. 检查是否有重复权限（避免重复插入错误）
SELECT '=== 检查重复权限 ===' as info;
SELECT rm.role_id, rm.menu_id, r.role_name, m.menu_name
FROM sys_role_menu rm 
JOIN sys_role r ON rm.role_id = r.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE rm.menu_id BETWEEN 2060 AND 2069 AND rm.role_id IN (2, 100);

-- 3. 删除可能存在的重复权限（如果有的话）
DELETE FROM sys_role_menu 
WHERE role_id = 2 AND menu_id BETWEEN 2060 AND 2069;

DELETE FROM sys_role_menu 
WHERE role_id = 100 AND menu_id BETWEEN 2060 AND 2069;

-- 4. 为"主播"角色（role_id=2）添加玩家统计权限
-- 主播角色只给基础权限，不给admin专用的权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES 
(2, 2060),  -- 玩家统计主菜单
(2, 2061),  -- 玩家查询
(2, 2062),  -- 玩家统计
(2, 2063),  -- 玩家详情
(2, 2064),  -- 在线统计
(2, 2065);  -- 注册统计

-- 5. 为"技术"角色（role_id=100）添加玩家统计权限
-- 技术角色给全部权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES 
(100, 2060),  -- 玩家统计主菜单
(100, 2061),  -- 玩家查询
(100, 2062),  -- 玩家统计
(100, 2063),  -- 玩家详情
(100, 2064),  -- 在线统计
(100, 2065),  -- 注册统计
(100, 2066),  -- 邀请码查询
(100, 2067),  -- 邀请码统计
(100, 2068),  -- 邀请码列表
(100, 2069);  -- 玩家导出

-- 6. 验证权限分配结果
SELECT '=== 权限分配完成后的结果 ===' as info;
SELECT r.role_name, m.menu_name, m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.menu_id BETWEEN 2060 AND 2069
ORDER BY r.role_name, m.order_num;

-- 7. 统计每个角色的玩家统计权限数量
SELECT '=== 权限统计 ===' as info;
SELECT r.role_name, COUNT(*) as permission_count
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.menu_id BETWEEN 2060 AND 2069
GROUP BY r.role_name
ORDER BY r.role_name;
