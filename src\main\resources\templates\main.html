<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计</title>
    <link rel="shortcut icon" href="favicon.ico">
    <link href="../static/css/bootstrap.min.css" th:href="@{/css/bootstrap.min.css}" rel="stylesheet"/>
    <link href="../static/css/font-awesome.min.css" th:href="@{/css/font-awesome.min.css}" rel="stylesheet"/>
    <link href="../static/css/main/animate.min.css" th:href="@{/css/main/animate.min.css}" rel="stylesheet"/>
    <link href="../static/css/main/style.min862f.css" th:href="@{/css/main/style.min862f.css}" rel="stylesheet"/>
    <script th:src="@{/js/echarts.min.js}"></script>
</head>

<body class="gray-bg">
    <div class="wrapper wrapper-content">

        <div class="row">
            <div class="col-sm-3">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <span class="label label-success pull-right">月</span>
                        <h5>收入</h5>
                    </div>
                    <div class="ibox-content">
                        <h1 id="monamid" class="no-margins">0</h1>
                        <div class="stat-percent font-bold text-success">
                            <!-- <i class="fa fa-bolt"></i> -->
                        </div>
                        <small>总收入</small>
                    </div>
                </div>
            </div>
            <div class="col-sm-3">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <span class="label label-info pull-right">全年</span>
                        <h5>收入</h5>
                    </div>
                    <div class="ibox-content">
                        <h1 id="monamyearid" class="no-margins">0</h1>
                        <div class="stat-percent font-bold text-info"><!-- <i class="fa fa-level-up"></i>-->
                        </div>
                        <small>全年收入</small>
                    </div>
                </div>
            </div>
            <div class="col-sm-3">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <span class="label label-info pull-right">全年</span>
                        <h5>订单总数</h5>
                    </div>
                    <div class="ibox-content">
                        <h1 id="yeardd" class="no-margins">0</h1>
                        <div class="stat-percent font-bold text-navy"><!--44% <i class="fa fa-level-up"></i>-->
                        </div>
                        <small>全年订单</small>
                    </div>
                </div>
            </div>
            <div class="col-sm-3">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <span class="label label-primary pull-right">全年</span>
                        <h5>支付成功订单</h5>
                    </div>
                    <div class="ibox-content">
                        <h1 id="yearddcg" class="no-margins">0</h1>
                        <div class="stat-percent font-bold text-danger"><!--38% <i class="fa fa-level-down"></i>-->
                        </div>
                        <small>全年支付成功订单</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>订单</h5>
                        <div class="pull-right">
                            <div class="btn-group">
                                <button type="button" class="btn btn-xs btn-white active">月</button>

                            </div>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-9">
                                <div class="flot-chart">
                                    <div class="flot-chart-content" id="flot-dashboard-chart"></div>
                                </div>
                            </div>
                            <div class="col-sm-3" >
                                <ul class="stat-list">
                                    <li>
                                        <h2 class="no-margins" id="yeardd2">0</h2>
                                        <small>订单总数</small>
                                        <div class="stat-percent">
                                        </div>
                                        <div class="progress progress-mini">
                                            <div style="width: 100%;" class="progress-bar"></div>
                                        </div>
                                    </li>
                                    <li>
                                        <h2 class="no-margins " id="yearddcg2">0</h2>
                                        <small>支付成功订单</small>
                                        <div class="stat-percent">
                                        </div>
                                        <div class="progress progress-mini">
                                            <div style="width: 85%;" class="progress-bar"></div>
                                        </div>
                                    </li>
                                    <li>
                                        <h2 class="no-margins " id="monamid2">0</h2>
                                        <small>本月销售额</small>
                                        <div class="stat-percent">
                                        </div>
                                        <div class="progress progress-mini">
                                            <div style="width: 100%;" class="progress-bar"></div>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>利润分成列表</h5>
                        <div class="ibox-tools">
                            <a class="collapse-link">
                                <i class="fa fa-chevron-up"></i>
                            </a>
                            <a class="close-link">
                                <i class="fa fa-times"></i>
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <table class="table table-hover no-margins">
                            <thead>
                                <tr bgcolor="#DDDDDD">
                                    <th>代理人</th>
                                    <th>分成月份</th>
                                    <th>分成金额</th>
                                    <th>支付成功数</th>
                                </tr>
                            </thead>
                            <tbody id ="pay_sum_id">

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
         </div>
      </div>
    </div>
    <script th:src="@{/js/jquery.min.js}"></script>
    <script th:src="@{/js/bootstrap.min.js}"></script>
    <script th:src="@{/ajax/libs/flot/jquery.flot.js}"></script>
    
    <th:block th:include="include :: sparkline-js" />
    <script type="text/javascript">
	    $(document).ready(function () {
            var prefix="/system/paysystem";

	        var previousPoint = null,
	            previousLabel = null;
            retsebyMon(prefix);
            retsebyYearam(prefix);
            retsebyYear(prefix);
            retsebyYearcg(prefix);
            selecttb(prefix);
            selectPayAm(prefix);
            /**查询当月订单支付成功和总额的方法*/
            function selecttb(prefix){
                var date = new Date();
                var year = date.getFullYear();
                var mon = ("0"+(date.getMonth()+1)).slice(-2);
                var day = date.getDate();/**当前天数*/
                var params = {};
                var pie=echarts.init(document.getElementById("flot-dashboard-chart"));
                params["beginCreateTime"] = year+"-"+mon+"-01";
                params["endCreateTime"] = year+"-"+mon+"-"+day;
                $.ajax({
                    type:"post",
                    url: prefix + "/list3",
                    dataType:"json",
                    data:{"channelCode":"","params":params,"status":"支付成功"},
                    success:function (res) {
                        var data1=[];
                        var data2=[];
                        var data3=[];
                        for(var i in res){
                            var time=res[i].paySuccTime;
                            var tt=time.split("-");
                            var tm=tt[1]+"月"+tt[2];
                            data1.push(tm);
                            data2.push(res[i].createBy);
                            data3.push(res[i].amount);
                        }

                        var	option = {
                            title: {
                                text: '订单'
                            },
                            tooltip: {
                                trigger: 'axis'
                            },
                            legend: {
                                data: ['金额', '订单数']
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                containLabel: true
                            },
                            toolbox: {
                                feature: {
                                    saveAsImage: {}
                                }
                            },
                            xAxis: {
                                type: 'category',
                                boundaryGap: true,
                                data: data1
                            },
                            yAxis: [
                                {
                                    type: 'value',
                                    name:'金额',
                                    show:true

                                },{
                                    type: 'value',
                                    name:'订单数',
                                    min:0,
                                    max:100
                                }
                            ],
                            series: [
                                {
                                    name: '金额',
                                    type: 'bar',
                                    barWidth: '50%',
                                    stack: 'Total',
                                    data: data3
                                },
                                {
                                    name: '订单数',
                                    yAxisIndex:1,
                                    type: 'line',
                                    data: data2
                                }
                            ]
                        };
//		<!--5.将配置项设置给echarts实例化对象-->
                        pie.setOption(option);
                    }

                });


            };

	    });
	    /**查询当月总收入方法*/
        function retsebyMon(prefix){
            var date = new Date();
            var year = date.getFullYear();
            var mon = ("0"+(date.getMonth()+1)).slice(-2);
            var day = new Date(year,date.getMonth()+1,0).getDate();/**当月最后一天数*/
                //alert(year+"-"+mon+"-"+day);
            var params = {};
            params["beginCreateTime"] = year+"-"+mon+"-01";
            params["endCreateTime"] = year+"-"+mon+"-"+day;
            $.ajax({
                type:"post",
                url: prefix + "/list2",
                dataType:"json",
                data:{"channelCode":"","params":params},
                success:function (res) {
                    var yueam=res.amount;
                    document.getElementById("monamid").innerHTML=yueam;
                    document.getElementById("monamid2").innerHTML=yueam;
                }

            });
        };
        /**查询当年总收入方法*/
        function retsebyYearam(prefix){
            var date = new Date();
            var year = date.getFullYear();
            var params = {};
            params["beginCreateTime"] = year+"-01-01";
            params["endCreateTime"] = year+"-12-31";
            $.ajax({
                type:"post",
                url: prefix + "/list2",
                dataType:"json",
                data:{"channelCode":"","params":params},
                success:function (res) {
                    var yearam=res.amount;
                    document.getElementById("monamyearid").innerHTML=yearam;
                }

            });
        };
        /**查询年总订单方法*/
        function retsebyYear(prefix){
            var date = new Date();
            var year = date.getFullYear();
            var params = {};
            params["beginCreateTime"] = year+"-01-01";
            params["endCreateTime"] = year+"-12-31";
            $.ajax({
                type:"post",
                url: prefix + "/list",
                dataType:"json",
                data:{"channelCode":"","params":params},
                success:function (res) {
                    var yearam=res.total;
                    document.getElementById("yeardd").innerHTML=yearam;
                    document.getElementById("yeardd2").innerHTML=yearam;

                }

            });
        };
        /**查询年总订单支付成功方法*/
        function retsebyYearcg(prefix){
            var date = new Date();
            var year = date.getFullYear();
            var params = {};
            params["beginCreateTime"] = year+"-01-01";
            params["endCreateTime"] = year+"-12-31";
            $.ajax({
                type:"post",
                url: prefix + "/list",
                dataType:"json",
                data:{"channelCode":"","params":params,"status":"支付成功"},
                success:function (res) {
                    var yearamcg=res.total;
                    document.getElementById("yearddcg").innerHTML=yearamcg;
                    document.getElementById("yearddcg2").innerHTML=yearamcg;
                }

            });
        };
        /**查询当月代理人分成金额*/
        function selectPayAm(prefix){
            var date = new Date();
            var year = date.getFullYear();
            var mon = ("0"+(date.getMonth()+1)).slice(-2);
            var day = new Date(year,date.getMonth()+1,0).getDate();/**当月最后一天数*/
                //alert(year+"-"+mon+"-"+day);
            var params = {};
            params["beginCreateTime"] = year+"-"+mon+"-01";
            params["endCreateTime"] = year+"-"+mon+"-"+day;
            $.ajax({
                type:"post",
                url: prefix + "/list4",
                dataType:"json",
                data:{"channelCode":"","params":params,"status":"支付成功"},
                success:function (res) {
                    var vtable="";
                    for(var i in res){
                        var agency ='';
                        if(res[i].agency.length<=0){
                            agency ="无代理人";
                        }else{
                            agency=res[i].agency;
                        }
                        vtable=vtable+"<tr><td>"+agency+"</td>"+"<td>"+res[i].paySuccTime+"</td>"+"<td>"+res[i].amount+"</td>"+"<td>"+res[i].createBy+"</td></tr>";

                    }
                    document.getElementById("pay_sum_id").innerHTML=vtable;
                }

            });
        };

    </script>
</body>
</html>
