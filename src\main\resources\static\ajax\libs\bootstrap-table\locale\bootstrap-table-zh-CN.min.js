/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.17.1
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t=t||self).jQuery)}(this,(function(t){"use strict";t=t&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t;var n="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function r(t,n){return t(n={exports:{}},n.exports),n.exports}var e=function(t){return t&&t.Math==Math&&t},o=e("object"==typeof globalThis&&globalThis)||e("object"==typeof window&&window)||e("object"==typeof self&&self)||e("object"==typeof n&&n)||Function("return this")(),i=function(t){try{return!!t()}catch(t){return!0}},u=!i((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})),c={}.propertyIsEnumerable,f=Object.getOwnPropertyDescriptor,a={f:f&&!c.call({1:2},1)?function(t){var n=f(this,t);return!!n&&n.enumerable}:c},l=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},s={}.toString,p=function(t){return s.call(t).slice(8,-1)},y="".split,g=i((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==p(t)?y.call(t,""):Object(t)}:Object,m=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},h=function(t){return g(m(t))},d=function(t){return"object"==typeof t?null!==t:"function"==typeof t},v=function(t,n){if(!d(t))return t;var r,e;if(n&&"function"==typeof(r=t.toString)&&!d(e=r.call(t)))return e;if("function"==typeof(r=t.valueOf)&&!d(e=r.call(t)))return e;if(!n&&"function"==typeof(r=t.toString)&&!d(e=r.call(t)))return e;throw TypeError("Can't convert object to primitive value")},b={}.hasOwnProperty,w=function(t,n){return b.call(t,n)},S=o.document,O=d(S)&&d(S.createElement),j=!u&&!i((function(){return 7!=Object.defineProperty((t="div",O?S.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),P=Object.getOwnPropertyDescriptor,T={f:u?P:function(t,n){if(t=h(t),n=v(n,!0),j)try{return P(t,n)}catch(t){}if(w(t,n))return l(!a.f.call(t,n),t[n])}},x=function(t){if(!d(t))throw TypeError(String(t)+" is not an object");return t},E=Object.defineProperty,A={f:u?E:function(t,n,r){if(x(t),n=v(n,!0),x(r),j)try{return E(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},C=u?function(t,n,r){return A.f(t,n,l(1,r))}:function(t,n,r){return t[n]=r,t},M=function(t,n){try{C(o,t,n)}catch(r){o[t]=n}return n},_=o["__core-js_shared__"]||M("__core-js_shared__",{}),F=Function.toString;"function"!=typeof _.inspectSource&&(_.inspectSource=function(t){return F.call(t)});var N,R,k,I,L=_.inspectSource,z=o.WeakMap,D="function"==typeof z&&/native code/.test(L(z)),q=r((function(t){(t.exports=function(t,n){return _[t]||(_[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.6.0",mode:"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})})),B=0,G=Math.random(),W=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++B+G).toString(36)},H=q("keys"),J={},K=o.WeakMap;if(D){var Q=new K,U=Q.get,V=Q.has,Y=Q.set;N=function(t,n){return Y.call(Q,t,n),n},R=function(t){return U.call(Q,t)||{}},k=function(t){return V.call(Q,t)}}else{var X=H[I="state"]||(H[I]=W(I));J[X]=!0,N=function(t,n){return C(t,X,n),n},R=function(t){return w(t,X)?t[X]:{}},k=function(t){return w(t,X)}}var Z,$,tt={set:N,get:R,has:k,enforce:function(t){return k(t)?R(t):N(t,{})},getterFor:function(t){return function(n){var r;if(!d(n)||(r=R(n)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},nt=r((function(t){var n=tt.get,r=tt.enforce,e=String(String).split("String");(t.exports=function(t,n,i,u){var c=!!u&&!!u.unsafe,f=!!u&&!!u.enumerable,a=!!u&&!!u.noTargetGet;"function"==typeof i&&("string"!=typeof n||w(i,"name")||C(i,"name",n),r(i).source=e.join("string"==typeof n?n:"")),t!==o?(c?!a&&t[n]&&(f=!0):delete t[n],f?t[n]=i:C(t,n,i)):f?t[n]=i:M(n,i)})(Function.prototype,"toString",(function(){return"function"==typeof this&&n(this).source||L(this)}))})),rt=o,et=function(t){return"function"==typeof t?t:void 0},ot=function(t,n){return arguments.length<2?et(rt[t])||et(o[t]):rt[t]&&rt[t][n]||o[t]&&o[t][n]},it=Math.ceil,ut=Math.floor,ct=function(t){return isNaN(t=+t)?0:(t>0?ut:it)(t)},ft=Math.min,at=function(t){return t>0?ft(ct(t),9007199254740991):0},lt=Math.max,st=Math.min,pt=function(t){return function(n,r,e){var o,i=h(n),u=at(i.length),c=function(t,n){var r=ct(t);return r<0?lt(r+n,0):st(r,n)}(e,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},yt={includes:pt(!0),indexOf:pt(!1)}.indexOf,gt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),mt={f:Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=h(t),o=0,i=[];for(r in e)!w(J,r)&&w(e,r)&&i.push(r);for(;n.length>o;)w(e,r=n[o++])&&(~yt(i,r)||i.push(r));return i}(t,gt)}},ht={f:Object.getOwnPropertySymbols},dt=ot("Reflect","ownKeys")||function(t){var n=mt.f(x(t)),r=ht.f;return r?n.concat(r(t)):n},vt=function(t,n){for(var r=dt(n),e=A.f,o=T.f,i=0;i<r.length;i++){var u=r[i];w(t,u)||e(t,u,o(n,u))}},bt=/#|\.prototype\./,wt=function(t,n){var r=Ot[St(t)];return r==Pt||r!=jt&&("function"==typeof n?i(n):!!n)},St=wt.normalize=function(t){return String(t).replace(bt,".").toLowerCase()},Ot=wt.data={},jt=wt.NATIVE="N",Pt=wt.POLYFILL="P",Tt=wt,xt=T.f,Et=Array.isArray||function(t){return"Array"==p(t)},At=function(t){return Object(m(t))},Ct=function(t,n,r){var e=v(n);e in t?A.f(t,e,l(0,r)):t[e]=r},Mt=!!Object.getOwnPropertySymbols&&!i((function(){return!String(Symbol())})),_t=Mt&&!Symbol.sham&&"symbol"==typeof Symbol(),Ft=q("wks"),Nt=o.Symbol,Rt=_t?Nt:W,kt=function(t){return w(Ft,t)||(Mt&&w(Nt,t)?Ft[t]=Nt[t]:Ft[t]=Rt("Symbol."+t)),Ft[t]},It=kt("species"),Lt=function(t,n){var r;return Et(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!Et(r.prototype)?d(r)&&null===(r=r[It])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===n?0:n)},zt=ot("navigator","userAgent")||"",Dt=o.process,qt=Dt&&Dt.versions,Bt=qt&&qt.v8;Bt?$=(Z=Bt.split("."))[0]+Z[1]:zt&&(!(Z=zt.match(/Edge\/(\d+)/))||Z[1]>=74)&&(Z=zt.match(/Chrome\/(\d+)/))&&($=Z[1]);var Gt,Wt=$&&+$,Ht=kt("species"),Jt=kt("isConcatSpreadable"),Kt=Wt>=51||!i((function(){var t=[];return t[Jt]=!1,t.concat()[0]!==t})),Qt=(Gt="concat",Wt>=51||!i((function(){var t=[];return(t.constructor={})[Ht]=function(){return{foo:1}},1!==t[Gt](Boolean).foo}))),Ut=function(t){if(!d(t))return!1;var n=t[Jt];return void 0!==n?!!n:Et(t)};!function(t,n){var r,e,i,u,c,f=t.target,a=t.global,l=t.stat;if(r=a?o:l?o[f]||M(f,{}):(o[f]||{}).prototype)for(e in n){if(u=n[e],i=t.noTargetGet?(c=xt(r,e))&&c.value:r[e],!Tt(a?e:f+(l?".":"#")+e,t.forced)&&void 0!==i){if(typeof u==typeof i)continue;vt(u,i)}(t.sham||i&&i.sham)&&C(u,"sham",!0),nt(r,e,u,t)}}({target:"Array",proto:!0,forced:!Kt||!Qt},{concat:function(t){var n,r,e,o,i,u=At(this),c=Lt(u,0),f=0;for(n=-1,e=arguments.length;n<e;n++)if(Ut(i=-1===n?u:arguments[n])){if(f+(o=at(i.length))>9007199254740991)throw TypeError("Maximum allowed index exceeded");for(r=0;r<o;r++,f++)r in i&&Ct(c,f,i[r])}else{if(f>=9007199254740991)throw TypeError("Maximum allowed index exceeded");Ct(c,f++,i)}return c.length=f,c}}),t.fn.bootstrapTable.locales["zh-CN"]={formatLoadingMessage:function(){return"正在努力地加载数据中，请稍候"},formatRecordsPerPage:function(t){return"每页显示 ".concat(t," 条记录")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"显示第 ".concat(t," 到第 ").concat(n," 条记录，总共 ").concat(r," 条记录（从 ").concat(e," 总记录中过滤）"):"显示第 ".concat(t," 到第 ").concat(n," 条记录，总共 ").concat(r," 条记录")},formatSRPaginationPreText:function(){return"上一页"},formatSRPaginationPageText:function(t){return"第".concat(t,"页")},formatSRPaginationNextText:function(){return"下一页"},formatDetailPagination:function(t){return"总共 ".concat(t," 条记录")},formatClearSearch:function(){return"清空过滤"},formatSearch:function(){return"搜索"},formatNoMatches:function(){return"没有找到匹配的记录"},formatPaginationSwitch:function(){return"隐藏/显示分页"},formatPaginationSwitchDown:function(){return"显示分页"},formatPaginationSwitchUp:function(){return"隐藏分页"},formatRefresh:function(){return"刷新"},formatToggle:function(){return"切换"},formatToggleOn:function(){return"显示卡片视图"},formatToggleOff:function(){return"隐藏卡片视图"},formatColumns:function(){return"列"},formatColumnsToggleAll:function(){return"切换所有"},formatFullscreen:function(){return"全屏"},formatAllRows:function(){return"所有"},formatAutoRefresh:function(){return"自动刷新"},formatExport:function(){return"导出数据"},formatJumpTo:function(){return"跳转"},formatAdvancedSearch:function(){return"高级搜索"},formatAdvancedCloseButton:function(){return"关闭"},formatFilterControlSwitch:function(){return"隐藏/显示过滤控制"},formatFilterControlSwitchHide:function(){return"隐藏过滤控制"},formatFilterControlSwitchShow:function(){return"显示过滤控制"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["zh-CN"])}));