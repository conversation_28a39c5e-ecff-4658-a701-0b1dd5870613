<!DOCTYPE html>
<html>
<head>
    <title>在线玩家列表测试</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>在线玩家列表测试页面</h1>
    <button onclick="testOnlinePlayersList()">测试查询在线玩家列表</button>
    <div id="result" style="margin-top: 20px;"></div>
    
    <script>
        function testOnlinePlayersList() {
            console.log("开始测试在线玩家列表查询...");
            
            // 模拟账户名列表
            var accountNames = ['cx19840312', 'yl008', 'jj008', 'jj888'];
            
            $.ajax({
                type: "POST",
                url: "/system/player/onlinePlayers",
                contentType: "application/json",
                data: JSON.stringify(accountNames),
                dataType: "json",
                success: function (res) {
                    console.log("请求成功:", res);
                    
                    if (res.code === 0) {
                        var characters = res.data;
                        var html = "<h3>查询成功！找到 " + characters.length + " 个在线角色：</h3>";
                        
                        if (characters.length > 0) {
                            html += "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                            html += "<tr><th>游戏名</th><th>账号名</th><th>等级</th><th>在线时长</th><th>最后登录</th></tr>";
                            
                            for (var i = 0; i < characters.length; i++) {
                                var char = characters[i];
                                html += "<tr>";
                                html += "<td>" + char.charName + "</td>";
                                html += "<td>" + char.accountName + "</td>";
                                html += "<td>" + char.level + "</td>";
                                html += "<td>" + char.useTimeFormatted + "</td>";
                                html += "<td>" + char.login + "</td>";
                                html += "</tr>";
                            }
                            
                            html += "</table>";
                        } else {
                            html += "<p>没有找到在线角色</p>";
                        }
                        
                        $("#result").html(html);
                    } else {
                        $("#result").html("<p style='color: red;'>查询失败: " + res.msg + "</p>");
                    }
                },
                error: function(xhr, status, error) {
                    console.error("请求失败:", xhr, status, error);
                    $("#result").html("<p style='color: red;'>请求失败: " + error + "<br>状态码: " + xhr.status + "</p>");
                }
            });
        }
    </script>
</body>
</html>
