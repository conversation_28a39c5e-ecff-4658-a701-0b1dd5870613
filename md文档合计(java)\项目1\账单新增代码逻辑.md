# Pay表账单新增代码逻辑分析

## 1. 概述

Pay表是系统中的支付记录表，主要用于存储用户的支付订单信息。本文档详细分析了Pay表数据的新增逻辑和相关接口。

## 2. 数据库表结构

### Pay表字段说明
- `id`: 主键ID
- `mch_order_no`: 商户订单号（系统生成的唯一订单号）
- `channel_code`: 支付渠道（如：wxpay微信支付、alipay支付宝）
- `amount`: 支付金额
- `goods_param_ext`: 角色名/商品描述
- `ip`: 用户IP地址
- `status`: 支付状态（未支付、支付成功）
- `pay_succ_time`: 支付时间
- `wuDi_order_no`: 第三方平台订单号
- `area`: 区服号
- `agency`: 代理信息
- `create_time`: 创建时间
- `update_time`: 更新时间

## 3. 核心新增接口

### 3.1 POST /pay/save - 网页端支付提交接口

**文件位置**: `src/main/java/com/jeethink/project/system/pay/controller/PayController.java`

**接口功能**: 处理用户在网页端提交的支付请求

**核心逻辑**:
```java
@PostMapping("/pay/save")
@ResponseStatus(HttpStatus.FOUND)
public String save(Pay pay, HttpServletRequest request, HttpServletResponse response, ModelMap mmap) {
    // 1. 检查订单是否已存在
    Pay oldPay = payService.selectByNoId(pay.getMchOrderNo());

    if(oldPay == null) {
        // 2. 获取用户真实IP地址
        String ipAddress = getClientIpAddress(request);

        // 3. 设置支付记录基本信息
        pay.setStatus("未支付");
        pay.setIp(ipAddress);
        pay.setPaySuccTime(getCurrentTimeString());

        // 4. 构建第三方支付参数
        HashMap<String, Object> params = buildPaymentParams(pay);

        // 5. 插入支付记录到数据库
        int result = payService.insertPay(pay);

        // 6. 如果插入成功，重定向到第三方支付平台
        if(result == 1) {
            URIBuilder builder = new URIBuilder(apiurl);
            // 添加支付参数到URL
            for (String key : params.keySet()) {
                builder.addParameter(key, params.get(key).toString());
            }
            response.setHeader("location", builder.toString());
            return null;
        }
    }

    // 订单已存在或插入失败，返回错误页面
    return prefix + "/info";
}
```

**关键步骤**:
1. **订单重复检查**: 通过`mch_order_no`检查订单是否已存在
2. **IP地址获取**: 从HTTP请求头中获取用户真实IP
3. **数据预处理**: 设置支付状态、IP、支付时间等基础信息
4. **参数构建**: 构建第三方支付平台所需的参数和签名
5. **数据库插入**: 调用Service层插入支付记录
6. **支付跳转**: 成功后重定向到第三方支付平台

### 3.2 GET /pay/game - 游戏内支付接口

**接口功能**: 处理游戏内发起的支付请求

**核心逻辑**:
```java
@GetMapping("/pay/game")
@ResponseStatus(HttpStatus.FOUND)
public String game(Pay pay, HttpServletRequest request, HttpServletResponse response, ModelMap mmap) {
    // 1. 获取用户IP地址
    String ipAddress = getClientIpAddress(request);

    // 2. 生成唯一订单号
    UUID id = UUID.randomUUID();
    String[] idd = id.toString().split("-");
    String uid = idd[0] + idd[1] + idd[2] + idd[3];

    // 3. 设置支付记录信息
    pay.setMchOrderNo(uid);
    pay.setStatus("未支付");
    pay.setIp(ipAddress);
    pay.setPaySuccTime(getCurrentTimeString());

    // 4. 构建支付参数和签名
    HashMap<String, Object> params = buildPaymentParams(pay);

    // 5. 插入支付记录
    int result = payService.insertPay(pay);

    // 6. 重定向到支付平台
    if(result == 1) {
        // 构建支付URL并重定向
        URIBuilder builder = new URIBuilder(apiurl);
        // ... URL参数构建逻辑
        response.setHeader("location", builder.toString());
        return null;
    }

    return prefix + "/info";
}
```

**与/pay/save的区别**:
- 自动生成订单号（不依赖前端传入）
- 主要用于游戏内调用
- 不进行订单重复性检查

## 4. Service层处理逻辑

### 4.1 PayServiceImpl.insertPay()

**文件位置**: `src/main/java/com/jeethink/project/system/pay/service/impl/PayServiceImpl.java`

```java
@Override
public int insertPay(Pay pay) {
    // 设置创建时间
    pay.setCreateTime(DateUtils.getNowDate());
    // 调用Mapper层插入数据
    return payMapper.insertPay(pay);
}
```

### 4.2 数据库插入逻辑

**文件位置**: `src/main/resources/mybatis/system/PayMapper.xml`

```xml
<insert id="insertPay" parameterType="Pay" useGeneratedKeys="true" keyProperty="id">
    insert into pay
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="mchOrderNo != null">mch_order_no,</if>
        <if test="channelCode != null">channel_code,</if>
        <if test="amount != null">amount,</if>
        <if test="goodsParamExt != null">goods_param_ext,</if>
        <if test="ip != null">ip,</if>
        <if test="area != null">area,</if>
        <if test="status != null">status,</if>
        <if test="paySuccTime != null">pay_succ_time,</if>
        <if test="createBy != null">create_by,</if>
        <if test="createTime != null">create_time,</if>
        <if test="updateBy != null">update_by,</if>
        <if test="updateTime != null">update_time,</if>
        <if test="wudiOrderNo != null">wuDi_order_no,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
        <if test="mchOrderNo != null">#{mchOrderNo},</if>
        <if test="channelCode != null">#{channelCode},</if>
        <if test="amount != null">#{amount},</if>
        <if test="goodsParamExt != null">#{goodsParamExt},</if>
        <if test="ip != null">#{ip},</if>
        <if test="area != null">#{area},</if>
        <if test="status != null">#{status},</if>
        <if test="paySuccTime != null">#{paySuccTime},</if>
        <if test="createBy != null">#{createBy},</if>
        <if test="createTime != null">#{createTime},</if>
        <if test="updateBy != null">#{updateBy},</if>
        <if test="updateTime != null">#{updateTime},</if>
        <if test="wudiOrderNo != null">#{wudiOrderNo},</if>
    </trim>
</insert>
```

## 5. 支付状态更新逻辑

### 5.1 支付回调接口 GET /pay/notifyUrl

**功能**: 接收第三方支付平台的支付结果通知

```java
@GetMapping("/pay/notifyUrl")
@ResponseBody
public String notifyUrl(String pid, String trade_no, String out_trade_no, String type,
                       String name, String money, String sign, String trade_status, String sign_type) {

    if ("TRADE_SUCCESS".equals(trade_status)) {
        // 1. 构建验签参数
        Map<String, Object> map = new HashMap<>();
        map.put("pid", pid);
        map.put("trade_no", trade_no);
        map.put("out_trade_no", out_trade_no);
        map.put("name", name);
        map.put("money", money);
        map.put("sign", sign);
        map.put("trade_status", trade_status);

        // 2. 验证签名
        boolean verify = SignUtils.checkParam(map, type + MD5_KEY, CHARSET);

        if (verify) {
            // 3. 根据商户订单号查询支付记录
            Pay pay = payService.selectByNoId(out_trade_no);

            // 4. 更新支付状态
            pay.setStatus("支付成功");
            pay.setWudiOrderNo(trade_no); // 设置第三方平台订单号

            // 5. 保存更新
            payService.updatePay(pay);

            return "SUCCESS";
        }
    }

    return "FAIL";
}
```

## 6. 完整数据流程

### 6.1 支付发起流程
1. **用户访问**: 用户访问 `/pay?area=1` 获取支付页面
2. **表单填写**: 用户填写支付金额、角色名、选择支付方式
3. **提交请求**: 表单提交到 `/pay/save` 接口
4. **数据处理**: Controller处理请求，生成支付记录
5. **数据库插入**: 调用Service层将支付记录插入pay表
6. **支付跳转**: 重定向到第三方支付平台

### 6.2 支付完成流程
1. **用户支付**: 用户在第三方平台完成支付
2. **回调通知**: 第三方平台调用 `/pay/notifyUrl` 通知支付结果
3. **签名验证**: 系统验证回调参数的签名
4. **状态更新**: 更新pay表中对应记录的支付状态
5. **返回确认**: 向第三方平台返回处理结果

## 7. 关键技术点

### 7.1 订单号生成策略
- 使用UUID生成唯一订单号
- 去除UUID中的连字符，取前4段拼接
- 确保订单号的唯一性

### 7.2 IP地址获取
```java
private String getClientIpAddress(HttpServletRequest request) {
    String ipAddress = request.getHeader("X-Forwarded-For");
    if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
        ipAddress = request.getHeader("Proxy-Client-IP");
    }
    if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
        ipAddress = request.getHeader("WL-Proxy-Client-IP");
    }
    if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
        ipAddress = request.getRemoteAddr();
    }
    return ipAddress;
}
```

### 7.3 支付参数签名
- 使用MD5算法对支付参数进行签名
- 签名密钥为: `channelCode + MD5_KEY`
- 确保支付请求的安全性

## 8. 配置参数

系统中相关的配置参数：
- `pay.MCH_ID`: 商户ID
- `pay.MD5_KEY`: MD5签名密钥
- `pay.url`: 系统回调地址
- `pay.apiurl`: 第三方支付平台API地址

## 9. 实际代码示例

### 9.1 支付参数构建实际代码
```java
HashMap<String, Object> params = new HashMap<>();
params.put("pid", MCH_ID);
params.put("out_trade_no", pay.getMchOrderNo());
params.put("notify_url", url+"/pay/notifyUrl");
params.put("return_url", url+"/pay/returnUrl");
params.put("name", pay.getGoodsParamExt());
params.put("money", pay.getAmount().toString());
params.put("sitename", "test");
String sign = SignUtils.sign(params, pay.getChannelCode()+MD5_KEY, RSAUtils.CHARSET);
params.put("type", pay.getChannelCode());
params.put("sign", sign);
params.put("sign_type", "MD5");
```

### 9.2 支付时间设置
```java
SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
String dateStr = sf.format(new Date());
pay.setPaySuccTime(dateStr);
```

## 10. 总结

Pay表的账单新增主要通过两个接口实现：
- `/pay/save`: 网页端支付提交
- `/pay/game`: 游戏内支付调用

数据流程清晰，包含了完整的支付发起、状态跟踪、回调处理等环节，确保了支付数据的完整性和准确性。

### 关键特点：
1. **双重保护**: 订单重复检查 + 签名验证
2. **状态跟踪**: 从"未支付"到"支付成功"的完整状态管理
3. **安全机制**: MD5签名确保支付请求安全
4. **灵活支持**: 同时支持网页端和游戏内支付场景