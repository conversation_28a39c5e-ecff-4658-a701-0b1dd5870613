package com.jeethink.project.system.player.service.impl;

import com.jeethink.project.system.player.dao.PlayerDao;
import com.jeethink.project.system.player.domain.UserAccount;
import com.jeethink.project.system.player.domain.Character;
import com.jeethink.project.system.player.service.IPlayerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 玩家数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@Service
public class PlayerServiceImpl implements IPlayerService {
    
    @Autowired
    private PlayerDao playerDao;

    /**
     * 查询玩家列表
     *
     * @param userAccount 玩家信息
     * @return 玩家集合
     */
    @Override
    public List<UserAccount> selectPlayerList(UserAccount userAccount) {
        return playerDao.selectPlayerListWithRegDate(userAccount);
    }

    /**
     * 查询玩家列表（带注册时间）
     *
     * @param userAccount 玩家信息
     * @return 玩家集合
     */
    @Override
    public List<UserAccount> selectPlayerListWithRegDate(UserAccount userAccount) {
        return playerDao.selectPlayerListWithRegDate(userAccount);
    }

    /**
     * 查询玩家列表（带注册时间，支持分页）
     *
     * @param userAccount 玩家信息
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 玩家集合
     */
    @Override
    public List<UserAccount> selectPlayerListWithRegDate(UserAccount userAccount, Integer pageNum, Integer pageSize) {
        return playerDao.selectPlayerListWithRegDate(userAccount, pageNum, pageSize);
    }

    /**
     * 统计玩家总数（用于分页）
     *
     * @param userAccount 玩家信息
     * @return 总记录数
     */
    @Override
    public int countPlayerListWithRegDate(UserAccount userAccount) {
        return playerDao.countPlayerListWithRegDate(userAccount);
    }

    /**
     * 根据邀请码查询玩家列表（忽略大小写）
     *
     * @param invitation 邀请码
     * @return 玩家集合
     */
    @Override
    public List<UserAccount> selectPlayerListByInvitation(String invitation) {
        return playerDao.selectPlayerListByInvitationWithRegDate(invitation);
    }

    /**
     * 根据邀请码查询玩家列表（带注册时间，忽略大小写）
     *
     * @param invitation 邀请码
     * @return 玩家集合
     */
    @Override
    public List<UserAccount> selectPlayerListByInvitationWithRegDate(String invitation) {
        return playerDao.selectPlayerListByInvitationWithRegDate(invitation);
    }

    /**
     * 获取玩家统计信息
     *
     * @return 统计信息Map
     */
    @Override
    public Map<String, Object> getPlayerStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("onlineCount", playerDao.countOnlinePlayers());
        stats.put("todayRegisterCount", playerDao.countTodayRegistrations());
        return stats;
    }

    /**
     * 根据邀请码获取玩家统计信息（忽略大小写）
     *
     * @param invitation 邀请码
     * @return 统计信息Map
     */
    @Override
    public Map<String, Object> getPlayerStatsByInvitation(String invitation) {
        Map<String, Object> stats = new HashMap<>();
        stats.put("onlineCount", playerDao.countOnlinePlayersByInvitation(invitation));
        stats.put("todayRegisterCount", playerDao.countTodayRegistrationsByInvitation(invitation));
        stats.put("totalPlayerCount", playerDao.countPlayersByInvitation(invitation));
        return stats;
    }

    /**
     * 统计当前在线玩家数量
     *
     * @return 在线玩家数量
     */
    @Override
    public int countOnlinePlayers() {
        return playerDao.countOnlinePlayers();
    }

    /**
     * 统计指定邀请码的在线玩家数量（忽略大小写）
     *
     * @param invitation 邀请码
     * @return 在线玩家数量
     */
    @Override
    public int countOnlinePlayersByInvitation(String invitation) {
        return playerDao.countOnlinePlayersByInvitation(invitation);
    }

    /**
     * 统计今日注册账号数量
     *
     * @return 今日注册数量
     */
    @Override
    public int countTodayRegistrations() {
        return playerDao.countTodayRegistrations();
    }

    /**
     * 统计指定邀请码今日注册账号数量（忽略大小写）
     *
     * @param invitation 邀请码
     * @return 今日注册数量
     */
    @Override
    public int countTodayRegistrationsByInvitation(String invitation) {
        return playerDao.countTodayRegistrationsByInvitation(invitation);
    }

    /**
     * 根据账户名查询玩家信息
     *
     * @param account 账户名
     * @return 玩家信息
     */
    @Override
    public UserAccount selectPlayerByAccount(String account) {
        return playerDao.selectPlayerByAccount(account);
    }

    /**
     * 查询所有邀请码列表（去重）
     *
     * @return 邀请码列表
     */
    @Override
    public List<String> selectAllInvitations() {
        return playerDao.selectAllInvitations();
    }

    /**
     * 根据邀请码统计玩家数量（忽略大小写）
     *
     * @param invitation 邀请码
     * @return 玩家数量
     */
    @Override
    public int countPlayersByInvitation(String invitation) {
        return playerDao.countPlayersByInvitation(invitation);
    }

    /**
     * 根据账户名查询角色列表
     *
     * @param accountName 账户名
     * @return 角色列表
     */
    @Override
    public List<Character> selectCharactersByAccount(String accountName) {
        return playerDao.selectCharactersByAccount(accountName);
    }

    /**
     * 根据账户名列表查询在线角色信息
     *
     * @param accountNames 账户名列表
     * @return 在线角色列表
     */
    @Override
    public List<Character> selectOnlineCharactersByAccounts(List<String> accountNames) {
        return playerDao.selectOnlineCharactersByAccounts(accountNames);
    }
}
