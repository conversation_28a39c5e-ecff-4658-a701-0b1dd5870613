package com.jeethink.project.system.player.service;

import com.jeethink.project.system.player.domain.UserAccount;
import com.jeethink.project.system.player.domain.Character;

import java.util.List;
import java.util.Map;

/**
 * 玩家数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
public interface IPlayerService {

    /**
     * 查询玩家列表
     * 
     * @param userAccount 玩家信息
     * @return 玩家集合
     */
    public List<UserAccount> selectPlayerList(UserAccount userAccount);

    /**
     * 查询玩家列表（带注册时间）
     *
     * @param userAccount 玩家信息
     * @return 玩家集合
     */
    public List<UserAccount> selectPlayerListWithRegDate(UserAccount userAccount);

    /**
     * 查询玩家列表（带注册时间，支持分页）
     *
     * @param userAccount 玩家信息
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 玩家集合
     */
    public List<UserAccount> selectPlayerListWithRegDate(UserAccount userAccount, Integer pageNum, Integer pageSize);

    /**
     * 统计玩家总数（用于分页）
     *
     * @param userAccount 玩家信息
     * @return 总记录数
     */
    public int countPlayerListWithRegDate(UserAccount userAccount);

    /**
     * 根据邀请码查询玩家列表（忽略大小写）
     * 
     * @param invitation 邀请码
     * @return 玩家集合
     */
    public List<UserAccount> selectPlayerListByInvitation(String invitation);

    /**
     * 根据邀请码查询玩家列表（带注册时间，忽略大小写）
     * 
     * @param invitation 邀请码
     * @return 玩家集合
     */
    public List<UserAccount> selectPlayerListByInvitationWithRegDate(String invitation);

    /**
     * 获取玩家统计信息
     * 
     * @return 统计信息Map
     */
    public Map<String, Object> getPlayerStats();

    /**
     * 根据邀请码获取玩家统计信息（忽略大小写）
     * 
     * @param invitation 邀请码
     * @return 统计信息Map
     */
    public Map<String, Object> getPlayerStatsByInvitation(String invitation);

    /**
     * 统计当前在线玩家数量
     * 
     * @return 在线玩家数量
     */
    public int countOnlinePlayers();

    /**
     * 统计指定邀请码的在线玩家数量（忽略大小写）
     * 
     * @param invitation 邀请码
     * @return 在线玩家数量
     */
    public int countOnlinePlayersByInvitation(String invitation);

    /**
     * 统计今日注册账号数量
     * 
     * @return 今日注册数量
     */
    public int countTodayRegistrations();

    /**
     * 统计指定邀请码今日注册账号数量（忽略大小写）
     * 
     * @param invitation 邀请码
     * @return 今日注册数量
     */
    public int countTodayRegistrationsByInvitation(String invitation);

    /**
     * 根据账户名查询玩家信息
     * 
     * @param account 账户名
     * @return 玩家信息
     */
    public UserAccount selectPlayerByAccount(String account);

    /**
     * 查询所有邀请码列表（去重）
     * 
     * @return 邀请码列表
     */
    public List<String> selectAllInvitations();

    /**
     * 根据邀请码统计玩家数量（忽略大小写）
     *
     * @param invitation 邀请码
     * @return 玩家数量
     */
    public int countPlayersByInvitation(String invitation);

    /**
     * 根据账户名查询角色列表
     *
     * @param accountName 账户名
     * @return 角色列表
     */
    public List<Character> selectCharactersByAccount(String accountName);

    /**
     * 根据账户名列表查询在线角色信息
     *
     * @param accountNames 账户名列表
     * @return 在线角色列表
     */
    public List<Character> selectOnlineCharactersByAccounts(List<String> accountNames);
}
