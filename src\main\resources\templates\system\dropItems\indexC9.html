<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('芙蕾雅掉宝查询')" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>物品名称：</label>
                            <input type="text" name="wpname" id="wpname" placeholder="请输入物品名称" />
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="selectlistWpname()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table">
                <div class="ibox float-e-margins">
                    <table class="table table-hover no-margins">
                        <thead>
                        <tr>
                            <th>物品完整名称</th>
                            <th>物品属性</th>
                            <th>物品说明</th>
                        </tr>
                        </thead>
                        <tbody id ="pay_sum_id">

                        </tbody>
                    </table>
                </div>
            </table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var prefix = ctx + "C9";
    $(function() {
        selectlistWpname();
    });
    function selectlistWpname(){
        var wpname=document.getElementById("wpname").value;
        $.ajax({
            type:"post",
            url: prefix + "/listWpnameC9",
            dataType:"json",
            data:{"wpname":wpname},
            success:function (res) {
                var vtable='';
                var name="";
                for(var i in res){
                    name = res[i].wpname;
                    name=encodeURI(unescape(name));
                    vtable=vtable+"<tr><td ><a href='/C9/selectC92?wpname="+name+"&wpid="+parseInt(res[i].wpid)+"' target='_blank'>"+res[i].wpname+"</a></td><td>"+res[i].wpatt+"</td><td>"+res[i].wpshows+"</td></tr>";

                }
                document.getElementById("pay_sum_id").innerHTML=vtable;

            }

        });
    }

</script>
</body>
</html>