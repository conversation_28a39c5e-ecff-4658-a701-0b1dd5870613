package com.jeethink.project.system.dropItems.domain;

import com.jeethink.framework.web.domain.BaseEntity;
/**
 * 物品掉落查询对象
 * */
public class DropItems extends BaseEntity {
    private  int wpid;
    private  String wpname;
    private  String wpatt;//物品属性
    private  String wpshows;//物品说明
    private  String gsname;
    private  String gslevel;
    private  int mindorps;
    private  int maxdorps;
    private  String dorpsgl;
    private  int minre;
    private  int maxre;
    private  String regl;

    public int getWpid() {
        return wpid;
    }

    public void setWpid(int wpid) {
        this.wpid = wpid;
    }

    public String getWpname() {
        return wpname;
    }

    public void setWpname(String wpname) {
        this.wpname = wpname;
    }

    public String getWpatt() {
        return wpatt;
    }
    public void setWpatt(String wpatt) {
        this.wpatt = wpatt;
    }

    public String getWpshows() {
        return wpshows;
    }
    public void setWpshows(String wpshows) {
        this.wpshows = wpshows;
    }

    public String getGsname() {
        return gsname;
    }

    public void setGsname(String gsname) {
        this.gsname = gsname;
    }

    public String getGslevel() {
        return gslevel;
    }

    public void setGslevel(String gslevel) {
        this.gslevel = gslevel;
    }

    public int getMindorps() {
        return mindorps;
    }

    public void setMindorps(int mindorps) {
        this.mindorps = mindorps;
    }

    public int getMaxdorps() {
        return maxdorps;
    }

    public void setMaxdorps(int maxdorps) {
        this.maxdorps = maxdorps;
    }

    public String getDorpsgl() {
        return dorpsgl;
    }

    public void setDorpsgl(String dorpsgl) {
        this.dorpsgl = dorpsgl;
    }

    public int getMinre() {
        return minre;
    }

    public void setMinre(int minre) {
        this.minre = minre;
    }

    public int getMaxre() {
        return maxre;
    }

    public void setMaxre(int maxre) {
        this.maxre = maxre;
    }

    public String getRegl() {
        return regl;
    }

    public void setRegl(String regl) {
        this.regl = regl;
    }
}
