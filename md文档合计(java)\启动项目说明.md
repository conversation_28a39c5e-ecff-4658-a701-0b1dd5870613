# JeeThink 项目启动说明

## 环境要求

- **操作系统**: Windows/Linux/MacOS
- **JDK**: Java 17
- **数据库**: MySQL
- **构建工具**: Maven

## 数据库配置

项目使用 MySQL 数据库，在启动前需要配置好数据库连接。配置文件位于 `src/main/resources/application-druid.yml`：

```yaml
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        druid:
            # 主库数据源
            master:
                url: *************************************************************************************************************************************************
                username: root
                password: jad198611
```

请根据实际情况修改数据库连接 URL、用户名和密码。

## 项目启动方式

### Windows 环境下启动

#### 方式一：使用批处理脚本启动

1. 进入项目根目录下的 `bin` 文件夹
2. 双击运行 `run-tomcat.bat` 脚本

```
bin/run-tomcat.bat
```

这将使用 Spring Boot 内嵌的 Tomcat 启动项目。

#### 方式二：使用 Maven 命令启动

在项目根目录下执行：

```bash
mvn clean spring-boot:run -Dmaven.test.skip=true
```

### Linux/MacOS 环境下启动

#### 方式一：使用 Shell 脚本启动

1. 确保 `jt.sh` 脚本有执行权限：
   ```bash
   chmod +x jt.sh
   ```

2. 执行启动命令：
   ```bash
   ./jt.sh start
   ```

其他可用命令：
- 停止服务：`./jt.sh stop`
- 重启服务：`./jt.sh restart`
- 查看状态：`./jt.sh status`

#### 方式二：使用 Maven 命令启动

```bash
mvn clean spring-boot:run -Dmaven.test.skip=true
```

## 项目打包

### Windows 环境

在 `bin` 目录下运行 `package.bat`：

```
bin/package.bat
```

### Linux/MacOS 环境

在项目根目录下执行：

```bash
mvn clean package -Dmaven.test.skip=true
```

打包后的文件位于 `target` 目录下。

## 访问项目

项目启动成功后，可通过以下地址访问：

```
http://localhost:8080
```

默认端口为 8080，可在 `application.yml` 中修改：

```yaml
server:
  port: 8080
```

## 常见问题

1. **端口占用**：如果 8080 端口被占用，可以在 `application.yml` 中修改 `server.port` 配置。

2. **数据库连接失败**：检查数据库配置是否正确，确保 MySQL 服务已启动。

3. **内存不足**：可以调整 JVM 参数，在 Windows 下修改 `bin/run-tomcat.bat` 中的 `MAVEN_OPTS`，在 Linux 下修改 `jt.sh` 中的 `JVM_OPTS`。 