package com.jeethink.project.system.player.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jeethink.framework.aspectj.lang.annotation.Excel;
import com.jeethink.framework.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 用户注册信息对象 ssn
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
public class Ssn extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** SSN */
    private String ssn;

    /** 用户名 */
    @Excel(name = "用户名")
    private String name;

    /** 邮箱 */
    @Excel(name = "邮箱")
    private String email;

    /** 新闻订阅 */
    private Integer newsletter;

    /** 职业 */
    private Integer job;

    /** 电话 */
    private String phone;

    /** 手机 */
    private String mobile;

    /** 注册日期 */
    @Excel(name = "注册日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date regDate;

    /** 邮编 */
    private String zip;

    /** 主要地址 */
    private String addrMain;

    /** 其他地址 */
    private String addrEtc;

    /** 账户数量 */
    private Integer accountNum;

    /** 状态标志 */
    private Integer statusFlag;

    /** 最终新闻日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finalNewsDate;

    /** 主账户 */
    private String master;

    /** 有效邮箱日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date validEmailDate;

    /** 最终主账户日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finalMasterDate;

    public void setSsn(String ssn) {
        this.ssn = ssn;
    }

    public String getSsn() {
        return ssn;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEmail() {
        return email;
    }

    public void setNewsletter(Integer newsletter) {
        this.newsletter = newsletter;
    }

    public Integer getNewsletter() {
        return newsletter;
    }

    public void setJob(Integer job) {
        this.job = job;
    }

    public Integer getJob() {
        return job;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhone() {
        return phone;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobile() {
        return mobile;
    }

    public void setRegDate(Date regDate) {
        this.regDate = regDate;
    }

    public Date getRegDate() {
        return regDate;
    }

    public void setZip(String zip) {
        this.zip = zip;
    }

    public String getZip() {
        return zip;
    }

    public void setAddrMain(String addrMain) {
        this.addrMain = addrMain;
    }

    public String getAddrMain() {
        return addrMain;
    }

    public void setAddrEtc(String addrEtc) {
        this.addrEtc = addrEtc;
    }

    public String getAddrEtc() {
        return addrEtc;
    }

    public void setAccountNum(Integer accountNum) {
        this.accountNum = accountNum;
    }

    public Integer getAccountNum() {
        return accountNum;
    }

    public void setStatusFlag(Integer statusFlag) {
        this.statusFlag = statusFlag;
    }

    public Integer getStatusFlag() {
        return statusFlag;
    }

    public void setFinalNewsDate(Date finalNewsDate) {
        this.finalNewsDate = finalNewsDate;
    }

    public Date getFinalNewsDate() {
        return finalNewsDate;
    }

    public void setMaster(String master) {
        this.master = master;
    }

    public String getMaster() {
        return master;
    }

    public void setValidEmailDate(Date validEmailDate) {
        this.validEmailDate = validEmailDate;
    }

    public Date getValidEmailDate() {
        return validEmailDate;
    }

    public void setFinalMasterDate(Date finalMasterDate) {
        this.finalMasterDate = finalMasterDate;
    }

    public Date getFinalMasterDate() {
        return finalMasterDate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("ssn", getSsn())
            .append("name", getName())
            .append("email", getEmail())
            .append("newsletter", getNewsletter())
            .append("job", getJob())
            .append("phone", getPhone())
            .append("mobile", getMobile())
            .append("regDate", getRegDate())
            .append("zip", getZip())
            .append("addrMain", getAddrMain())
            .append("addrEtc", getAddrEtc())
            .append("accountNum", getAccountNum())
            .append("statusFlag", getStatusFlag())
            .append("finalNewsDate", getFinalNewsDate())
            .append("master", getMaster())
            .append("validEmailDate", getValidEmailDate())
            .append("finalMasterDate", getFinalMasterDate())
            .toString();
    }
}
