package com.jeethink.project.system.goods.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jeethink.project.system.goods.mapper.GoodsMapper;
import com.jeethink.project.system.goods.domain.Goods;
import com.jeethink.project.system.goods.service.IGoodsService;
import com.jeethink.common.utils.text.Convert;

/**
 * 物品信息维护Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-02-01
 */
@Service
public class GoodsServiceImpl implements IGoodsService 
{
    @Autowired
    private GoodsMapper goodsMapper;

    /**
     * 查询物品信息维护
     * 
     * @param gid 物品信息维护ID
     * @return 物品信息维护
     */
    @Override
    public Goods selectGoodsById(Long gid)
    {
        return goodsMapper.selectGoodsById(gid);
    }

    /**
     * 查询物品信息维护列表
     * 
     * @param goods 物品信息维护
     * @return 物品信息维护
     */
    @Override
    public List<Goods> selectGoodsList(Goods goods)
    {
        return goodsMapper.selectGoodsList(goods);
    }
    /**
     * 查询物品信息下拉列表
     *
     * @param goods 物品信息维护
     * @return 物品信息维护
     */
    @Override
    public List<Goods> selectGoodsAll(Goods g)
    {
        List<Goods> listph = goodsMapper.selectGoodsAll(g);
        String wpname="";
        for(Goods goods : listph ){
            if(goods!=null){
                wpname = goods.getGid()+"-"+goods.getGname();
                goods.setGname(wpname);
            }
        }
        return listph;
    }
    /**
     * 根据发货信息id查询物品信息下拉列表
     *
     * @param goods 物品信息维护
     * @return 物品信息维护
     */
    @Override
    public List<Goods> selectGoodByShId(Long sid)
    {
        List<Goods> listph=goodsMapper.selectGoodByShId(sid);
        Goods g =new Goods();
        List<Goods> goodss = selectGoodsAll(g);
        if(listph!=null&&listph.size()>0){
            for (Goods goods : goodss)
            {
                for (Goods pGoods : listph)
                {
                    if(pGoods !=null){
                        if (goods.getGid().longValue() == pGoods.getGid().longValue())
                        {
                            goods.setFlag(true);
                            break;
                        }
                    }

                }
            }
        }
        return goodss;
    }

    /**
     * 新增物品信息维护
     * 
     * @param goods 物品信息维护
     * @return 结果
     */
    @Override
    public int insertGoods(Goods goods)
    {
        return goodsMapper.insertGoods(goods);
    }

    /**
     * 修改物品信息维护
     * 
     * @param goods 物品信息维护
     * @return 结果
     */
    @Override
    public int updateGoods(Goods goods)
    {
        return goodsMapper.updateGoods(goods);
    }

    /**
     * 删除物品信息维护对象
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteGoodsByIds(String ids)
    {
        return goodsMapper.deleteGoodsByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除物品信息维护信息
     * 
     * @param gid 物品信息维护ID
     * @return 结果
     */
    @Override
    public int deleteGoodsById(Long gid)
    {
        return goodsMapper.deleteGoodsById(gid);
    }
}
