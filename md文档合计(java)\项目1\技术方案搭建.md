现在有一个前后端结合的需求
# 已知：
## 当前的环境：
1. 当前的项目已经可以运行，是一个前后端结合的项目
2. 项目是基于jeethink框架开发的
3. 项目是java开发的，前端是thymeleaf
4. 项目是springboot框架的
5. 项目是maven管理的
6. 项目是mysql数据库的
7. 项目是shiro权限管理的
8. 项目是mybatis框架的
9. 项目是druid数据源的

## 当前的数据库：
数据库的介绍：
项目中已经有了mysql的连接，在接下来的编程过程中，请不要影响已经有了的Mysql的连接代码配置
sql server数据库测试程序：（src\main\java\com\jeethink\common\utils\DatabaseConnectionTest.java）
我想让你连接到这个SQL server数据库：
$dbnm['DB'] = 'lin2db';
$host['DB'] = '***********';
$port['DB'] = 1433;
$user['DB'] = 'sa';
$pass['DB'] = 'bgroigmroAD147258JFDJGBHjhdhf';
这个里面最重要的表就是：user_account
我已经为你连上了数据库MCP，你可以随时查询这个数据库的相关的信息

## 数据库需求：
现在的数据库需求是要加上SQL server数据库参与项目（当前的需求是连接上一个sqlserver的数据库lin2db，其实在以后的需求中，还有相同数据库主机的另外两个sqlserver数据库，所以你注意代码的可拓展性）

## 后端代码需求：
提供接口

## 前端代码需求：
在左侧列表加上一个选项“注册支付统计”



已知：
主播和邀请码和JT账号是一一对应的关系
我想实现的是：
在admin进入8080的时候，可以看到全部的订单，充值量等数据
在主播进入8080的时候，可以通过自己的邀请码，
看到属于自己的邀请码的用户的，总的充值量，
注册的信息
