package com.jeethink.project.system.player.dao;

import com.jeethink.project.system.player.domain.UserAccount;
import com.jeethink.project.system.player.domain.Character;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 玩家数据访问层
 * 使用JdbcTemplate操作SQL Server数据库
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@Repository
public class PlayerDao {

    @Autowired
    @Qualifier("sqlServerJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Autowired
    @Qualifier("lin2worldJdbcTemplate")
    private JdbcTemplate lin2worldJdbcTemplate;

    /**
     * UserAccount的RowMapper
     */
    private final RowMapper<UserAccount> userAccountRowMapper = new RowMapper<UserAccount>() {
        @Override
        public UserAccount mapRow(ResultSet rs, int rowNum) throws SQLException {
            UserAccount userAccount = new UserAccount();
            userAccount.setUid(rs.getInt("uid"));
            userAccount.setAccount(rs.getString("account"));
            userAccount.setPayStat(rs.getInt("pay_stat"));
            userAccount.setLoginFlag(rs.getInt("login_flag"));
            userAccount.setWarnFlag(rs.getInt("warn_flag"));
            userAccount.setBlockFlag(rs.getInt("block_flag"));
            userAccount.setBlockFlag2(rs.getInt("block_flag2"));
            userAccount.setBlockEndDate(rs.getTimestamp("block_end_date"));
            userAccount.setLastLogin(rs.getTimestamp("last_login"));
            userAccount.setLastLogout(rs.getTimestamp("last_logout"));
            userAccount.setSubscriptionFlag(rs.getInt("subscription_flag"));
            userAccount.setLastWorld(rs.getInt("last_world"));
            userAccount.setLastGame(rs.getInt("last_game"));
            userAccount.setLastIp(rs.getString("last_ip"));
            userAccount.setTelephone(rs.getString("telephone"));
            userAccount.setInvitation(rs.getString("invitation"));
            userAccount.setOnlineStatus(rs.getString("onlineStatus"));

            // 处理注册时间（可能为null）
            try {
                userAccount.setRegDate(rs.getTimestamp("reg_date"));
            } catch (SQLException e) {
                // 如果没有reg_date字段，忽略异常
            }

            return userAccount;
        }
    };

    /**
     * 查询玩家列表（带注册时间关联查询）
     *
     * @param userAccount 查询条件
     * @return 玩家集合
     */
    public List<UserAccount> selectPlayerListWithRegDate(UserAccount userAccount) {
        return selectPlayerListWithRegDate(userAccount, null, null);
    }

    /**
     * 查询玩家列表（带注册时间关联查询，支持分页）
     *
     * @param userAccount 查询条件
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页大小
     * @return 玩家集合
     */
    public List<UserAccount> selectPlayerListWithRegDate(UserAccount userAccount, Integer pageNum, Integer pageSize) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ua.uid, ua.account, ua.pay_stat, ua.login_flag, ua.warn_flag, ");
        sql.append("       ua.block_flag, ua.block_flag2, ua.block_end_date, ua.last_login, ");
        sql.append("       ua.last_logout, ua.subscription_flag, ua.last_world, ua.last_game, ");
        sql.append("       ua.last_ip, ua.telephone, ua.invitation, ");
        sql.append("       CASE ");
        sql.append("           WHEN ua.last_login IS NULL OR ua.last_logout IS NULL OR ua.last_ip IS NULL THEN '尚未正式参与游戏' ");
        sql.append("           WHEN ua.last_login > ua.last_logout THEN '在线' ");
        sql.append("           ELSE '离线' ");
        sql.append("       END as onlineStatus, ");
        sql.append("       s.reg_date ");
        sql.append("FROM user_account ua ");
        sql.append("LEFT JOIN ssn s ON ua.account = s.name ");
        sql.append("WHERE 1=1 ");

        List<Object> params = new ArrayList<>();

        if (userAccount.getAccount() != null && !userAccount.getAccount().isEmpty()) {
            sql.append("AND ua.account LIKE ? ");
            params.add("%" + userAccount.getAccount() + "%");
        }

        if (userAccount.getInvitation() != null && !userAccount.getInvitation().isEmpty()) {
            sql.append("AND UPPER(ua.invitation) = UPPER(?) ");
            params.add(userAccount.getInvitation());
        }

        if (userAccount.getLastIp() != null && !userAccount.getLastIp().isEmpty()) {
            sql.append("AND ua.last_ip LIKE ? ");
            params.add("%" + userAccount.getLastIp() + "%");
        }

        // 添加在线状态过滤条件
        if (userAccount.getOnlineStatus() != null && !userAccount.getOnlineStatus().isEmpty()) {
            if ("在线".equals(userAccount.getOnlineStatus())) {
                sql.append("AND ua.last_login > ua.last_logout ");
                sql.append("AND ua.last_login IS NOT NULL ");
                sql.append("AND ua.last_logout IS NOT NULL ");
                sql.append("AND ua.last_ip IS NOT NULL ");
            } else if ("离线".equals(userAccount.getOnlineStatus())) {
                sql.append("AND ua.last_login <= ua.last_logout ");
                sql.append("AND ua.last_login IS NOT NULL ");
                sql.append("AND ua.last_logout IS NOT NULL ");
                sql.append("AND ua.last_ip IS NOT NULL ");
            } else if ("尚未正式参与游戏".equals(userAccount.getOnlineStatus())) {
                sql.append("AND (ua.last_login IS NULL OR ua.last_logout IS NULL OR ua.last_ip IS NULL) ");
            }
        }

        sql.append("ORDER BY ");
        sql.append("    CASE ");
        sql.append("        WHEN ua.last_login IS NULL OR ua.last_logout IS NULL OR ua.last_ip IS NULL THEN 3 ");
        sql.append("        WHEN ua.last_login > ua.last_logout THEN 1 ");
        sql.append("        ELSE 2 ");
        sql.append("    END, ");
        sql.append("    ua.last_login DESC");

        // 添加分页支持
        if (pageNum != null && pageSize != null && pageNum > 0 && pageSize > 0) {
            int offset = (pageNum - 1) * pageSize;
            sql.append(" OFFSET ").append(offset).append(" ROWS FETCH NEXT ").append(pageSize).append(" ROWS ONLY");
        }

        return jdbcTemplate.query(sql.toString(), params.toArray(), userAccountRowMapper);
    }

    /**
     * 统计玩家总数（用于分页）
     *
     * @param userAccount 查询条件
     * @return 总记录数
     */
    public int countPlayerListWithRegDate(UserAccount userAccount) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*) ");
        sql.append("FROM user_account ua ");
        sql.append("LEFT JOIN ssn s ON ua.account = s.name ");
        sql.append("WHERE 1=1 ");

        List<Object> params = new ArrayList<>();

        if (userAccount.getAccount() != null && !userAccount.getAccount().isEmpty()) {
            sql.append("AND ua.account LIKE ? ");
            params.add("%" + userAccount.getAccount() + "%");
        }

        if (userAccount.getInvitation() != null && !userAccount.getInvitation().isEmpty()) {
            sql.append("AND UPPER(ua.invitation) = UPPER(?) ");
            params.add(userAccount.getInvitation());
        }

        if (userAccount.getLastIp() != null && !userAccount.getLastIp().isEmpty()) {
            sql.append("AND ua.last_ip LIKE ? ");
            params.add("%" + userAccount.getLastIp() + "%");
        }

        // 添加在线状态过滤条件
        if (userAccount.getOnlineStatus() != null && !userAccount.getOnlineStatus().isEmpty()) {
            if ("在线".equals(userAccount.getOnlineStatus())) {
                sql.append("AND ua.last_login > ua.last_logout ");
                sql.append("AND ua.last_login IS NOT NULL ");
                sql.append("AND ua.last_logout IS NOT NULL ");
                sql.append("AND ua.last_ip IS NOT NULL ");
            } else if ("离线".equals(userAccount.getOnlineStatus())) {
                sql.append("AND ua.last_login <= ua.last_logout ");
                sql.append("AND ua.last_login IS NOT NULL ");
                sql.append("AND ua.last_logout IS NOT NULL ");
                sql.append("AND ua.last_ip IS NOT NULL ");
            } else if ("尚未正式参与游戏".equals(userAccount.getOnlineStatus())) {
                sql.append("AND (ua.last_login IS NULL OR ua.last_logout IS NULL OR ua.last_ip IS NULL) ");
            }
        }

        Integer count = jdbcTemplate.queryForObject(sql.toString(), params.toArray(), Integer.class);
        return count != null ? count : 0;
    }

    /**
     * 根据邀请码查询玩家列表（带注册时间，忽略大小写）
     * 
     * @param invitation 邀请码
     * @return 玩家集合
     */
    public List<UserAccount> selectPlayerListByInvitationWithRegDate(String invitation) {
        String sql = "SELECT ua.uid, ua.account, ua.pay_stat, ua.login_flag, ua.warn_flag, " +
                     "       ua.block_flag, ua.block_flag2, ua.block_end_date, ua.last_login, " +
                     "       ua.last_logout, ua.subscription_flag, ua.last_world, ua.last_game, " +
                     "       ua.last_ip, ua.telephone, ua.invitation, " +
                     "       CASE " +
                     "           WHEN ua.last_login IS NULL OR ua.last_logout IS NULL OR ua.last_ip IS NULL THEN '尚未正式参与游戏' " +
                     "           WHEN ua.last_login > ua.last_logout THEN '在线' " +
                     "           ELSE '离线' " +
                     "       END as onlineStatus, " +
                     "       s.reg_date " +
                     "FROM user_account ua " +
                     "LEFT JOIN ssn s ON ua.account = s.name " +
                     "WHERE UPPER(ua.invitation) = UPPER(?) " +
                     "ORDER BY " +
                     "    CASE " +
                     "        WHEN ua.last_login IS NULL OR ua.last_logout IS NULL OR ua.last_ip IS NULL THEN 3 " +
                     "        WHEN ua.last_login > ua.last_logout THEN 1 " +
                     "        ELSE 2 " +
                     "    END, " +
                     "    ua.last_login DESC";

        return jdbcTemplate.query(sql, new Object[]{invitation}, userAccountRowMapper);
    }

    /**
     * 统计当前在线玩家数量
     * 
     * @return 在线玩家数量
     */
    public int countOnlinePlayers() {
        String sql = "SELECT COUNT(*) FROM user_account " +
                     "WHERE last_login > last_logout " +
                     "AND last_login IS NOT NULL " +
                     "AND last_logout IS NOT NULL " +
                     "AND last_ip IS NOT NULL";

        Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
        return count != null ? count : 0;
    }

    /**
     * 统计指定邀请码的在线玩家数量（忽略大小写）
     * 
     * @param invitation 邀请码
     * @return 在线玩家数量
     */
    public int countOnlinePlayersByInvitation(String invitation) {
        String sql = "SELECT COUNT(*) FROM user_account " +
                     "WHERE last_login > last_logout " +
                     "AND last_login IS NOT NULL " +
                     "AND last_logout IS NOT NULL " +
                     "AND last_ip IS NOT NULL " +
                     "AND UPPER(invitation) = UPPER(?)";

        Integer count = jdbcTemplate.queryForObject(sql, new Object[]{invitation}, Integer.class);
        return count != null ? count : 0;
    }

    /**
     * 统计今日注册账号数量
     * 
     * @return 今日注册数量
     */
    public int countTodayRegistrations() {
        String sql = "SELECT COUNT(*) FROM ssn " +
                     "WHERE CAST(reg_date AS DATE) = CAST(GETDATE() AS DATE)";
        
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
        return count != null ? count : 0;
    }

    /**
     * 统计指定邀请码今日注册账号数量（忽略大小写）
     * 
     * @param invitation 邀请码
     * @return 今日注册数量
     */
    public int countTodayRegistrationsByInvitation(String invitation) {
        String sql = "SELECT COUNT(*) FROM ssn s " +
                     "INNER JOIN user_account ua ON s.name = ua.account " +
                     "WHERE CAST(s.reg_date AS DATE) = CAST(GETDATE() AS DATE) " +
                     "AND UPPER(ua.invitation) = UPPER(?)";
        
        Integer count = jdbcTemplate.queryForObject(sql, new Object[]{invitation}, Integer.class);
        return count != null ? count : 0;
    }

    /**
     * 根据账户名查询玩家信息
     * 
     * @param account 账户名
     * @return 玩家信息
     */
    public UserAccount selectPlayerByAccount(String account) {
        String sql = "SELECT ua.uid, ua.account, ua.pay_stat, ua.login_flag, ua.warn_flag, " +
                     "       ua.block_flag, ua.block_flag2, ua.block_end_date, ua.last_login, " +
                     "       ua.last_logout, ua.subscription_flag, ua.last_world, ua.last_game, " +
                     "       ua.last_ip, ua.telephone, ua.invitation, " +
                     "       CASE " +
                     "           WHEN ua.last_login IS NULL OR ua.last_logout IS NULL OR ua.last_ip IS NULL THEN '尚未正式参与游戏' " +
                     "           WHEN ua.last_login > ua.last_logout THEN '在线' " +
                     "           ELSE '离线' " +
                     "       END as onlineStatus, " +
                     "       s.reg_date " +
                     "FROM user_account ua " +
                     "LEFT JOIN ssn s ON ua.account = s.name " +
                     "WHERE ua.account = ?";

        List<UserAccount> results = jdbcTemplate.query(sql, new Object[]{account}, userAccountRowMapper);
        
        return results.isEmpty() ? null : results.get(0);
    }

    /**
     * 查询所有邀请码列表（去重）
     * 
     * @return 邀请码列表
     */
    public List<String> selectAllInvitations() {
        String sql = "SELECT DISTINCT invitation FROM user_account " +
                     "WHERE invitation IS NOT NULL AND invitation != '' " +
                     "ORDER BY invitation";
        
        return jdbcTemplate.queryForList(sql, String.class);
    }

    /**
     * 根据邀请码统计玩家数量（忽略大小写）
     *
     * @param invitation 邀请码
     * @return 玩家数量
     */
    public int countPlayersByInvitation(String invitation) {
        String sql = "SELECT COUNT(*) FROM user_account " +
                     "WHERE UPPER(invitation) = UPPER(?)";

        Integer count = jdbcTemplate.queryForObject(sql, new Object[]{invitation}, Integer.class);
        return count != null ? count : 0;
    }

    /**
     * Character的RowMapper
     */
    private final RowMapper<Character> characterRowMapper = new RowMapper<Character>() {
        @Override
        public Character mapRow(ResultSet rs, int rowNum) throws SQLException {
            Character character = new Character();
            character.setCharId(rs.getInt("char_id"));
            character.setCharName(rs.getString("char_name"));
            character.setAccountName(rs.getString("account_name"));
            character.setAccountId(rs.getInt("account_id"));
            character.setLevel(rs.getInt("Lev"));
            character.setCharClass(rs.getInt("class"));
            character.setGender(rs.getInt("gender"));
            character.setRace(rs.getInt("race"));
            character.setLogin(rs.getTimestamp("login"));
            character.setLogout(rs.getTimestamp("logout"));
            character.setUseTime(rs.getInt("use_time"));
            character.setCreateDate(rs.getTimestamp("create_date"));
            character.setOnlineStatus(rs.getString("online_status"));
            character.setUseTimeFormatted(rs.getString("use_time_formatted"));
            return character;
        }
    };

    /**
     * 根据账户名查询角色列表
     *
     * @param accountName 账户名
     * @return 角色列表
     */
    public List<Character> selectCharactersByAccount(String accountName) {
        String sql = "SELECT char_id, char_name, account_name, account_id, Lev, class, gender, race, " +
                     "       login, logout, use_time, create_date, " +
                     "       CASE " +
                     "           WHEN login > logout OR logout IS NULL THEN '在线' " +
                     "           ELSE '离线' " +
                     "       END as online_status, " +
                     "       CASE " +
                     "           WHEN use_time >= 3600 THEN CAST(use_time / 3600 AS VARCHAR) + '小时' + CAST((use_time % 3600) / 60 AS VARCHAR) + '分钟' " +
                     "           WHEN use_time >= 60 THEN CAST(use_time / 60 AS VARCHAR) + '分钟' + CAST(use_time % 60 AS VARCHAR) + '秒' " +
                     "           ELSE CAST(use_time AS VARCHAR) + '秒' " +
                     "       END as use_time_formatted " +
                     "FROM user_data " +
                     "WHERE account_name = ? " +
                     "ORDER BY " +
                     "    CASE " +
                     "        WHEN login > logout OR logout IS NULL THEN 1 " +
                     "        ELSE 2 " +
                     "    END, " +
                     "    login DESC";

        return lin2worldJdbcTemplate.query(sql, new Object[]{accountName}, characterRowMapper);
    }

    /**
     * 根据账户名列表查询在线角色信息
     *
     * @param accountNames 账户名列表
     * @return 在线角色列表
     */
    public List<Character> selectOnlineCharactersByAccounts(List<String> accountNames) {
        if (accountNames == null || accountNames.isEmpty()) {
            return new ArrayList<>();
        }

        // 构建IN子句的占位符
        String placeholders = String.join(",", Collections.nCopies(accountNames.size(), "?"));

        String sql = "SELECT char_name, account_name, Lev, use_time, login, logout, " +
                     "       CASE " +
                     "           WHEN use_time >= 3600 THEN CAST(use_time / 3600 AS VARCHAR) + '小时' + CAST((use_time % 3600) / 60 AS VARCHAR) + '分钟' " +
                     "           WHEN use_time >= 60 THEN CAST(use_time / 60 AS VARCHAR) + '分钟' + CAST(use_time % 60 AS VARCHAR) + '秒' " +
                     "           ELSE CAST(use_time AS VARCHAR) + '秒' " +
                     "       END as use_time_formatted " +
                     "FROM user_data " +
                     "WHERE account_name IN (" + placeholders + ") " +
                     "  AND (login > logout OR logout IS NULL) " +
                     "ORDER BY login DESC";

        return lin2worldJdbcTemplate.query(sql, accountNames.toArray(), onlineCharacterRowMapper);
    }

    /**
     * 在线角色的RowMapper
     */
    private final RowMapper<Character> onlineCharacterRowMapper = new RowMapper<Character>() {
        @Override
        public Character mapRow(ResultSet rs, int rowNum) throws SQLException {
            Character character = new Character();
            character.setCharName(rs.getString("char_name"));
            character.setAccountName(rs.getString("account_name"));
            character.setLevel(rs.getInt("Lev"));
            character.setUseTime(rs.getInt("use_time"));
            character.setLogin(rs.getTimestamp("login"));
            character.setLogout(rs.getTimestamp("logout"));
            character.setUseTimeFormatted(rs.getString("use_time_formatted"));
            character.setOnlineStatus("在线"); // 查询条件已确保都是在线的
            return character;
        }
    };
}
