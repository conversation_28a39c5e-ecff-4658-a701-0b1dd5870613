package com.jeethink.project.system.plaa.mapper;

import java.util.List;
import com.jeethink.project.system.plaa.domain.Plaa;

/**
 * 利润分成Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-01
 */
public interface PlaaMapper 
{
    /**
     * 查询利润分成
     * 
     * @param postId 利润分成ID
     * @return 利润分成
     */
    public Plaa selectPlaaById(Long postId);

    /**
     * 查询利润分成列表
     * 
     * @param plaa 利润分成
     * @return 利润分成集合
     */
    public List<Plaa> selectPlaaList(Plaa plaa);

    /**
     * 新增利润分成
     * 
     * @param plaa 利润分成
     * @return 结果
     */
    public int insertPlaa(Plaa plaa);

    /**
     * 修改利润分成
     * 
     * @param plaa 利润分成
     * @return 结果
     */
    public int updatePlaa(Plaa plaa);

    /**
     * 删除利润分成
     * 
     * @param postId 利润分成ID
     * @return 结果
     */
    public int deletePlaaById(Long postId);

    /**
     * 批量删除利润分成
     * 
     * @param postIds 需要删除的数据ID
     * @return 结果
     */
    public int deletePlaaByIds(String[] postIds);
}
