-- 查询现有菜单结构，找到合适的父菜单ID

-- 1. 查找所有包含"分成"、"利润"、"代理"关键词的菜单
SELECT menu_id, menu_name, parent_id, order_num, url, perms, menu_type, visible
FROM sys_menu 
WHERE menu_name LIKE '%分成%' 
   OR menu_name LIKE '%利润%' 
   OR menu_name LIKE '%代理%'
   OR menu_name LIKE '%paysystem%'
   OR menu_name LIKE '%plaa%'
ORDER BY parent_id, order_num;

-- 2. 查找顶级菜单（parent_id = 0）
SELECT menu_id, menu_name, parent_id, order_num, url, perms, menu_type, visible
FROM sys_menu 
WHERE parent_id = 0
ORDER BY order_num;

-- 3. 查找二级菜单（可能的父菜单）
SELECT menu_id, menu_name, parent_id, order_num, url, perms, menu_type, visible
FROM sys_menu 
WHERE parent_id IN (
    SELECT menu_id FROM sys_menu WHERE parent_id = 0
)
ORDER BY parent_id, order_num;

-- 4. 查找所有菜单的层级结构
SELECT 
    CASE 
        WHEN parent_id = 0 THEN CONCAT('├─ ', menu_name)
        WHEN parent_id IN (SELECT menu_id FROM sys_menu WHERE parent_id = 0) THEN CONCAT('│  ├─ ', menu_name)
        ELSE CONCAT('│  │  ├─ ', menu_name)
    END as menu_tree,
    menu_id,
    parent_id,
    order_num,
    url,
    menu_type
FROM sys_menu 
WHERE visible = '0'
ORDER BY 
    CASE WHEN parent_id = 0 THEN menu_id ELSE parent_id END,
    CASE WHEN parent_id = 0 THEN 0 ELSE 1 END,
    order_num;

-- 5. 查找现有的角色菜单关联（确认哪些角色有权限）
SELECT r.role_name, m.menu_name, m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.menu_name LIKE '%分成%' 
   OR m.menu_name LIKE '%利润%' 
   OR m.menu_name LIKE '%代理%'
ORDER BY r.role_name, m.menu_name;
