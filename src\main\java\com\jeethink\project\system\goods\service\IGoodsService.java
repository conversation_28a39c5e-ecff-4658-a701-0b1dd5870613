package com.jeethink.project.system.goods.service;

import java.util.List;
import com.jeethink.project.system.goods.domain.Goods;

/**
 * 物品信息维护Service接口
 * 
 * <AUTHOR>
 * @date 2024-02-01
 */
public interface IGoodsService 
{
    /**
     * 查询物品信息维护
     * 
     * @param gid 物品信息维护ID
     * @return 物品信息维护
     */
    public Goods selectGoodsById(Long gid);

    /**
     * 查询物品信息维护列表
     * 
     * @param goods 物品信息维护
     * @return 物品信息维护集合
     */
    public List<Goods> selectGoodsList(Goods goods);
    /**
     * 查询物品信息下拉列表
     *
     * @param goods 物品信息维护
     * @return 物品信息维护集合
     */
    public List<Goods> selectGoodsAll(Goods goods);
    /**
     * 根据发货信息id查询物品信息下拉列表
     *
     * @param goods 物品信息维护
     * @return 物品信息维护集合
     */
    public List<Goods> selectGoodByShId(Long sid);

    /**
     * 新增物品信息维护
     * 
     * @param goods 物品信息维护
     * @return 结果
     */
    public int insertGoods(Goods goods);

    /**
     * 修改物品信息维护
     * 
     * @param goods 物品信息维护
     * @return 结果
     */
    public int updateGoods(Goods goods);

    /**
     * 批量删除物品信息维护
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteGoodsByIds(String ids);

    /**
     * 删除物品信息维护信息
     * 
     * @param gid 物品信息维护ID
     * @return 结果
     */
    public int deleteGoodsById(Long gid);
}
