package com.jeethink.project.system.dropItems.controller;


import com.alibaba.fastjson.JSON;
import com.jeethink.framework.web.controller.BaseController;
import com.jeethink.framework.web.page.PageDomain;
import com.jeethink.framework.web.page.TableSupport;
import com.jeethink.project.system.dropItems.domain.DropItems;

import com.jeethink.project.system.goods.service.IGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.jeethink.framework.web.page.TableDataInfo;
import java.io.*;
import java.text.DecimalFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Controller
public class DropItemsController extends BaseController
{
    private String prefix = "system/dropItems";
    @Autowired
    private IGoodsService goodsService;


    //获取C6查询界面
    @GetMapping("/C6")
    public String selectC6() { return prefix + "/indexC6"; }
    //获取C4查询界面
    @GetMapping("/C4")
    public String selectC4()
    {
        return prefix + "/indexC4";
    }
    //获取C8查询界面
    @GetMapping("/C8")
    public String selectC8()
    {
        return prefix + "/indexC8";
    }
    //获取C9查询界面
    @GetMapping("/C9")
    public String selectC9()
    {
        return prefix + "/indexC9";
    }
    //获取C6
    @GetMapping("/C6/selectC62")
    public String selectC62(DropItems dropItems, ModelMap mmap)
    {
        String wpname = dropItems.getWpname();
        dropItems.setWpname(wpname);
        mmap.put("dropItems",dropItems);
        return prefix + "/indexC62";
    }

    //获取C4
    @GetMapping("/C4/selectC42")
    public String selectC42(DropItems dropItems, ModelMap mmap)
    {
        String wpname = dropItems.getWpname();
        dropItems.setWpname(wpname);
        mmap.put("dropItems",dropItems);
        return prefix + "/indexC42";
    }
    //获取C8
    @GetMapping("/C8/selectC82")
    public String selectC82(DropItems dropItems, ModelMap mmap)
    {
        String wpname = dropItems.getWpname();
        dropItems.setWpname(wpname);
        mmap.put("dropItems",dropItems);
        return prefix + "/indexC82";
    }
    //获取C9
    @GetMapping("/C9/selectC92")
    public String selectC92(DropItems dropItems, ModelMap mmap)
    {
        String wpname = dropItems.getWpname();
        dropItems.setWpname(wpname);
        mmap.put("dropItems",dropItems);
        return prefix + "/indexC92";
    }

    public List<DropItems> listWpname(String pathName1,String wuname){
        List<DropItems> list = new ArrayList<DropItems>();
        DropItems dropItems=new DropItems();
        if(wuname!=null&&wuname!=""){
            try(
                    InputStreamReader reader = new InputStreamReader(new FileInputStream(new File(pathName1)),"UTF-8");
                    BufferedReader br = new BufferedReader(reader)
            )
            {
                String line;
                while((line=br.readLine())!=null){
                    String [] lines=line.split("\t");
                    if(lines.length>3&&lines[1].contains(wuname)){
                        dropItems.setWpname(lines[1]);
                        dropItems.setWpid(Integer.parseInt(lines[0]));
                        dropItems.setWpatt(lines[2]);//物品属性
                        dropItems.setWpshows(lines[3]);//物品说明
                        list.add(dropItems);
                        dropItems=new DropItems();
                    }

                }
            }catch(IOException e){
                e.printStackTrace();
            }
        }
        return list;
    }

    @PostMapping("/C6/listWpnameC6")
    @ResponseBody
    public String listWpnameC6(DropItems dropItems)
    {
        List<DropItems> list = new ArrayList<DropItems>();
        String wuname=dropItems.getWpname();
        String fileName = System.getProperty("user.dir");//获取项目路径
        String pathName1=fileName+"/file/C6/itemname.txt";
        list =listWpname(pathName1,wuname);
        return JSON.toJSONString(list);

    }
    @PostMapping("/C4/listWpnameC4")
    @ResponseBody
    public String listWpnameC4(DropItems dropItems)
    {
        List<DropItems> list = new ArrayList<DropItems>();
        String wuname=dropItems.getWpname();
        String fileName = System.getProperty("user.dir");//获取项目路径
        String pathName1=fileName+"/file/C4/itemname.txt";
        list =listWpname(pathName1,wuname);
        return JSON.toJSONString(list);

    }
    @PostMapping("/C8/listWpnameC8")
    @ResponseBody
    public String listWpnameC8(DropItems dropItems)
    {
        List<DropItems> list = new ArrayList<DropItems>();
        String wuname=dropItems.getWpname();
        String fileName = System.getProperty("user.dir");//获取项目路径
        String pathName1=fileName+"/file/C8/itemname.txt";
        list =listWpname(pathName1,wuname);
        return JSON.toJSONString(list);

    }
    @PostMapping("/C9/listWpnameC9")
    @ResponseBody
    public String listWpnameC9(DropItems dropItems)
    {
        List<DropItems> list = new ArrayList<DropItems>();
        String wuname=dropItems.getWpname();
        String fileName =System.getProperty("user.dir");//获取项目路径
        String pathName1=fileName+"/file/C9/ItemName.txt";
        list =listWpname(pathName1,wuname);
        return JSON.toJSONString(list);

    }

    public static List readtxtByList(String pathName2,String pathName3,String pathName4,DropItems dropItems,int leverIndexNum,int dlIndexNum,int hsIndexNum){
        List<DropItems> list = new ArrayList<DropItems>();
        Map<String,String> map=new HashMap<String,String>();
        String wuname=dropItems.getWpname();
        String wpid=dropItems.getWpid()+"";
        if(dropItems!=null && dropItems.getWpname()!=null&& dropItems.getWpname()!=""){
            try(
                    InputStreamReader reader2 = new InputStreamReader(new FileInputStream(new File(pathName2)),"UTF-8");
                    BufferedReader br2 = new BufferedReader(reader2);
                    InputStreamReader reader3 = new InputStreamReader(new FileInputStream(new File(pathName3)),"UTF-8");
                    BufferedReader br3 = new BufferedReader(reader3);
                    InputStreamReader reader4 = new InputStreamReader(new FileInputStream(new File(pathName4)),"UTF-8");
                    BufferedReader br4 = new BufferedReader(reader4)

            ){
                String line2;
                String line3;
                String line4;
                Map<String,String> mp2=new HashMap<String,String>();
                while((line2=br2.readLine())!=null){
                    String [] lines=line2.split("\t");
                    if(lines.length>3){
                        mp2.put(lines[2],lines[3]);
                    }
                }
                Map<String,String> mp3=new HashMap<String,String>();
                while((line3=br3.readLine())!=null){
                    mp3.put(line3,"0");
                }
                Map<String,String> mp4=new HashMap<String,String>();
                //将怪兽信息存进map，以便循环使用
                while((line4=br4.readLine())!=null){
                    String [] ls=line4.split("\t");
                    if(ls.length>1&&ls[1].length()>1){
                        mp4.put(ls[0],ls[1]);
                    }

                }
                    for(String mp2key : mp2.keySet()){
                        if(mp2key.equals(wpid)){
                            String zdname=mp2.get(mp2key);//字段名中包含[]
                            //根据字段名获取掉落几率
                            for(String mp3key : mp3.keySet()){
                                String [] lines3=mp3key.split("\t");
                                if(lines3.length>=dlIndexNum){
                                    if(lines3[dlIndexNum].indexOf(zdname)>=0||lines3[hsIndexNum].indexOf(zdname)>=0){
                                        //当字段名存在与掉落几率或者收回几率字段中
                                        for(String key : mp4.keySet()){
                                            if(key.equals(lines3[2])){
                                                dropItems.setGsname(mp4.get(key));
                                                break;
                                            }
                                        }

                                        String level=lines3[leverIndexNum].split("=")[1];
                                        dropItems.setGslevel(level);
                                        String [] gl=lines3[dlIndexNum].split("\\{\\{\\{");//掉落几率
                                        for(int y=0;y<gl.length;y++){
                                            if(gl[y].indexOf(zdname)>=0){
                                                String [] gls=gl[y].split("};");
                                                for(int t=0;t<gls.length;t++){
                                                    if(gls[t].indexOf(zdname)>=0){
                                                        String [] glnums=gls[t].split(";");
                                                        dropItems.setMindorps( Integer.parseInt(getNumber(glnums[1])));
                                                        dropItems.setMaxdorps(Integer.parseInt(getNumber(glnums[2])));
                                                        if(!getNumber(glnums[3]).equals("")){
                                                            Double dropgl=Double.valueOf(getNumber(glnums[3]))*Double.valueOf(getNumber(gls[gls.length-1]));
                                                            DecimalFormat df =new DecimalFormat("#0.0000");
                                                            dropItems.setDorpsgl(df.format(dropgl/100)+"%");
                                                        }

                                                        break;
                                                    }

                                                }

                                            }
                                        }
                                        String [] gl2=lines3[hsIndexNum].split("\\{");//回收几率
                                        for(int y2=0;y2<gl2.length;y2++){
                                            if(gl2[y2].indexOf(zdname)>=0){
                                                String [] glnums2=gl2[y2].split(";");
                                                dropItems.setMinre(Integer.parseInt(getNumber(glnums2[1])));
                                                dropItems.setMaxre(Integer.parseInt(getNumber(glnums2[2])));
                                                dropItems.setRegl(getNumber(glnums2[3])+"%");
                                                break;
                                            }
                                        }
                                        dropItems.setWpname(wuname);
                                        list.add(dropItems);
                                        dropItems=new DropItems();

                                    }
                                }

                            }
                            break;

                        }

                    }



            }catch(IOException e){
                e.printStackTrace();
            }
        }
        return  list;
    }
    /**
     * 查C6
     */
    @PostMapping("/C6/listC6")
    @ResponseBody
    public TableDataInfo listC6(DropItems dropItems)
    {
        List<DropItems> list = new ArrayList<DropItems>();
        String fileName = System.getProperty("user.dir");//获取项目路径
        String pathName2=fileName+"/file/C6/ItemData.txt";
        String pathName3=fileName+"/file/C6/npcdata.txt";
        String pathName4=fileName+"/file/C6/NpcName.txt";
        if(dropItems!=null&&dropItems.getWpname()!=null&&dropItems.getWpname()!=""){
            list = readtxtByList(pathName2,pathName3,pathName4,dropItems,4,60,58);
        }
        return  getDatePage(list);

    }

    /**
     * 查C4
     */
    @PostMapping("/C4/listC4")
    @ResponseBody
    public TableDataInfo listC4(DropItems dropItems)
    {
        List<DropItems> list = new ArrayList<DropItems>();
        String fileName = System.getProperty("user.dir");//获取项目路径
        String pathName2=fileName+"/file/C4/itemdata.txt";
        String pathName3=fileName+"/file/C4/npcdata.txt";
        String pathName4=fileName+"/file/C4/npcname.txt";
        if(dropItems!=null&&dropItems.getWpname()!=null&&dropItems.getWpname()!=""){
            list = readtxtByList(pathName2,pathName3,pathName4,dropItems,4,60,58);
        }
        return  getDatePage(list);

    }
    /**
     * 查C8
     */
    @PostMapping("/C8/listC8")
    @ResponseBody
    public TableDataInfo listC8(DropItems dropItems)
    {
        List<DropItems> list = new ArrayList<DropItems>();
        String fileName = System.getProperty("user.dir");//获取项目路径
        String pathName2=fileName+"/file/C8/Itemdata.txt";
        String pathName3=fileName+"/file/C8/npcdata.txt";
        String pathName4=fileName+"/file/C8/npcname.txt";
        if(dropItems!=null&&dropItems.getWpname()!=null&&dropItems.getWpname()!=""){
            list = readtxtByList(pathName2,pathName3,pathName4,dropItems,5,74,72);
        }
        return  getDatePage(list);

    }
    /**
     * 查C9
     */
    @PostMapping("/C9/listC9")
    @ResponseBody
    public TableDataInfo listC9(DropItems dropItems)
    {
        List<DropItems> list = new ArrayList<DropItems>();
        String fileName = System.getProperty("user.dir");//获取项目路径
        String pathName2=fileName+"/file/C9/itemdata.txt";
        String pathName3=fileName+"/file/C9/npcdata.txt";
        String pathName4=fileName+"/file/C9/NpcName.txt";
        if(dropItems!=null&&dropItems.getWpname()!=null&&dropItems.getWpname()!=""){
            list = readtxtByList(pathName2,pathName3,pathName4,dropItems,5,75,73);
        }
        return  getDatePage(list);
    }



    public static String getNumber(String str){
        Pattern p = Pattern.compile("(\\d+\\.\\d+)");//小数正则表达式
        Matcher m =p.matcher(str);
        if(m.find()){//包含小数
            str=m.group(1)==null?"":m.group(1);
        }else{//不包含小数则匹配整数
            p = Pattern.compile("(\\d+)");//整数正则表达式
            m =p.matcher(str);
            if(m.find()){
                str=m.group(1)==null?"":m.group(1);
            }else{
                str="";
            }
        }
        return  str;
    }
    /**
     * list分页方法
     * */
    public static TableDataInfo getDatePage(List list){
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(0);
        rspData.setTotal(list.size());
        Integer startIndex=(pageNum-1)*pageSize;
        Integer endIndex = pageNum*pageSize;
        if(list.size()>=endIndex){
            list = list.subList(startIndex,endIndex);
        }else{
            list = list.subList(startIndex,list.size());
        }
        rspData.setRows(list);
        return rspData;
}

}
