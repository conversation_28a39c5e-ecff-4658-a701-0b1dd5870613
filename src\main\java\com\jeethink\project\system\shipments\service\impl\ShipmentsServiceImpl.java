package com.jeethink.project.system.shipments.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jeethink.project.system.shipments.mapper.ShipmentsMapper;
import com.jeethink.project.system.shipments.domain.Shipments;
import com.jeethink.project.system.shipments.service.IShipmentsService;
import com.jeethink.common.utils.text.Convert;

/**
 * 发货信息表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-31
 */
@Service
public class ShipmentsServiceImpl implements IShipmentsService 
{
    @Autowired
    private ShipmentsMapper shipmentsMapper;

    /**
     * 查询发货信息表
     * 
     * @param sid 发货信息表ID
     * @return 发货信息表
     */
    @Override
    public Shipments selectShipmentsById(Long sid)
    {
        return shipmentsMapper.selectShipmentsById(sid);
    }

    /**
     * 查询发货信息表列表
     * 
     * @param shipments 发货信息表
     * @return 发货信息表
     */
    @Override
    public List<Shipments> selectShipmentsList(Shipments shipments)
    {
        return shipmentsMapper.selectShipmentsList(shipments);
    }

    /**
     * 新增发货信息表
     * 
     * @param shipments 发货信息表
     * @return 结果
     */
    @Override
    public int insertShipments(Shipments shipments)
    {
        return shipmentsMapper.insertShipments(shipments);
    }

    /**
     * 修改发货信息表
     * 
     * @param shipments 发货信息表
     * @return 结果
     */
    @Override
    public int updateShipments(Shipments shipments)
    {
        return shipmentsMapper.updateShipments(shipments);
    }

    /**
     * 删除发货信息表对象
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteShipmentsByIds(String ids)
    {
        return shipmentsMapper.deleteShipmentsByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除发货信息表信息
     * 
     * @param sid 发货信息表ID
     * @return 结果
     */
    @Override
    public int deleteShipmentsById(Long sid)
    {
        return shipmentsMapper.deleteShipmentsById(sid);
    }
}
