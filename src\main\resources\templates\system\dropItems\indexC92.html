<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('芙蕾雅掉宝查询')" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId" th:object="${dropItems}">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>物品名称：</label>
                            <input type="text" th:field="*{wpname}" name="wpname"  />
                            <input type="text" name="wpid" th:field="*{wpid}"  hidden/>
                        </li>

                    </ul>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: select2-js" />
<script th:inline="javascript">

    var prefix = ctx + "C9";

    $(function() {
        var options = {
            url: prefix + "/listC9",
            modalName: "掉落概率表",
            columns: [
                {
                    field: 'wpid',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'wpname',
                    title: '物品名称'
                },
                {
                    field: 'gsname',
                    title: '怪兽名称'
                },
                {
                    field: 'gslevel',
                    title: '怪兽等级'
                },
                {
                    field: 'mindorps',
                    title: '最小掉落数'
                },
                {
                    field: 'maxdorps',
                    title: '最大掉落数'
                },
                {
                    field: 'dorpsgl',
                    title: '掉落概率'
                },
                {
                    field: 'minre',
                    title: '最小回收数'
                },
                {
                    field: 'maxre',
                    title: '最大收回数'
                },
                {
                    field: 'regl',
                    title: '回收概率'
                }]
        };
        $.table.init(options);
    });

</script>
</body>
</html>