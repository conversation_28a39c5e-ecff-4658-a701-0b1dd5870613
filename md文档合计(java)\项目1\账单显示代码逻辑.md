# 首页账单显示代码逻辑分析

## 概述
首页（main.html）显示了四个关键的账单统计数据：
- 收入（月）
- 收入（年）
- 订单总数
- 支付成功订单

## 前端页面结构

### 1. 显示元素
```html
<!-- 月收入 -->
<h1 id="monamid" class="no-margins">0</h1>

<!-- 年收入 -->
<h1 id="monamyearid" class="no-margins">0</h1>

<!-- 订单总数 -->
<h1 id="yeardd" class="no-margins">0</h1>

<!-- 支付成功订单 -->
<h1 id="yearddcg" class="no-margins">0</h1>
```

### 2. JavaScript调用逻辑
页面加载时调用以下函数：
```javascript
$(document).ready(function () {
    var prefix="/system/paysystem";
    retsebyMon(prefix);      // 查询当月收入
    retsebyYearam(prefix);   // 查询当年收入
    retsebyYear(prefix);     // 查询年总订单
    retsebyYearcg(prefix);   // 查询年支付成功订单
    selecttb(prefix);        // 查询图表数据
    selectPayAm(prefix);     // 查询代理分成
});
```

### 3. AJAX请求详情

#### 3.1 月收入查询 (retsebyMon)
- **接口**: `/system/paysystem/list2`
- **参数**: 当月时间范围 (beginCreateTime, endCreateTime)
- **更新元素**: `monamid`, `monamid2`

#### 3.2 年收入查询 (retsebyYearam)
- **接口**: `/system/paysystem/list2`
- **参数**: 当年时间范围 (beginCreateTime, endCreateTime)
- **更新元素**: `monamyearid`

#### 3.3 年订单总数查询 (retsebyYear)
- **接口**: `/system/paysystem/list`
- **参数**: 当年时间范围
- **更新元素**: `yeardd`, `yeardd2`

#### 3.4 年支付成功订单查询 (retsebyYearcg)
- **接口**: `/system/paysystem/list`
- **参数**: 当年时间范围 + status="支付成功"
- **更新元素**: `yearddcg`, `yearddcg2`

## 后端控制器逻辑

### PaySystemController 关键方法

#### 1. list() - 查询订单列表
```java
@PostMapping("/list")
public TableDataInfo list(PaySystem paySystem) {
    // 获取当前登录用户
    User u = getSysUser();

    // 权限控制：非admin用户只能看自己代理的数据
    if(!u.getLoginName().equals("admin")){
        u = userService.selectUserByLoginName(u.getLoginName());
        paySystem.setAgency(u.getInvite()); // 设置代理条件
    }

    startPage();
    List<PaySystem> list = paySystemService.selectPaySystemList(paySystem);
    return getDataTable(list);
}
```

#### 2. list2() - 查询收入总额
```java
@PostMapping("/list2")
public String list2(PaySystem paySystem) {
    // 获取当前登录用户
    User u = getSysUser();

    // 权限控制：非admin用户只能看自己代理的数据
    if(!u.getLoginName().equals("admin")){
        u = userService.selectUserByLoginName(u.getLoginName());
        paySystem.setAgency(u.getInvite()); // 设置代理条件
    }

    // 渠道代码转换
    if(paySystem.getChannelCode() != null){
        if(paySystem.getChannelCode().equals("微信")){
            paySystem.setChannelCode("WECHAT");
        }else if(paySystem.getChannelCode().equals("支付宝")){
            paySystem.setChannelCode("ALIPAY");
        }
    }

    paySystem.setStatus("支付成功");
    Integer psum = paySystemService.selectPaySum(paySystem);
    if(psum == null) psum = 0;

    paySystem.setAmount(psum.longValue());
    return JSON.toJSONString(paySystem);
}
```

## 数据库查询逻辑

### 核心SQL查询

#### 1. selectPaySystemList - 订单列表查询
```sql
SELECT id, mch_order_no, area, channel_code, amount, goods_param_ext,
       ip, status, pay_succ_time, deliver_goods, create_by, agency,
       create_time, update_by, update_time, wuDi_order_no
FROM pay
WHERE 1=1
  AND (agency = #{agency} OR agency IS NULL) -- 代理条件
  AND status = #{status}                     -- 状态条件
  AND date_format(create_time,'%y%m%d') >= date_format(#{params.beginCreateTime},'%y%m%d')
  AND date_format(create_time,'%y%m%d') <= date_format(#{params.endCreateTime},'%y%m%d')
```

#### 2. selectPaySum - 收入总额查询
```sql
SELECT SUM(amount) as amount
FROM pay
WHERE 1=1
  AND (agency = #{agency} OR agency IS NULL) -- 代理条件
  AND status = '支付成功'                    -- 只统计成功支付
  AND date_format(create_time,'%y%m%d') >= date_format(#{params.beginCreateTime},'%y%m%d')
  AND date_format(create_time,'%y%m%d') <= date_format(#{params.endCreateTime},'%y%m%d')
```

## 权限控制机制

### 用户类型区分

#### 1. Admin用户
- **登录名**: "admin"
- **数据范围**: 可以查看所有数据
- **SQL条件**: 不添加agency限制条件

#### 2. 普通员工用户
- **登录名**: 非"admin"的其他用户
- **数据范围**: 只能查看自己代理的数据
- **SQL条件**: 添加 `agency = #{user.invite}` 限制条件

### 数据来源区别

#### Admin显示的数据来源：
- **范围**: 系统中所有的订单和支付数据
- **SQL**: 不带agency过滤条件
- **统计**: 全平台的收入、订单总数、支付成功数

#### 员工显示的数据来源：
- **范围**: 仅限该员工作为代理人(agency)的订单数据
- **SQL**: 带有 `agency = #{user.invite}` 过滤条件
- **统计**: 只统计该员工代理的收入、订单总数、支付成功数

### 关键字段说明

#### User表字段：
- **loginName**: 用户登录名
- **invite**: 用户的邀请码，作为代理标识

#### Pay表字段：
- **agency**: 代理人标识，对应User表的invite字段
- **amount**: 支付金额
- **status**: 支付状态（"支付成功"/"支付失败"等）
- **create_time**: 创建时间，用于时间范围过滤

## 业务流程总结

1. **用户登录** → 获取用户信息(getSysUser())
2. **权限判断** → 检查loginName是否为"admin"
3. **数据过滤** →
   - Admin: 查询所有数据
   - 员工: 查询agency=user.invite的数据
4. **时间范围** → 根据月/年设置beginCreateTime和endCreateTime
5. **状态过滤** → 收入统计只计算status="支付成功"的记录
6. **数据返回** → 前端更新对应的显示元素

## 代码执行流程图

```
用户访问首页
    ↓
页面加载完成
    ↓
执行JavaScript初始化
    ↓
并发发起4个AJAX请求
    ├── retsebyMon() → /list2 (月收入)
    ├── retsebyYearam() → /list2 (年收入)
    ├── retsebyYear() → /list (年订单总数)
    └── retsebyYearcg() → /list (年支付成功订单)
    ↓
后端控制器处理请求
    ↓
获取当前登录用户信息
    ↓
判断用户权限
    ├── admin用户 → 查询所有数据
    └── 普通用户 → 查询agency=user.invite的数据
    ↓
执行数据库查询
    ↓
返回JSON数据给前端
    ↓
前端更新页面显示元素
```

## 安全性分析

### 1. 权限控制
- 通过检查用户登录名实现权限区分
- 非admin用户只能查看自己代理的数据
- 数据库层面通过agency字段进行过滤

### 2. 数据隔离
- 每个代理人只能看到自己的业绩数据
- Admin可以看到全平台数据
- 防止数据泄露和越权访问

### 3. 时间范围控制
- 通过beginCreateTime和endCreateTime限制查询范围
- 避免全表扫描，提高查询性能

### 4. 前端参数安全性
**重要发现**：在paysystem.html页面中，虽然前端会传递用户在搜索框中输入的agency参数，但后端有强制安全控制：

```java
// 在list2()方法中
if(!u.getLoginName().equals("admin")){
    u=userService.selectUserByLoginName(u.getLoginName());
    paySystem.setAgency(u.getInvite()); // 强制覆盖为当前用户的invite
}
```

这意味着：
- **Admin用户**：前端传递的agency参数会被使用，可以查看指定代理的数据
- **普通员工**：无论前端传递什么agency参数，后端都会强制覆盖为当前用户的邀请码
- **安全保障**：普通员工无法通过修改前端参数来查看其他代理的数据

### 5. 双重安全机制
1. **前端层面**：列表数据通过`/list`接口获取，有权限控制
2. **统计层面**：支付成功总额通过`/list2`接口计算，同样有权限控制
3. **参数覆盖**：后端强制覆盖agency参数，防止前端参数篡改

### 6. 大小写敏感性分析
**重要发现**：agency字段的比较是**大小写不敏感**的！

#### 数据库层面证据：
```sql
-- agency字段的排序规则
SHOW FULL COLUMNS FROM pay WHERE Field = 'agency';
-- 结果：Collation = 'utf8_general_ci' (ci = Case Insensitive)
```

#### 实际测试验证：
```sql
-- 数据库中存在：
-- id=2, agency='A008'
-- id=3, agency='a008'

-- 以下两个查询都返回相同的两条记录：
SELECT * FROM pay WHERE agency = 'A008';  -- 返回2条记录
SELECT * FROM pay WHERE agency = 'a008';  -- 返回2条记录
```

#### 业务影响：
1. **权限控制**：如果用户的invite是"A008"，系统会匹配到所有"a008"、"A008"等变体
2. **数据统计**：大小写不同但字母相同的代理码会被统计在一起
3. **安全考虑**：需要确保invite字段的唯一性考虑大小写问题

这种设计确保了数据安全性和业务逻辑的正确性，每个用户只能看到自己权限范围内的数据。但需要注意大小写不敏感可能带来的业务逻辑影响。

---

# 支付成功回调后的数据表影响分析

## 概述

当用户支付成功后，第三方支付平台会调用系统的回调接口 `/pay/notifyUrl`，本节详细分析支付成功回调后会影响哪些数据表的哪些字段。

## 支付成功回调处理流程

### 回调接口：GET /pay/notifyUrl

**文件位置**: `src/main/java/com/jeethink/project/system/pay/controller/PayController.java`

```java
@GetMapping("/pay/notifyUrl")
@ResponseBody
public String notifyUrl(String pid, String trade_no, String out_trade_no, String type,
                       String name, String money, String sign, String trade_status, String sign_type) {

    if ("TRADE_SUCCESS".equals(trade_status)) {
        // 1. 验证签名
        Map<String, Object> map = new HashMap<>();
        // ... 构建验签参数
        boolean verify = SignUtils.checkParam(map, type + MD5_KEY, CHARSET);

        if (verify) {
            // 2. 查询支付记录
            Pay pay = payService.selectByNoId(out_trade_no);

            // 3. 更新支付状态和平台订单号
            pay.setStatus("支付成功");
            pay.setWudiOrderNo(trade_no);

            // 4. 保存更新
            payService.updatePay(pay);

            return "SUCCESS";
        }
    }

    return "FAIL";
}
```

## 受影响的数据表和字段

### 1. Pay表 (主要影响表)

**表名**: `pay`

**受影响的字段**:

| 字段名 | 字段类型 | 更新内容 | 说明 |
|--------|----------|----------|------|
| `status` | varchar | "支付成功" | 支付状态从"未支付"更新为"支付成功" |
| `wuDi_order_no` | varchar | 第三方平台订单号 | 设置第三方支付平台返回的订单号 |
| `update_time` | datetime | 当前时间 | 系统自动设置更新时间 |

### 2. 更新操作的具体实现

#### Service层处理
**文件位置**: `src/main/java/com/jeethink/project/system/pay/service/impl/PayServiceImpl.java`

```java
@Override
public int updatePay(Pay pay) {
    // 自动设置更新时间
    pay.setUpdateTime(DateUtils.getNowDate());
    return payMapper.updatePay(pay);
}
```

#### 数据库更新SQL
**文件位置**: `src/main/resources/mybatis/system/PayMapper.xml`

```xml
<update id="updatePay" parameterType="Pay">
    update pay
    <trim prefix="SET" suffixOverrides=",">
        <if test="status != null">status = #{status},</if>
        <if test="wudiOrderNo != null">wuDi_order_no = #{wudiOrderNo},</if>
        <if test="updateTime != null">update_time = #{updateTime},</if>
        <!-- 其他字段... -->
    </trim>
    where id = #{id}
</update>
```

## 重要发现

### 1. 仅影响Pay表
根据代码分析，**支付成功回调后仅直接影响Pay表**，没有其他表的直接更新操作。

### 2. 没有自动触发的后续业务逻辑
- **没有代理分成自动创建**: 虽然系统中存在PaySystem（代理分成）相关功能，但支付成功后不会自动创建代理分成记录
- **没有发货状态更新**: pay表中的`deliver_goods`字段在回调中不会被更新
- **没有异步任务**: 支付成功后没有触发异步任务或定时任务处理其他业务逻辑

### 3. 代理分成功能说明
虽然系统中存在PaySystem（代理分成）功能，但它是**独立的查询和统计功能**：
- PaySystem实际上是对Pay表数据的不同视角查询
- 代理分成数据来源于Pay表，通过`agency`字段关联
- 支付成功后不会自动在PaySystem中创建新记录

## 数据流转图

```
支付成功回调
    ↓
验证签名
    ↓
查询Pay表记录 (根据mch_order_no)
    ↓
更新Pay表字段:
  - status: "未支付" → "支付成功"
  - wuDi_order_no: null → 第三方订单号
  - update_time: 自动更新为当前时间
    ↓
返回SUCCESS给第三方平台
```

## 对首页显示数据的影响

支付成功回调完成后，会影响首页显示的统计数据：

### 1. 收入统计影响
- **月收入**: 如果是当月支付，会增加月收入统计
- **年收入**: 如果是当年支付，会增加年收入统计
- **统计条件**: 只有`status="支付成功"`的记录才会被统计

### 2. 订单统计影响
- **支付成功订单数**: 会增加支付成功订单的统计数量
- **订单总数**: 不受影响（因为订单在支付发起时就已创建）

### 3. 实时性说明
- 首页数据不是实时更新的，需要用户刷新页面才能看到最新统计
- 统计查询基于`create_time`字段，而不是`update_time`

## 总结

**支付成功回调后影响的数据表和字段**：

1. **Pay表** - 唯一受影响的表
   - `status` 字段：更新为"支付成功"
   - `wuDi_order_no` 字段：设置第三方平台订单号
   - `update_time` 字段：自动更新为当前时间

2. **其他表** - 无直接影响
   - 没有其他表会在支付成功回调中被直接修改
   - 代理分成等功能是基于Pay表数据的查询统计，不涉及额外的数据写入

3. **首页统计数据** - 间接影响
   - 支付成功后，首页的收入和支付成功订单统计会发生变化
   - 但需要用户刷新页面才能看到更新后的数据

这种设计保证了支付回调处理的简洁性和可靠性，避免了复杂的业务逻辑可能导致的回调失败。
