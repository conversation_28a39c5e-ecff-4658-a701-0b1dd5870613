三个SQL server的数据库

// 游戏账户数据库
$dbnm['DB'] = 'lin2db';
$host['DB'] = '***********';
$port['DB'] = 1433;
$user['DB'] = 'sa';
$pass['DB'] = 'bgroigmroAD147258JFDJGBHjhdhf';

// 游戏世界数据库
$dbnm['WORLD'] = 'lin2world';
$host['WORLD'] = '***********';
$port['WORLD'] = 1433;
$user['WORLD'] = 'sa';
$pass['WORLD'] = 'bgroigmroAD147258JFDJGBHjhdhf';

这个里面有游戏的名称表：user_data 里面的account_name（账号名称）与char_name（角色名称）是一对多的关系，而且里面有字段，可以用来判断在线时长，在线状态
还要用作支付成功的回调监听，利用char_name找到 account_name，然后设置pay表里面的invite字段

// 网站功能数据库
$dbnm['SITE'] = 'lin2site';
$host['SITE'] = '***********';
$port['SITE'] = 1433;
$user['SITE'] = 'sa';
$pass['SITE'] = 'bgroigmroAD147258JFDJGBHjhdhf';
