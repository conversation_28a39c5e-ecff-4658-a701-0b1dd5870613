<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeethink.project.system.goods.mapper.GoodsMapper">
    
    <resultMap type="Goods" id="GoodsResult">
        <result property="gid"    column="gid"    />
        <result property="gname"    column="gname"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectGoodsVo">
        select gid, gname, status from goods
    </sql>

    <select id="selectGoodsAll" resultMap="GoodsResult">
        <include refid="selectGoodsVo"/>
        <where>
              status='1'
            <if test="gname != null and gname != ''">
                AND gname like concat('%', #{gname}, '%')
            </if>
        </where>

    </select>
    <select id="selectGoodByShId" parameterType="Long" resultMap="GoodsResult">
		SELECT g.gid,g.gname,g.status
		FROM shipments s
			 LEFT JOIN goods g ON s.wpid = g.gid
		WHERE s.sid = #{sid}
	</select>
    <select id="selectGoodsList" parameterType="Goods" resultMap="GoodsResult">
        <include refid="selectGoodsVo"/>
        <where>  
            <if test="gid != null "> and gid = #{gid}</if>
            <if test="gname != null  and gname != ''"> and gname like concat('%', #{gname}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectGoodsById" parameterType="Long" resultMap="GoodsResult">
        <include refid="selectGoodsVo"/>
        where gid = #{gid}
    </select>
        
    <insert id="insertGoods" parameterType="Goods">
        insert into goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="gid != null">gid,</if>
            <if test="gname != null">gname,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="gid != null">#{gid},</if>
            <if test="gname != null">#{gname},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateGoods" parameterType="Goods">
        update goods
        <trim prefix="SET" suffixOverrides=",">
            <if test="gname != null">gname = #{gname},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where gid = #{gid}
    </update>

    <delete id="deleteGoodsById" parameterType="Long">
        delete from goods where gid = #{gid}
    </delete>

    <delete id="deleteGoodsByIds" parameterType="String">
        delete from goods where gid in 
        <foreach item="gid" collection="array" open="(" separator="," close=")">
            #{gid}
        </foreach>
    </delete>

</mapper>