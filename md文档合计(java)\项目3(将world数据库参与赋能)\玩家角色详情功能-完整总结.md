# 玩家角色详情功能 - 完整总结

## 📋 功能概述

### 需求描述
在玩家统计页面的详情按钮基础上，新增显示该玩家账户下所有角色的详细信息，包括角色名称、等级、在线状态、在线时长等。

### 技术栈
- **后端**: Spring Boot + Spring JDBC + SQL Server
- **前端**: jQuery + Bootstrap + Thymeleaf
- **数据库**: MySQL (权限管理) + SQL Server (游戏数据)

## 🗄️ 数据库设计

### 数据源配置
```yaml
# 两个数据库连接
1. MySQL (jeethink) - 权限管理系统
2. SQL Server (lin2world) - 游戏世界数据
   - 主机: 110.42.3.94:1433
   - 数据库: lin2world
   - 表: user_data
```

### 核心数据表
**user_data表 (SQL Server)**
```sql
-- 主要字段
char_id         INT           -- 角色ID
char_name       VARCHAR(50)   -- 角色名称
account_name    VARCHAR(50)   -- 账户名
account_id      INT           -- 账户ID
Lev             INT           -- 等级
class           INT           -- 职业
gender          INT           -- 性别
race            INT           -- 种族
login           DATETIME      -- 最后登录时间
logout          DATETIME      -- 最后登出时间
use_time        INT           -- 在线时长(秒)
create_date     DATETIME      -- 创建时间
```

### 权限配置 (MySQL)
```sql
-- 新增权限菜单
INSERT INTO sys_menu (
    menu_name: '角色查询',
    parent_id: 2060,
    perms: 'system:player:characters',
    menu_type: 'F'
);

-- 权限分配给所有角色
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT role_id, 2070 FROM sys_role WHERE status = '0';
```

## 🔧 后端架构

### 1. 数据源配置层
**SqlServerDataSourceConfig.java**
```java
@Configuration
public class SqlServerDataSourceConfig {
    
    // lin2db数据源 (原有)
    @Bean("lin2dbDataSource")
    public DataSource lin2dbDataSource() { ... }
    
    // lin2world数据源 (新增)
    @Bean("lin2worldDataSource") 
    public DataSource lin2worldDataSource() { ... }
    
    // lin2world JdbcTemplate (新增)
    @Bean("lin2worldJdbcTemplate")
    public JdbcTemplate lin2worldJdbcTemplate() { ... }
}
```

### 2. 实体层 (Domain)
**Character.java** - 角色实体类
```java
public class Character extends BaseEntity {
    private Integer charId;           // 角色ID
    private String charName;          // 角色名称
    private String accountName;       // 账户名
    private Integer level;            // 等级
    private Date login;               // 最后登录
    private Date logout;              // 最后登出
    private Integer useTime;          // 在线时长
    private String onlineStatus;      // 在线状态(计算字段)
    private String useTimeFormatted;  // 格式化时长(计算字段)
    // ... getters/setters
}
```

### 3. 数据访问层 (DAO)
**PlayerDao.java** - 扩展
```java
@Repository
public class PlayerDao {
    
    @Autowired
    @Qualifier("sqlServerJdbcTemplate")
    private JdbcTemplate jdbcTemplate;  // 原有 - lin2db
    
    @Autowired
    @Qualifier("lin2worldJdbcTemplate")
    private JdbcTemplate lin2worldJdbcTemplate;  // 新增 - lin2world
    
    // 角色查询方法
    public List<Character> selectCharactersByAccount(String accountName) {
        String sql = """
            SELECT char_id, char_name, account_name, account_id, Lev, class, 
                   gender, race, login, logout, use_time, create_date,
                   CASE 
                       WHEN login > logout OR logout IS NULL THEN '在线' 
                       ELSE '离线' 
                   END as online_status,
                   CASE 
                       WHEN use_time >= 3600 THEN 
                           CAST(use_time / 3600 AS VARCHAR) + '小时' + 
                           CAST((use_time % 3600) / 60 AS VARCHAR) + '分钟'
                       WHEN use_time >= 60 THEN 
                           CAST(use_time / 60 AS VARCHAR) + '分钟' + 
                           CAST(use_time % 60 AS VARCHAR) + '秒'
                       ELSE CAST(use_time AS VARCHAR) + '秒'
                   END as use_time_formatted
            FROM user_data 
            WHERE account_name = ? 
            ORDER BY 
                CASE WHEN login > logout OR logout IS NULL THEN 1 ELSE 2 END,
                login DESC
            """;
        
        return lin2worldJdbcTemplate.query(sql, new Object[]{accountName}, characterRowMapper);
    }
}
```

### 4. 服务层 (Service)
**IPlayerService.java** - 接口扩展
```java
public interface IPlayerService {
    // 新增方法
    List<Character> selectCharactersByAccount(String accountName);
}
```

**PlayerServiceImpl.java** - 实现
```java
@Service
public class PlayerServiceImpl implements IPlayerService {
    
    @Override
    public List<Character> selectCharactersByAccount(String accountName) {
        return playerDao.selectCharactersByAccount(accountName);
    }
}
```

### 5. 控制器层 (Controller)
**PlayerController.java** - 扩展
```java
@Controller
@RequestMapping("/system/player")
public class PlayerController {
    
    @RequiresPermissions("system:player:detail")
    @GetMapping("/characters/{account}")
    @ResponseBody
    public AjaxResult getCharacters(@PathVariable("account") String account) {
        try {
            // 权限检查
            User currentUser = getSysUser();
            if (!currentUser.getLoginName().equals("admin")) {
                UserAccount player = playerService.selectPlayerByAccount(account);
                if (player == null) {
                    return AjaxResult.error("玩家不存在");
                }
                
                currentUser = userService.selectUserByLoginName(currentUser.getLoginName());
                if (!currentUser.getInvite().equalsIgnoreCase(player.getInvitation())) {
                    return AjaxResult.error("权限不足");
                }
            }
            
            // 查询角色信息
            List<Character> characters = playerService.selectCharactersByAccount(account);
            return AjaxResult.success(characters);
            
        } catch (Exception e) {
            return AjaxResult.error("查询角色信息失败: " + e.getMessage());
        }
    }
}
```

## 🎨 前端架构

### 1. 页面结构
**player.html** - 主页面
```html
<!-- 玩家列表表格 -->
<table id="bootstrap-table"></table>

<!-- 详情按钮 -->
<a class="btn btn-info btn-xs" href="javascript:void(0)" 
   onclick="viewDetail('account')">
   <i class="fa fa-eye"></i>详情
</a>
```

### 2. JavaScript核心逻辑
```javascript
// 查看玩家详情 - 同时获取基本信息和角色信息
function viewDetail(account) {
    $.when(
        // 获取玩家基本信息
        $.ajax({
            type: "get",
            url: prefix + "/detail/" + account,
            dataType: "json"
        }),
        // 获取角色信息
        $.ajax({
            type: "get",
            url: prefix + "/characters/" + account,
            dataType: "json"
        })
    ).done(function(playerRes, charactersRes) {
        var playerData = playerRes[0];
        var charactersData = charactersRes[0];
        
        if (playerData.code === 0 && charactersData.code === 0) {
            var player = playerData.data;
            var characters = charactersData.data;
            
            // 构建HTML内容
            var content = buildPlayerDetailContent(player, characters);
            
            // 显示自定义模态框
            showPlayerDetailModal("玩家详情 - " + account, content);
        }
    });
}

// 自定义模态框函数
function showPlayerDetailModal(title, content) {
    var modalHtml = 
        '<div class="modal fade" id="playerDetailModal">' +
            '<div class="modal-dialog modal-lg">' +
                '<div class="modal-content">' +
                    '<div class="modal-header">' +
                        '<h4 class="modal-title">' + title + '</h4>' +
                        '<button type="button" class="close" data-dismiss="modal">' +
                            '<span>&times;</span>' +
                        '</button>' +
                    '</div>' +
                    '<div class="modal-body">' + content + '</div>' +
                    '<div class="modal-footer">' +
                        '<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>' +
                    '</div>' +
                '</div>' +
            '</div>' +
        '</div>';

    $('#playerDetailModal').remove();
    $('body').append(modalHtml);
    $('#playerDetailModal').modal('show');
}
```

### 3. 界面展示
```html
<!-- 玩家基本信息区域 -->
<h4><i class="fa fa-user"></i> 账户信息</h4>
<div class="row">
    <div class="col-sm-6"><strong>玩家账号：</strong>xxx</div>
    <div class="col-sm-6"><strong>在线状态：</strong>xxx</div>
    <!-- ... 更多基本信息 -->
</div>

<!-- 角色信息区域 -->
<hr>
<h4><i class="fa fa-gamepad"></i> 角色信息 (X个角色)</h4>
<table class="table table-striped">
    <thead>
        <tr>
            <th>角色名称</th>
            <th>等级</th>
            <th>在线状态</th>
            <th>在线时长</th>
            <th>最后登录</th>
        </tr>
    </thead>
    <tbody>
        <!-- 动态生成角色数据 -->
    </tbody>
</table>
```

## 🔐 权限控制

### 权限设计
- **权限标识**: `system:player:characters`
- **权限名称**: 角色查询
- **权限类型**: 功能权限 (F)
- **父权限**: 玩家统计 (menu_id: 2060)

### 权限逻辑
```java
// admin用户：查看所有玩家角色
// 普通用户：只能查看自己邀请码下的玩家角色
if (!currentUser.getLoginName().equals("admin")) {
    UserAccount player = playerService.selectPlayerByAccount(account);
    currentUser = userService.selectUserByLoginName(currentUser.getLoginName());
    if (!currentUser.getInvite().equalsIgnoreCase(player.getInvitation())) {
        return AjaxResult.error("权限不足");
    }
}
```

## 📊 核心SQL查询

### 角色信息查询
```sql
SELECT char_id, char_name, account_name, account_id, Lev, class, gender, race,
       login, logout, use_time, create_date,
       -- 在线状态计算
       CASE 
           WHEN login > logout OR logout IS NULL THEN '在线' 
           ELSE '离线' 
       END as online_status,
       -- 在线时长格式化
       CASE 
           WHEN use_time >= 3600 THEN 
               CAST(use_time / 3600 AS VARCHAR) + '小时' + 
               CAST((use_time % 3600) / 60 AS VARCHAR) + '分钟'
           WHEN use_time >= 60 THEN 
               CAST(use_time / 60 AS VARCHAR) + '分钟' + 
               CAST(use_time % 60 AS VARCHAR) + '秒'
           ELSE CAST(use_time AS VARCHAR) + '秒'
       END as use_time_formatted
FROM user_data 
WHERE account_name = ? 
ORDER BY 
    -- 在线角色优先显示
    CASE WHEN login > logout OR logout IS NULL THEN 1 ELSE 2 END,
    login DESC
```

## 🧪 测试验证

### 单元测试
```java
@Test
public void testSelectCharactersByAccount() {
    List<Character> characters = playerDao.selectCharactersByAccount("tt0011");
    assertNotNull(characters);
    assertTrue(characters.size() > 0);
}
```

### 功能测试
1. **权限测试**: admin用户 vs 普通用户
2. **数据测试**: 多角色账户 vs 无角色账户
3. **界面测试**: 弹窗显示 vs 数据格式化

## 📈 性能优化

### 数据库优化
- 在线状态实时计算，避免存储冗余数据
- 按在线状态和登录时间排序，优先显示活跃角色
- 使用索引优化 account_name 查询

### 前端优化
- 使用 $.when() 并行请求，减少等待时间
- 自定义模态框，避免第三方组件问题
- 简化HTML结构，提高渲染性能

## 🎯 功能特点

### 核心特性
✅ **实时数据**: 角色在线状态和时长都是实时计算  
✅ **权限隔离**: 普通用户只能查看自己邀请码的玩家  
✅ **友好展示**: 在线时长自动格式化为易读格式  
✅ **状态标识**: 在线角色优先显示，状态清晰标识  
✅ **响应式设计**: 支持不同屏幕尺寸的设备  

### 技术亮点
- **多数据源**: 同时连接MySQL和SQL Server
- **复杂SQL**: 实时计算和格式化显示
- **权限控制**: 细粒度的数据访问控制
- **异步请求**: 并行获取多个数据源
- **错误处理**: 完善的异常处理和用户提示

---

**功能版本**: v1.0  
**开发完成**: 2025-07-26  
**状态**: ✅ 已完成并测试通过  
**维护者**: jeethink
