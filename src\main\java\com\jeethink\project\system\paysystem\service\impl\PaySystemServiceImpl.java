package com.jeethink.project.system.paysystem.service.impl;

import java.util.List;
import com.jeethink.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jeethink.project.system.paysystem.mapper.PaySystemMapper;
import com.jeethink.project.system.paysystem.domain.PaySystem;
import com.jeethink.project.system.paysystem.service.IPaySystemService;
import com.jeethink.common.utils.text.Convert;

/**
 * 代理分成Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-11-11
 */
@Service
public class PaySystemServiceImpl implements IPaySystemService 
{
    @Autowired
    private PaySystemMapper paySystemMapper;

    /**
     * 查询代理分成
     * 
     * @param id 代理分成ID
     * @return 代理分成
     */
    @Override
    public PaySystem selectPaySystemById(Long id)
    {
        return paySystemMapper.selectPaySystemById(id);
    }
    /**
     * 查询代理分成成功总金额
     *
     * @param paySystem 代理分成
     * @return 总金额
     */
    @Override
    public Integer selectPaySum(PaySystem paySystem)
    {
        return paySystemMapper.selectPaySum(paySystem);
    }

    /**
     * 查询代理分成图标
     *
     * @param paySystem 代理分成
     * @return 代理分成
     */
    @Override
    public List<PaySystem> selectPayTb(PaySystem paySystem)
    {
        return paySystemMapper.selectPayTb(paySystem);
    }
    /**
     * 查询本月代理分成金额
     *
     * @param paySystem 代理分成
     * @return 代理分成
     */
    @Override
    public List<PaySystem> selectPayAm(PaySystem paySystem)
    {
        return paySystemMapper.selectPayAm(paySystem);
    }
    /**
     * 查询代理分成列表
     * 
     * @param paySystem 代理分成
     * @return 代理分成
     */
    @Override
    public List<PaySystem> selectPaySystemList(PaySystem paySystem)
    {
        return paySystemMapper.selectPaySystemList(paySystem);
    }

    /**
     * 新增代理分成
     * 
     * @param paySystem 代理分成
     * @return 结果
     */
    @Override
    public int insertPaySystem(PaySystem paySystem)
    {
        paySystem.setCreateTime(DateUtils.getNowDate());
        return paySystemMapper.insertPaySystem(paySystem);
    }

    /**
     * 修改代理分成
     * 
     * @param paySystem 代理分成
     * @return 结果
     */
    @Override
    public int updatePaySystem(PaySystem paySystem)
    {
        paySystem.setUpdateTime(DateUtils.getNowDate());
        return paySystemMapper.updatePaySystem(paySystem);
    }

    /**
     * 删除代理分成对象
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deletePaySystemByIds(String ids)
    {
        return paySystemMapper.deletePaySystemByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除代理分成信息
     * 
     * @param id 代理分成ID
     * @return 结果
     */
    @Override
    public int deletePaySystemById(Long id)
    {
        return paySystemMapper.deletePaySystemById(id);
    }
}
