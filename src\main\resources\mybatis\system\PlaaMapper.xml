<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeethink.project.system.plaa.mapper.PlaaMapper">
    
    <resultMap type="Plaa" id="PlaaResult">
        <result property="postId"    column="post_id"    />
        <result property="postCode"    column="post_code"    />
        <result property="postName"    column="post_name"    />
        <result property="postSort"    column="post_sort"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectPlaaVo">
        select post_id, post_code, post_name, post_sort, status, create_by, create_time, update_by, update_time, remark from sys_plaa
    </sql>

    <select id="selectPlaaList" parameterType="Plaa" resultMap="PlaaResult">
        <include refid="selectPlaaVo"/>
        <where>  
            <if test="postCode != null  and postCode != ''"> and post_code = #{postCode}</if>
            <if test="postName != null  and postName != ''"> and post_name like concat('%', #{postName}, '%')</if>
            <if test="postSort != null "> and post_sort = #{postSort}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectPlaaById" parameterType="Long" resultMap="PlaaResult">
        <include refid="selectPlaaVo"/>
        where post_id = #{postId}
    </select>
        
    <insert id="insertPlaa" parameterType="Plaa" useGeneratedKeys="true" keyProperty="postId">
        insert into sys_plaa
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="postCode != null and postCode != ''">post_code,</if>
            <if test="postName != null and postName != ''">post_name,</if>
            <if test="postSort != null">post_sort,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="postCode != null and postCode != ''">#{postCode},</if>
            <if test="postName != null and postName != ''">#{postName},</if>
            <if test="postSort != null">#{postSort},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePlaa" parameterType="Plaa">
        update sys_plaa
        <trim prefix="SET" suffixOverrides=",">
            <if test="postCode != null and postCode != ''">post_code = #{postCode},</if>
            <if test="postName != null and postName != ''">post_name = #{postName},</if>
            <if test="postSort != null">post_sort = #{postSort},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where post_id = #{postId}
    </update>

    <delete id="deletePlaaById" parameterType="Long">
        delete from sys_plaa where post_id = #{postId}
    </delete>

    <delete id="deletePlaaByIds" parameterType="String">
        delete from sys_plaa where post_id in 
        <foreach item="postId" collection="array" open="(" separator="," close=")">
            #{postId}
        </foreach>
    </delete>

</mapper>