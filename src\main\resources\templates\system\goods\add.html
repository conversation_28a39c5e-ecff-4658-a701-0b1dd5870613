<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增物品信息维护')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-goods-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">：</label>
                <div class="col-sm-8">
                    <input name="gid" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">：</label>
                <div class="col-sm-8">
                    <input name="gname" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">1正常使用，0不使用：</label>
                <div class="col-sm-8">
                    <div class="radio-box">
                        <input type="radio" name="status" value="">
                        <label th:for="status" th:text="未知"></label>
                    </div>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 代码生成请选择字典属性</span>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "system/goods"
        $("#form-goods-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-goods-add').serialize());
            }
        }
    </script>
</body>
</html>