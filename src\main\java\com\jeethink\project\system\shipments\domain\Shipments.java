package com.jeethink.project.system.shipments.domain;

import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jeethink.framework.aspectj.lang.annotation.Excel;
import com.jeethink.framework.web.domain.BaseEntity;

/**
 * 发货信息表对象 shipments
 * 
 * <AUTHOR>
 * @date 2024-01-31
 */
public class Shipments extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long sid;

    /** 角色名称 */
    @Excel(name = "角色名称")
    private String sname;

    /** 物品ID */
    @Excel(name = "物品ID")
    private Long wpid;
    /** 物品名称 */
    @Excel(name = "物品名称")
    private String wpname;
    /** 强化值 */
    @Excel(name = "强化值")
    private String qhValue;

    /** 发货数量 */
    @Excel(name = "发货数量")
    private Long snumber;

    /** 发货原因 */
    @Excel(name = "发货原因")
    private String sause;

    /** 发货时间 */
    @Excel(name = "发货时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date stime;

    /** 发货状态 */
    @Excel(name = "发货状态")
    private String status;



    public void setSid(Long sid)
    {
        this.sid = sid;
    }

    public Long getSid()
    {
        return sid;
    }
    public void setSname(String sname)
    {
        this.sname = sname;
    }

    public String getSname()
    {
        return sname;
    }

    public void setWpname(String wpname)
    {
        this.wpname = wpname;
    }

    public String getWpname()
    {
        return wpname;
    }

    public void setWpid(Long wpid)
    {
        this.wpid = wpid;
    }

    public Long getWpid()
    {
        return wpid;
    }
    public void setQhValue(String qhValue)
    {
        this.qhValue = qhValue;
    }

    public String getQhValue()
    {
        return qhValue;
    }
    public void setSnumber(Long snumber)
    {
        this.snumber = snumber;
    }

    public Long getSnumber()
    {
        return snumber;
    }
    public void setSause(String sause)
    {
        this.sause = sause;
    }

    public String getSause()
    {
        return sause;
    }
    public void setStime(Date stime)
    {
        this.stime = stime;
    }

    public Date getStime()
    {
        return stime;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("sid", getSid())
            .append("sname", getSname())
            .append("wpid", getWpid())
            .append("wpname", getWpname())
            .append("qhValue", getQhValue())
            .append("snumber", getSnumber())
            .append("sause", getSause())
            .append("stime", getStime())
            .append("status", getStatus())
            .toString();
    }
}
