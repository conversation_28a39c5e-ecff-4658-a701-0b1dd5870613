package com.jeethink.project.system.plaa.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.jeethink.framework.aspectj.lang.annotation.Log;
import com.jeethink.framework.aspectj.lang.enums.BusinessType;
import com.jeethink.project.system.plaa.domain.Plaa;
import com.jeethink.project.system.plaa.service.IPlaaService;
import com.jeethink.framework.web.controller.BaseController;
import com.jeethink.framework.web.domain.AjaxResult;
import com.jeethink.common.utils.poi.ExcelUtil;
import com.jeethink.framework.web.page.TableDataInfo;

/**
 * 利润分成Controller
 * 
 * <AUTHOR>
 * @date 2023-11-01
 */
@Controller
@RequestMapping("/system/plaa")
public class PlaaController extends BaseController
{
    private String prefix = "system/plaa";

    @Autowired
    private IPlaaService plaaService;

    @RequiresPermissions("system:plaa:view")
    @GetMapping()
    public String plaa()
    {
        return prefix + "/plaa";
    }

    /**
     * 查询利润分成列表
     */
    @RequiresPermissions("system:plaa:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Plaa plaa)
    {
        startPage();
        List<Plaa> list = plaaService.selectPlaaList(plaa);
        return getDataTable(list);
    }

    /**
     * 导出利润分成列表
     */
    @RequiresPermissions("system:plaa:export")
    @Log(title = "利润分成", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Plaa plaa)
    {
        List<Plaa> list = plaaService.selectPlaaList(plaa);
        ExcelUtil<Plaa> util = new ExcelUtil<Plaa>(Plaa.class);
        return util.exportExcel(list, "plaa");
    }

    /**
     * 新增利润分成
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存利润分成
     */
    @RequiresPermissions("system:plaa:add")
    @Log(title = "利润分成", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(Plaa plaa)
    {
        return toAjax(plaaService.insertPlaa(plaa));
    }

    /**
     * 修改利润分成
     */
    @GetMapping("/edit/{postId}")
    public String edit(@PathVariable("postId") Long postId, ModelMap mmap)
    {
        Plaa plaa = plaaService.selectPlaaById(postId);
        mmap.put("plaa", plaa);
        return prefix + "/edit";
    }

    /**
     * 修改保存利润分成
     */
    @RequiresPermissions("system:plaa:edit")
    @Log(title = "利润分成", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(Plaa plaa)
    {
        return toAjax(plaaService.updatePlaa(plaa));
    }

    /**
     * 删除利润分成
     */
    @RequiresPermissions("system:plaa:remove")
    @Log(title = "利润分成", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(plaaService.deletePlaaByIds(ids));
    }
}
