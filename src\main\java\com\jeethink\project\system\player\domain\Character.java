package com.jeethink.project.system.player.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jeethink.framework.aspectj.lang.annotation.Excel;
import com.jeethink.framework.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 角色对象 user_data
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
public class Character extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 角色ID */
    private Integer charId;

    /** 角色名称 */
    @Excel(name = "角色名称")
    private String charName;

    /** 账户名 */
    @Excel(name = "账户名")
    private String accountName;

    /** 账户ID */
    private Integer accountId;

    /** 等级 */
    @Excel(name = "等级")
    private Integer level;

    /** 职业 */
    private Integer charClass;

    /** 性别 */
    private Integer gender;

    /** 种族 */
    private Integer race;

    /** 最后登录时间 */
    @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date login;

    /** 最后登出时间 */
    @Excel(name = "最后登出时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date logout;

    /** 在线时长（秒） */
    @Excel(name = "在线时长")
    private Integer useTime;

    /** 创建时间 */
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /** 在线状态（计算字段，不存储在数据库） */
    @Excel(name = "在线状态")
    private String onlineStatus;

    /** 在线时长格式化（计算字段，不存储在数据库） */
    @Excel(name = "在线时长格式化")
    private String useTimeFormatted;

    public void setCharId(Integer charId) {
        this.charId = charId;
    }

    public Integer getCharId() {
        return charId;
    }

    public void setCharName(String charName) {
        this.charName = charName;
    }

    public String getCharName() {
        return charName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public Integer getAccountId() {
        return accountId;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getLevel() {
        return level;
    }

    public void setCharClass(Integer charClass) {
        this.charClass = charClass;
    }

    public Integer getCharClass() {
        return charClass;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getGender() {
        return gender;
    }

    public void setRace(Integer race) {
        this.race = race;
    }

    public Integer getRace() {
        return race;
    }

    public void setLogin(Date login) {
        this.login = login;
    }

    public Date getLogin() {
        return login;
    }

    public void setLogout(Date logout) {
        this.logout = logout;
    }

    public Date getLogout() {
        return logout;
    }

    public void setUseTime(Integer useTime) {
        this.useTime = useTime;
    }

    public Integer getUseTime() {
        return useTime;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setOnlineStatus(String onlineStatus) {
        this.onlineStatus = onlineStatus;
    }

    public String getOnlineStatus() {
        return onlineStatus;
    }

    public void setUseTimeFormatted(String useTimeFormatted) {
        this.useTimeFormatted = useTimeFormatted;
    }

    public String getUseTimeFormatted() {
        return useTimeFormatted;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("charId", getCharId())
            .append("charName", getCharName())
            .append("accountName", getAccountName())
            .append("accountId", getAccountId())
            .append("level", getLevel())
            .append("charClass", getCharClass())
            .append("gender", getGender())
            .append("race", getRace())
            .append("login", getLogin())
            .append("logout", getLogout())
            .append("useTime", getUseTime())
            .append("createDate", getCreateDate())
            .append("onlineStatus", getOnlineStatus())
            .append("useTimeFormatted", getUseTimeFormatted())
            .toString();
    }
}
