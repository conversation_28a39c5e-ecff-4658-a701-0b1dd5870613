<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">
<title> 《绊脚石》天堂六章首区充值 </title>
<link rel="stylesheet" type="text/css" href="C:/file/js/bootstrap.min.css?v=0" />
<link rel="stylesheet" type="text/css" href="C:/file/js/home.css?v=13" />
<link rel="stylesheet" href="C:/file/js/jquery-dialog.min.css?v=1">
 
<style type="text/css">
@media (min-width: 768px){
.modal-dialog {width: 400px;margin: 30px auto;}
}
</style>

</head>
<body>



<div class="container main-container" id="main-container">

    <div class="main-content con-wrapper" id="main-content">
        <div class="row">
            <div class="col-sm-12 col-md-12">
                <div class="box clearfix">
                    <div class="box-title c-flex flex_cont">
                        <div class="subtitle"><img src="C:/file/js/icon_title_tag.png" style="margin-top: 3px;" /><span>&nbsp;游戏充值</span></div>
                    </div>
                    <div class="box-content clearfix">
					   
                        
    <form name=alipayment action=/pay/save method=post target="_blank" class="form-horizontal con-form" style="margin-left: 20px; margin-right: 20px;">
	
	<div class="form-group ">
							
                                <label class="col-sm-3 col-lg-2 control-label form-title pl-0">商户订单号</label>
                                <div class="col-sm-6 col-lg-5 controls">

<input class="form-control"  size="30" name="mchOrderNo" th:value="${mchOrderNo}" readonly="readonly" style="width:300px"/>
                                     
									   
                                </div>
                            </div>
	
	
	
                <input size="30" type="hidden" name="area" th:value="${area}" readonly="readonly">


              <div class="form-group ">
                                <label class="col-sm-3 col-lg-2 control-label form-title pl-0">角色名称：</label>
                                <div class="col-sm-6 col-lg-5 controls">
		<div class="con-other">
            <input class="form-control" placeholder="请输入游戏角色名(注意:不是账号)" name="goodsParamExt">
			 </div>
                                </div>
                            </div>
							
							
							
              <div class="form-group">
                                <label class="col-sm-3 col-lg-2 control-label form-title pl-0">如输入充值金额：</label>
                                <div class="col-sm-6 col-lg-5 controls">
                                  
                                    <div class="con-other">
		
						   
                                    <input class="form-control" placeholder="请输入你要赞助的金额" id="amount" size="30" name="amount" onkeyup="value=value.replace(/[^\d]/g,'')"/> 
                                      
                                    </div>
                                </div>
                            </div>
							
							
							
							
							 <div class="form-group">
                                <label class="col-sm-3 col-lg-2 control-label form-title pl-0">充值方式：</label>
                                <div class="col-sm-6 col-lg-5 controls">
                                    <div class="con-list con-list-style">
					
<span id="1"><label><input type="radio" name="channelCode" value="ALIPAY" style="width:0px; height:0px;"><em class="icon font-ico">&#40;</em>支付宝</label></span>
<span id="0"><label><input type="radio" name="channelCode" value="WECHAT" checked="" style="width:0px; height:0px;"><em class='icon font-ico'>&#39;</em>微信支付</label></span>				

                  
                                    </div>
                                </div>
                            </div>

				
				
			
 <div class="form-group">
                                <label class="col-sm-3 col-lg-2 control-label form-title"></label>
                                <div class="col-sm-6 col-lg-5 controls" style="margin-top:10px;">
								 
                                    <input type="submit" class="btn btn-primary c-button btn-issue"  value="我要赞助">
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!--底部-->
    <div id="captcha"></div><!-- 验证码容器元素定义 -->
    <script src="C:/file/js/jquery-1.11.3.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="C:/file/js/jquery-dialog.min.js"></script>
    <script src="C:/file/js/base.js?v=6" type="text/javascript" charset="utf-8"></script>
    <script src="C:/file/js/newpublic.js?v=2" type="text/javascript" charset="utf-8"></script>


    <script type="text/javascript">

        function setMenuCur(i) {
            //$('.main-container').find('li').eq(i).addClass("cur");
            $('#pcSidebar li').eq(i).addClass("cur");
            if (i <= 2)
                $(".mobileHeader-menu-detail a").eq(i).addClass("cur");
            else
                $('#mobileSidebar li').eq(i - 3).addClass("cur");
        }
    </script>
    
 
        <script type="text/javascript">
        var token = $('<input name="__RequestVerificationToken" type="hidden" value="CfDJ8M414kGPlRZLqWBlk1s7839ZtX8beFROGUhA_lpqEr25nPHVhk9Fu6E0OQ5hjVlbdeW5RXzwqRXtJdE-7Z7ZuWJUDxiTfxnc770R8ybQQJ6jYrJtwgc_P0JzSttq2iaqpEx4Qnph5jl3BWY6XeZhtlk" />').val();
        var PayType = ""; //选择支付方式
        var money = 0; //充值金额
        var Acct = ""; //卡号
        var confirmPayData = null;

        $(function () {
            $('#myModal').on('show.bs.modal', function () {
                var $this = $(this);
                var $modal_dialog = $this.find('.modal-dialog');
                $this.css('display', 'block');// 关键代码必须设为block
                $modal_dialog.css({ 'margin-top': Math.max(0, ($(window).height() - $modal_dialog.height()) / 2) });
            });

            setMenuCur(2);

            //充值金额
            $(".selectMoney span").bind('click', function () {
                money = $(this).attr("id").replace("money", "");
                $("#amount").val("");
                $("#amount").next().addClass("hide");
                check();
            });
            //支付方式
            $(".con-list-style span").bind('click', function () {
                PayType = $(this).attr("id");
                if (PayType > 10)
                    Acct = $("#hid" + PayType).val();
                check();
            });

            
           $("#amount").focus(function () {
                money = "";
                $(".selectMoney span").siblings().removeClass("cur");
                $("#btnCheckFormSubmit").attr("disabled", "disabled");
            });

            $("#amount").bind('input', function () {
                money = $(this).val();
                check();
                if (checkData(13, money) && parseFloat(money) >= 0)
                    $("#amount").next().addClass("hide");
                else
                    $("#amount").next().removeClass("hide");
            });

            $("#Captcha").bind('focus', function () {
                $(this).css('color', 'black');
            });
        });

      
     
     

        function Send() {
            curCount = count;
            btnId = "btnSendCode";
            $("#" + btnId).val(+curCount + "秒再获取");
            $("#" + btnId).attr("disabled", "true");
            InterValObj = window.setInterval(SetRemainTime, 1000); //启动计时器，1秒执行一次
        }
        </script>
    
    
</body>
</html>