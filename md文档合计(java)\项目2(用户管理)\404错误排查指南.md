# 404错误排查指南

## 🚨 问题描述
点击玩家详情按钮时出现404错误，无法访问角色查询接口。

## 🔍 排查步骤

### 第一步：验证基础路由
1. **测试基础控制器路由**
   ```
   访问: http://localhost:8080/system/player/test
   预期结果: {"code":0,"msg":"PlayerController路由工作正常"}
   ```

2. **如果基础路由不工作**
   - 检查应用程序是否正常启动
   - 查看启动日志是否有错误
   - 确认端口8080是否被占用

### 第二步：检查权限配置
1. **验证权限是否正确分配**
   ```sql
   -- 在MySQL中执行
   SELECT r.role_name, rm.menu_id
   FROM sys_role r
   INNER JOIN sys_role_menu rm ON r.role_id = rm.role_id
   WHERE rm.menu_id = 2070 AND r.status = '0';
   ```

2. **检查当前用户角色**
   - 确认登录用户具有相应权限
   - 如果是admin用户，应该有所有权限

### 第三步：检查前端请求
1. **打开浏览器开发者工具（F12）**
2. **切换到Network标签**
3. **点击详情按钮，观察请求**
   - 请求URL应该是: `/system/player/characters/{account}`
   - 请求方法应该是: GET
   - 状态码: 如果是404，说明路由没找到

### 第四步：检查后端日志
1. **查看控制台输出**
   - 应该看到: "=== 角色查询接口被调用 ==="
   - 如果没有看到，说明请求没有到达控制器

2. **检查错误日志**
   - 查看是否有权限相关错误
   - 查看是否有数据库连接错误

## 🛠️ 修复方案

### 方案1：权限问题修复
如果是权限问题，执行以下SQL：
```sql
-- 确保角色查询权限存在并分配给所有角色
INSERT IGNORE INTO sys_role_menu (role_id, menu_id)
SELECT r.role_id, 2070
FROM sys_role r
WHERE r.status = '0'
  AND NOT EXISTS (
      SELECT 1 FROM sys_role_menu rm
      WHERE rm.role_id = r.role_id AND rm.menu_id = 2070
  );
```

### 方案2：重启应用程序
```bash
# 完全重启Spring Boot应用
# 1. 停止应用
# 2. 清理缓存
# 3. 重新启动
```

### 方案3：检查数据库连接
```java
// 测试lin2world数据库连接
// 运行: src/test/java/com/jeethink/Lin2WorldConnectionTest.java
```

## 🔧 临时解决方案

### 移除权限检查（仅用于测试）
如果需要快速测试功能，可以临时注释掉权限注解：
```java
// @RequiresPermissions("system:player:detail")
@GetMapping("/characters/{account}")
@ResponseBody
public AjaxResult getCharacters(@PathVariable("account") String account) {
    // ... 方法内容
}
```

## 📋 检查清单

### 应用程序状态
- [ ] Spring Boot应用正常启动
- [ ] 没有启动错误日志
- [ ] 端口8080可访问
- [ ] 基础路由 `/system/player/test` 可访问

### 权限配置
- [ ] 角色查询权限（menu_id: 2070）存在
- [ ] 当前用户角色有该权限
- [ ] 权限注解正确配置

### 数据库连接
- [ ] MySQL数据库连接正常
- [ ] SQL Server lin2world数据库连接正常
- [ ] 相关表和数据存在

### 前端配置
- [ ] 请求URL正确
- [ ] 请求方法正确
- [ ] 没有JavaScript错误

## 🎯 预期结果

修复后，点击详情按钮应该：
1. **发送正确的HTTP请求**
2. **后端控制器接收请求**
3. **查询角色数据成功**
4. **返回JSON格式的角色信息**
5. **前端正确显示角色表格**

## 📞 如果问题仍然存在

请提供以下信息：
1. **浏览器Network标签的请求详情**
2. **后端控制台的完整日志**
3. **应用程序启动日志**
4. **当前登录用户的角色信息**

---

**注意**: 当前代码中已添加详细的调试日志，可以帮助快速定位问题所在。
