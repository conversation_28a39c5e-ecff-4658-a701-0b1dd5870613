-- =====================================================
-- 添加角色查询权限 SQL
-- 功能：为玩家统计功能添加角色查询权限
-- 版本：v1.0
-- 日期：2025-07-26
-- 作者：jeethink
-- =====================================================

-- 开始事务，确保数据一致性
START TRANSACTION;

-- =====================================================
-- 第一步：检查前置条件
-- =====================================================

-- 检查是否存在玩家统计主菜单
SELECT '=== 第一步：检查前置条件 ===' as step;

SELECT
    CASE
        WHEN COUNT(*) > 0 THEN '✓ 玩家统计主菜单存在'
        ELSE '✗ 玩家统计主菜单不存在，请先执行 player_menu.sql'
    END as check_result,
    COUNT(*) as menu_count
FROM sys_menu
WHERE menu_name = '玩家统计' AND menu_type = 'C';

-- 检查是否已存在角色查询权限
SELECT
    CASE
        WHEN COUNT(*) = 0 THEN '✓ 角色查询权限不存在，可以添加'
        ELSE '⚠ 角色查询权限已存在，将跳过添加'
    END as check_result,
    COUNT(*) as existing_count
FROM sys_menu
WHERE perms = 'system:player:characters';

-- =====================================================
-- 第二步：获取父菜单ID
-- =====================================================

SELECT '=== 第二步：获取父菜单ID ===' as step;

-- 查找玩家统计主菜单ID
SET @parentId = (SELECT menu_id FROM sys_menu WHERE menu_name = '玩家统计' AND menu_type = 'C' LIMIT 1);

-- 显示找到的父菜单信息
SELECT
    @parentId as parent_menu_id,
    menu_name,
    url,
    perms
FROM sys_menu
WHERE menu_id = @parentId;

-- =====================================================
-- 第三步：添加角色查询权限（如果不存在）
-- =====================================================

SELECT '=== 第三步：添加角色查询权限 ===' as step;

-- 只有在权限不存在且父菜单存在时才添加
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
SELECT '角色查询', @parentId, '10', '#', 'F', '0', 'system:player:characters', '#', 'admin', NOW(), '', NULL, '查询玩家角色信息'
WHERE @parentId IS NOT NULL
  AND NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'system:player:characters');

-- 获取新插入的菜单ID（如果有插入的话）
SET @newMenuId = (SELECT menu_id FROM sys_menu WHERE perms = 'system:player:characters');

-- 显示添加结果
SELECT
    CASE
        WHEN @newMenuId IS NOT NULL THEN '✓ 角色查询权限添加成功'
        ELSE '⚠ 角色查询权限未添加（可能已存在或父菜单不存在）'
    END as add_result,
    @newMenuId as new_menu_id;

-- =====================================================
-- 第四步：为所有角色分配权限
-- =====================================================

SELECT '=== 第四步：为所有角色分配权限 ===' as step;

-- 显示将要分配权限的角色
SELECT
    role_id,
    role_name,
    role_key,
    status
FROM sys_role
WHERE status = '0'
ORDER BY role_id;

-- 为所有现有角色添加这个权限（避免重复添加）
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT r.role_id, @newMenuId
FROM sys_role r
WHERE r.status = '0'
  AND @newMenuId IS NOT NULL
  AND NOT EXISTS (
      SELECT 1 FROM sys_role_menu rm
      WHERE rm.role_id = r.role_id AND rm.menu_id = @newMenuId
  );

-- 显示权限分配结果
SELECT
    COUNT(*) as assigned_roles_count,
    '个角色已分配角色查询权限' as message
FROM sys_role_menu rm
WHERE rm.menu_id = @newMenuId;

-- =====================================================
-- 第五步：验证最终结果
-- =====================================================

SELECT '=== 第五步：验证最终结果 ===' as step;

-- 显示玩家统计模块的完整权限结构
SELECT
    m.menu_id,
    m.menu_name,
    m.parent_id,
    m.order_num,
    m.perms,
    m.menu_type,
    CASE m.visible WHEN '0' THEN '显示' ELSE '隐藏' END as visible_status,
    m.remark
FROM sys_menu m
WHERE m.menu_name = '玩家统计' AND m.menu_type = 'C'
   OR m.parent_id = @parentId
ORDER BY m.parent_id, m.order_num;

-- 显示角色查询权限的角色分配情况
SELECT
    r.role_id,
    r.role_name,
    r.role_key,
    CASE WHEN rm.menu_id IS NOT NULL THEN '✓ 已分配' ELSE '✗ 未分配' END as permission_status
FROM sys_role r
LEFT JOIN sys_role_menu rm ON r.role_id = rm.role_id AND rm.menu_id = @newMenuId
WHERE r.status = '0'
ORDER BY r.role_id;

-- =====================================================
-- 第六步：提交事务和总结
-- =====================================================

SELECT '=== 第六步：执行总结 ===' as step;

-- 最终总结信息
SELECT
    CASE
        WHEN @newMenuId IS NOT NULL THEN '✓ 角色查询权限添加成功'
        ELSE '⚠ 角色查询权限未添加'
    END as final_status,
    @parentId as parent_menu_id,
    @newMenuId as new_menu_id,
    (SELECT COUNT(*) FROM sys_role_menu WHERE menu_id = @newMenuId) as roles_assigned,
    NOW() as execution_time;

-- 提交事务
COMMIT;

SELECT '=== 执行完成 ===' as completion_message;

-- =====================================================
-- 使用说明
-- =====================================================
/*
执行步骤：
1. 确保你已连接到正确的MySQL数据库（jeethink）
2. 直接执行整个脚本文件
3. 查看输出结果，确认每一步都成功执行
4. 如果有错误，脚本会自动回滚

验证方法：
1. 登录系统管理后台
2. 进入系统管理 -> 菜单管理
3. 查找"玩家统计"菜单，确认下面有"角色查询"子菜单
4. 进入系统管理 -> 角色管理
5. 编辑任意角色，确认权限列表中有"角色查询"权限

注意事项：
- 此脚本是幂等的，可以重复执行而不会产生重复数据
- 如果权限已存在，脚本会跳过创建步骤
- 所有操作都在事务中执行，确保数据一致性
*/
