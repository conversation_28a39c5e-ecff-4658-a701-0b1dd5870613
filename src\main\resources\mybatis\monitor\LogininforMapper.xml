<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeethink.project.monitor.logininfor.mapper.LogininforMapper">

	<resultMap type="Logininfor" id="LogininforResult">
		<id     property="infoId"        column="info_id"           />
		<result property="loginName"     column="login_name"        />
		<result property="status"        column="status"            />
		<result property="ipaddr"        column="ipaddr"            />
		<result property="loginLocation" column="login_location"    />
		<result property="browser"       column="browser"           />
		<result property="os"            column="os"                />
		<result property="msg"           column="msg"               />
		<result property="loginTime"     column="login_time"        />
	</resultMap>

	<insert id="insertLogininfor" parameterType="Logininfor">
		insert into sys_logininfor (login_name, status, ipaddr, login_location, browser, os, msg, login_time)
		values (#{loginName}, #{status}, #{ipaddr}, #{loginLocation}, #{browser}, #{os}, #{msg}, sysdate())
	</insert>
	
	<select id="selectLogininforList" parameterType="Logininfor" resultMap="LogininforResult">
		select info_id,login_name,ipaddr,login_location,browser,os,status,msg,login_time from sys_logininfor
		<where>
			<if test="ipaddr != null and ipaddr != ''">
				AND ipaddr like concat('%', #{ipaddr}, '%')
			</if>
			<if test="status != null and status != ''">
				AND status = #{status}
			</if>
			<if test="loginName != null and loginName != ''">
				AND login_name like concat('%', #{loginName}, '%')
			</if>
			<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
				and date_format(login_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
			</if>
			<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
				and date_format(login_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
			</if>
		</where>
	</select>
	
	<delete id="deleteLogininforByIds" parameterType="String">
 		delete from sys_logininfor where info_id in
 		<foreach collection="array" item="infoId" open="(" separator="," close=")">
 			#{infoId}
        </foreach> 
 	</delete>
 	
 	<update id="cleanLogininfor">
        truncate table sys_logininfor
    </update>

</mapper> 