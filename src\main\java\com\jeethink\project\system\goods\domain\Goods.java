package com.jeethink.project.system.goods.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.jeethink.framework.aspectj.lang.annotation.Excel;
import com.jeethink.framework.web.domain.BaseEntity;

/**
 * 物品信息维护对象 goods
 * 
 * <AUTHOR>
 * @date 2024-02-01
 */
public class Goods extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    @Excel(name = "")
    private Long gid;

    /**  */
    @Excel(name = "")
    private String gname;

    /** 1正常使用，0不使用 */
    @Excel(name = "1正常使用，0不使用")
    private String status;

    private boolean  flag;//前台用于是否被选中展示

    public void setGid(Long gid)
    {
        this.gid = gid;
    }

    public Long getGid()
    {
        return gid;
    }

    public void setGname(String gname)
    {
        this.gname = gname;
    }

    public String getGname()
    {
        return gname;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }
    public String getStatus()
    {
        return status;
    }
    public boolean getFlag()
    {
        return flag;
    }
    public void setFlag(boolean flag)
    {
        this.flag = flag;
    }



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("gid", getGid())
            .append("gname", getGname())
            .append("status", getStatus())
            .toString();
    }
}
