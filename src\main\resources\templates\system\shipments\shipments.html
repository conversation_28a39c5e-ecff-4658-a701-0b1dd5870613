<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('发货信息表列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>角色名称：</label>
                                <input type="text" name="sname"/>
                            </li>
                            <li>
                                <label>物品ID：</label>
                                <input type="text" name="wpid"/>
                            </li>
                            <li>
                                <label>物品名称：</label>
                                <input type="text" name="wpname"/>
                            </li>
                            <li>
                                <label>强化值：</label>
                                <input type="text" name="qhValue" />
                            </li>
                            <li>
                                <label>发货数量：</label>
                                <input type="text" name="snumber"/>
                            </li>
                            <li>
                                <label>发货原因：</label>
                                <input type="text" name="sause"/>
                            </li>
                            <li class="select-time">
                                <label>发货时间：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginStime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endStime]"/>
                            </li>
                            <li>
                                <label>发货状态：</label>
                                <select name="status">
                                    <option value="">所有</option>
                                    <option value="成功">成功</option>
                                    <option value="失败">失败</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:shipments:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:shipments:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:shipments:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:shipments:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:shipments:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:shipments:remove')}]];
        var prefix = ctx + "system/shipments";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "发货信息表",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'sid',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'sname',
                    title: '角色名称'
                },
                {
                    field: 'wpid',
                    title: '物品ID'
                },
                 {
                        field: 'wpname',
                        title: '物品名称'
                 },
                {
                    field: 'qhValue',
                    title: '强化值'
                },
                {
                    field: 'snumber',
                    title: '发货数量'
                },
                {
                    field: 'sause',
                    title: '发货原因'
                },
                {
                    field: 'stime',
                    title: '发货时间'
                },
                {
                    field: 'status',
                    title: '发货状态'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.sid + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.sid + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>