<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <title>充值 </title>
    <link href="/css/bootstrap.min.css" rel="stylesheet"/>
    <link rel="stylesheet" type="text/css" href="/css/home.css?v=13"/>
    <link rel="stylesheet" href="/css/jquery-dialog.min.css?v=1">

    <style type="text/css">
        @media (min-width: 768px) {
            .modal-dialog {
                width: 400px;
                margin: 30px auto;
            }
        }
        body {
            background-image: url('/img/bj.jpg'); /* 替换为你的图片路径 */
            background-size: cover; /* 背景图片覆盖整个页面 */
            background-repeat: no-repeat; /* 背景图片不重复 */
            height:100%;
        }
    </style>

</head>
<body >


<div class="container main-container" id="main-container">

    <div class="main-content con-wrapper" id="main-content">
        <div class="row">
            <div class="col-sm-12 col-md-12">
                <div class="box clearfix" style="opacity:0.9">
                    <form onsubmit="return checkform()" name=alipayment action=/pay/save method=post target="_blank" class="form-horizontal con-form"
                          style="margin-left: 20px; margin-right: 20px;" >
                        <div class="box-title c-flex flex_cont">
                            <div class="subtitle"><img src="/css/icon_title_tag.png"
                                                       style="margin-top: 3px;"/><span>&nbsp;充值信息</span></div>
                        </div>
                        <div class="box-content clearfix" style="width: 700px;margin-left: auto;margin-right: auto;">

                            <div class="form-group ">

                                <label class="col-sm-3 col-lg-2 control-label form-title pl-0">商户订单号</label>
                                <div class="col-sm-6 col-lg-5 controls">

                                    <div class="con-other">
                                        <input class="form-control"  name="mchOrderNo" th:value="${mchOrderNo}"
                                               readonly="readonly" />
                                    </div>
                                </div>
                            </div>


                            <input size="30" type="hidden" name="area" th:value="${area}" readonly="readonly" >


                            <div class="form-group ">
                                <label class="col-sm-3 col-lg-2 control-label form-title pl-0">名字：</label>
                                <div class="col-sm-6 col-lg-5 controls">
                                    <div class="con-other">
                                        <input class="form-control" placeholder="请输入游戏角色名(注意:不是账号)"
                                               name="goodsParamExt" required>
                                    </div>
                                </div>
                            </div>


                            <div class="form-group">
                                <label class="col-sm-3 col-lg-2 control-label form-title pl-0">金额：</label>
                                <div class="col-sm-6 col-lg-5 controls">

                                    <div class="con-other">
                                        <input class="form-control" placeholder="请输入你要赞助的金额" id="amount" size="30"
                                               name="amount" onkeyup="value=value.replace(/[^\d]/g,'')" required/>
                                    </div>
                                </div>
                            </div>


                        </div>
                        <div class="box-title c-flex flex_cont">
                            <div class="subtitle"><img src="/css/icon_title_tag.png"
                                                       style="margin-top: 3px;"/><span>&nbsp;充值方式</span>
                            </div>

                        </div>
                        <div class="box-content clearfix" style="width: 700px;
    margin-left: auto;
    margin-right: auto;">
                            <div class="form-group" style="margin-top:30px">
                                <label class="col-sm-3 col-lg-2 control-label form-title pl-0"></label>
                                <div class="col-sm-6 col-lg-5 controls">
                                    <div class="con-list con-list-style">

                                            <span id="1"><label><input id="alipay" type="radio" name="channelCode" value="alipay"
                                                                       style="width:0px; height:0px;"><em
                                                    class="icon font-ico">&#40;</em>支付宝</label></span>
                                        <span style="color: #159c07;" id="0"><label><input type="radio" name="channelCode" value="wxpay"
                                                                  style="width:0px; height:0px;"><em
                                                class='icon font-ico'>&#42;</em>微信</label></span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 col-lg-2 control-label form-title"></label>
                                <div class="col-sm-6 col-lg-5 controls" style="margin-top:10px;">

                                    <input type="submit" class="btn btn-primary c-button btn-issue" value="支付">
                                </div>
                            </div>
                        </div>


                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
        <script>
            function ref(){
                location.reload();
            }
            function checkform(){
                setTimeout(ref, 500);
                return true;
            }
        </script>
        <!--底部-->
        <script src="/css/jquery-1.11.3.min.js" type="text/javascript" charset="utf-8"></script>
        <script src="/css/base.js?v=6" type="text/javascript" charset="utf-8"></script>

</body>

</html>