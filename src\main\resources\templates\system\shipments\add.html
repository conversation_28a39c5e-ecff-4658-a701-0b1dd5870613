<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增发货信息表')" />
    <th:block th:include="include :: select2-css" />
</head>
<body class="white-bg">
    <div class="main-content">
        <form class="form-horizontal m" id="form-shipments-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label">角色名称：</label>
                <div class="col-sm-8">
                    <input name="sname" id= "sname" placeholder="必填项" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">物品名称：</label>
                <div class="col-sm-8">
                    <input name="wuname" id= "wuname" placeholder="物品名称模糊查询" class="form-control" type="text" required>
                </div>
                <a class="btn btn-primary btn-rounded btn-sm" onclick="sousuo()"><i class="fa fa-search"></i>查询</a>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">物品全名称：</label>
                <div class="col-sm-8">
                    <select id="goods" class="form-control select2-multiple"  multiple required>
                    </select>
                </div>

            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">强化值：</label>
                <div class="col-sm-8">
                    <input name="qhValue" class="form-control" type="text" value="0">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">发货数量：</label>
                <div class="col-sm-8">
                    <input name="snumber" placeholder="必填项" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">发货原因：</label>
                <div class="col-sm-8">
                    <input name="sause" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">发货时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="stime" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>

        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: select2-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/shipments"
        $("#form-shipments-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                var data = $("#form-shipments-add").serializeArray();
                var obj=document.getElementById("goods");
                var index = obj.selectedIndex;
                var wpnames = obj.options[index].text;
                var wpname = wpnames.split("-")[1];
                var goodsIds = $.form.selectSelects("goods");
                data.push({"name": "wpid", "value": goodsIds});
                data.push({"name": "wpname", "value": wpname});
                $.operate.save(prefix + "/add", data);
            }
        }
        function sousuo(){
            var wuname=document.getElementById("wuname").value;
            $.ajax({
                type:"post",
                url: prefix + "/listGoods",
                dataType:"json",
                data:{"wpname":wuname},
                success : function(data) {
                    if (data.length>0) {
                        var html="<option>请选择</option>";
                        for (var i = 0; i < data.length; i++) {
                            html+=	'<option  value="' +data[i].gid + '">'+data[i].gname  + '</option>';
                        }
                        $("#goods").empty().append(html);//为select赋值
                        $('#goods').select2({
                            allowClear: true,
                            maximumSelectionLength:1
                        }); //调用select2（）方法

                    }
                }
            });

        }
        $("input[name='stime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

    </script>
</body>
</html>