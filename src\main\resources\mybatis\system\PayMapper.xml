<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeethink.project.system.pay.mapper.PayMapper">
    
    <resultMap type="Pay" id="PayResult">
        <result property="id"    column="id"    />
        <result property="mchOrderNo"    column="mch_order_no"    />
        <result property="channelCode"    column="channel_code"    />
        <result property="amount"    column="amount"    />
        <result property="goodsParamExt"    column="goods_param_ext"    />
        <result property="ip"    column="ip"    />
        <result property="area"    column="area"    />
        <result property="status"    column="status"    />
        <result property="paySuccTime"    column="pay_succ_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="wudiOrderNo"    column="wuDi_order_no"    />
    </resultMap>

    <sql id="selectPayVo">
        select id, mch_order_no, channel_code, amount,area, goods_param_ext, ip, status, pay_succ_time, create_by, create_time, update_by, update_time, wuDi_order_no from pay
    </sql>

    <select id="selectPayList" parameterType="Pay" resultMap="PayResult">
        <include refid="selectPayVo"/>
        <where>  
            <if test="mchOrderNo != null  and mchOrderNo != ''"> and mch_order_no = #{mchOrderNo}</if>
            <if test="channelCode != null  and channelCode != ''"> and channel_code = #{channelCode}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="area != null "> and area = #{area}</if>
            <if test="goodsParamExt != null  and goodsParamExt != ''"> and goods_param_ext = #{goodsParamExt}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="paySuccTime != null "> and pay_succ_time = #{paySuccTime}</if>
            <if test="wudiOrderNo != null  and wudiOrderNo != ''"> and wuDi_order_no = #{wudiOrderNo}</if>
        </where>
    </select>
    
    <select id="selectPayById" parameterType="Long" resultMap="PayResult">
        <include refid="selectPayVo"/>
        where id = #{id}
    </select>

    <select id="selectByNoId" parameterType="String" resultMap="PayResult">
        <include refid="selectPayVo"/>
        where mch_order_no = #{id}
    </select>
        
    <insert id="insertPay" parameterType="Pay" useGeneratedKeys="true" keyProperty="id">
        insert into pay
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mchOrderNo != null">mch_order_no,</if>
            <if test="channelCode != null">channel_code,</if>
            <if test="amount != null">amount,</if>
            <if test="goodsParamExt != null">goods_param_ext,</if>
            <if test="ip != null">ip,</if>
            <if test="area != null">area,</if>
            <if test="status != null">status,</if>
            <if test="paySuccTime != null">pay_succ_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="wudiOrderNo != null">wuDi_order_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mchOrderNo != null">#{mchOrderNo},</if>
            <if test="channelCode != null">#{channelCode},</if>
            <if test="amount != null">#{amount},</if>
            <if test="goodsParamExt != null">#{goodsParamExt},</if>
            <if test="ip != null">#{ip},</if>
            <if test="area != null">#{area},</if>
            <if test="status != null">#{status},</if>
            <if test="paySuccTime != null">#{paySuccTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="wudiOrderNo != null">#{wudiOrderNo},</if>
         </trim>
    </insert>

    <update id="updatePay" parameterType="Pay">
        update pay
        <trim prefix="SET" suffixOverrides=",">
            <if test="mchOrderNo != null">mch_order_no = #{mchOrderNo},</if>
            <if test="channelCode != null">channel_code = #{channelCode},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="goodsParamExt != null">goods_param_ext = #{goodsParamExt},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="area != null">area = #{area},</if>
            <if test="status != null">status = #{status},</if>
            <if test="paySuccTime != null">pay_succ_time = #{paySuccTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="wudiOrderNo != null">wuDi_order_no = #{wudiOrderNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePayById" parameterType="Long">
        delete from pay where id = #{id}
    </delete>

    <delete id="deletePayByIds" parameterType="String">
        delete from pay where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>